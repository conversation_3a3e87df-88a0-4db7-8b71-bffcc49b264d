# 首页数据同步概览改造

## 概述

改造首页的数据同步概览功能，使其能够按照给定的日期范围统计pacs_patient_study表中的真实数据，并添加快捷日期范围选择组件。

## 需求分析

### 原有问题
1. **数据来源不准确**：原来使用`getSyncStatistics()` API，返回的是同步操作的统计（新增和更新数量），而不是按日期范围统计的真实数据
2. **缺少日期范围选择**：用户无法灵活选择统计的时间范围
3. **统计逻辑不符合需求**：应该统计总的pacs_patient_study数和dicom_sync_flag为1的数量

### 改造目标
1. 按照给定的日期范围，统计总的pacs_patient_study数
2. 统计dicom_sync_flag为1（影像数据已同步）的数量
3. 添加快捷日期范围选择UI组件
4. 去掉原有的getSyncStatistics()请求，改用修改后的API

## 技术实现

### 1. 后端API修改

#### 修改PacsSyncController.java
- **文件路径**：`ruoyi-admin/src/main/java/com/ruoyi/datasync/controller/PacsSyncController.java`
- **修改内容**：
  1. 添加`IPacsPatientStudyService`依赖注入（注意是接口类型）
  2. 修改`getSyncStatistics`方法，支持日期范围参数
  3. 使用`pacsPatientStudyService.stat()`方法获取真实的统计数据
  4. 添加异常处理和错误日志记录

```java
@GetMapping("/statistics")
public AjaxResult getSyncStatistics(
        @RequestParam(value = "start", required = false) String start,
        @RequestParam(value = "end", required = false) String end) {
    try {
        // 直接返回pacsPatientStudyService.stat()的统计数据
        Map<String, Object> result = pacsPatientStudyService.stat(start, end);
        return AjaxResult.success(result);
    } catch (Exception e) {
        return AjaxResult.error("获取统计数据失败: " + e.getMessage());
    }
}
```

**返回数据结构**：
```json
{
  "code": 200,
  "data": {
    "studyCount": 1250,           // 总检查数
    "syncedStudyCount": 980,      // 已同步检查数
    "applyCount": 800,            // 申请总数
    "applySyncedCount": 650,      // 已同步申请数
    "applyUnsyncedCount": 150,    // 待同步申请数
    "applyTrend": [...],          // 申请趋势数据
    "syncedTrend": [...],         // 已同步趋势数据
    "unsyncedTrend": [...]        // 待同步趋势数据
  }
}
```

### 2. 前端UI改造

#### 添加快捷日期范围选择组件
在数据同步概览卡片中添加了日期过滤器：

```vue
<!-- 快捷日期范围选择 -->
<div class="sync-date-filter">
  <div class="date-filter-label">统计范围：</div>
  <div class="date-filter-buttons">
    <el-button size="small" :type="syncDateRange === 'today' ? 'primary' : ''" @click="setSyncDateRange('today')">
      今日
    </el-button>
    <el-button size="small" :type="syncDateRange === 'week' ? 'primary' : ''" @click="setSyncDateRange('week')">
      本周
    </el-button>
    <el-button size="small" :type="syncDateRange === 'month' ? 'primary' : ''" @click="setSyncDateRange('month')">
      本月
    </el-button>
    <el-button size="small" :type="syncDateRange === 'quarter' ? 'primary' : ''" @click="setSyncDateRange('quarter')">
      本季度
    </el-button>
    <el-date-picker
      v-model="customSyncDateRange"
      type="daterange"
      size="small"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      format="YYYY-MM-DD"
      value-format="YYYY-MM-DD"
      @change="handleCustomSyncDateChange"
      style="width: 240px; margin-left: 8px;"
    />
  </div>
</div>
```

#### JavaScript逻辑改造
1. **添加新的状态变量**：
   ```javascript
   const syncDateRange = ref('month'); // 默认本月
   const customSyncDateRange = ref([]);
   ```

2. **添加日期范围处理方法**：
   ```javascript
   // 设置数据同步概览的日期范围
   const setSyncDateRange = (range) => {
     // 根据选择的范围计算开始和结束日期
     // 调用getSyncOverviewStatistics获取统计数据
   };
   
   // 处理自定义日期范围变化
   const handleCustomSyncDateChange = (dates) => {
     // 处理用户选择的自定义日期范围
   };
   
   // 获取数据同步概览统计
   const getSyncOverviewStatistics = async (start, end) => {
     const response = await getSyncStatistics(params);
     if (response?.data) {
       const data = response.data;
       studyCount.value = data.studyCount || 0;           // 直接使用studyCount
       syncedStudyCount.value = data.syncedStudyCount || 0; // 直接使用syncedStudyCount
     }
   };
   ```

3. **修改统计数据获取逻辑**：
   - 去掉原有的独立`getSyncStatistics()`调用
   - 使用专门的`getSyncOverviewStatistics`方法获取数据同步概览数据
   - 直接使用`stat`方法返回的`studyCount`和`syncedStudyCount`字段
   - 保持其他统计数据的获取逻辑不变

### 3. API接口修改

#### 修改dashboard/index.js
- **文件路径**：`pacs-admin-ui/src/api/dashboard/index.js`
- **修改内容**：为`getSyncStatistics`函数添加参数支持

```javascript
// 获取数据同步统计
export function getSyncStatistics(params) {
  return request({
    url: '/datasync/pacs/statistics',
    method: 'get',
    params: params
  })
}
```

### 4. 样式设计

#### 日期过滤器样式
- 采用紧凑的设计，放置在卡片头部下方
- 快捷按钮使用小尺寸，选中状态为主色调
- 自定义日期选择器与快捷按钮在同一行
- 支持移动端响应式布局

#### 响应式设计
- **桌面端**：完整显示所有快捷按钮和日期选择器
- **平板端**：适当缩小按钮和字体大小
- **移动端**：进一步优化布局，确保在小屏幕上正常显示

## 数据流程

### 统计数据获取流程
1. **初始化**：页面加载时默认显示本月数据
2. **快捷选择**：用户点击快捷按钮（今日/本周/本月/本季度）
3. **自定义选择**：用户使用日期选择器选择自定义范围
4. **数据获取**：调用后端API获取指定日期范围的统计数据
5. **界面更新**：更新总检查数、已同步数和同步进度

### API调用参数
```javascript
// 调用示例
getSyncStatistics({
  start: '2025-01-01',  // 开始日期
  end: '2025-01-31'     // 结束日期
})
```

### 返回数据结构
```json
{
  "code": 200,
  "data": {
    "studyCount": 1250,           // 总检查数
    "syncedStudyCount": 980,      // 已同步检查数
    "applyCount": 800,            // 申请总数
    "applySyncedCount": 650,      // 已同步申请数
    "applyUnsyncedCount": 150,    // 待同步申请数
    "applyTrend": [...],          // 申请趋势数据
    "syncedTrend": [...],         // 已同步趋势数据
    "unsyncedTrend": [...]        // 待同步趋势数据
  }
}
```

## 功能特性

### 1. 快捷日期范围选择
- **今日**：当天的数据统计
- **本周**：本周一到今天的数据统计
- **本月**：本月1号到今天的数据统计
- **本季度**：本季度第一天到今天的数据统计
- **自定义**：用户自选日期范围

### 2. 实时数据更新
- 选择不同日期范围时立即更新统计数据
- 支持手动刷新功能
- 数据加载时显示loading状态

### 3. 响应式设计
- 适配不同屏幕尺寸
- 移动端优化的触摸体验
- 紧凑的布局设计

## 技术要点

### 1. 数据准确性
- 直接从pacs_patient_study表统计真实数据
- 按check_finish_time字段进行日期范围过滤
- dicom_sync_flag='1'表示已同步状态

### 2. 性能优化
- 使用数据库索引优化查询性能
- 合理的日期范围限制
- 异步数据加载

### 3. 用户体验
- 直观的快捷日期选择
- 清晰的数据展示
- 流畅的交互体验

## 部署说明

### 1. 后端部署
- 重新编译并部署后端服务
- 确保PacsPatientStudyService正常工作

### 2. 前端部署
- 重新构建前端项目
- 由于项目支持热更新，修改后会自动生效

### 3. 测试验证
- 验证不同日期范围的统计数据准确性
- 测试快捷日期选择功能
- 验证移动端响应式效果
