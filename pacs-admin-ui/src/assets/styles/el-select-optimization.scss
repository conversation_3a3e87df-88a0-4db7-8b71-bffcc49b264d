/**
 * Element Plus el-select 组件全局优化样式
 * 解决下拉选择器宽度不足导致占位符显示不全的问题
 */

/* ==================== 基础宽度设置 ==================== */

// 全局 el-select 最小宽度
.el-select {
  min-width: 120px; // 基础最小宽度
  
  // 确保输入框继承最小宽度
  .el-input {
    min-width: inherit;
    
    .el-input__wrapper {
      min-width: inherit;
    }
    
    .el-input__inner {
      min-width: inherit;
    }
  }
}

/* ==================== 场景化宽度设置 ==================== */

// 表单中的 el-select
.el-form-item .el-select {
  min-width: 150px;
}

// 内联表单中的 el-select
.el-form--inline .el-form-item .el-select {
  min-width: 140px;
}

// 搜索栏中的 el-select
.search-bar .el-select,
.filter-section .el-select {
  min-width: 160px;
}

// 对话框中的 el-select
.el-dialog .el-select {
  min-width: 180px;
}

// 表格中的 el-select (适中宽度)
.el-table .el-select {
  min-width: 100px;
}

// 工具栏中的 el-select
.toolbar .el-select,
.el-toolbar .el-select {
  min-width: 140px;
}

/* ==================== 基于占位符内容的智能宽度 ==================== */

// 操作类型选择器
.el-select[placeholder*="操作类型"],
.el-select[placeholder*="请选择操作类型"] {
  min-width: 180px;
}

// 状态选择器
.el-select[placeholder*="状态"],
.el-select[placeholder*="请选择状态"] {
  min-width: 140px;
}

// 检查类型选择器
.el-select[placeholder*="检查类型"],
.el-select[placeholder*="请选择检查类型"] {
  min-width: 160px;
}

// 医院选择器
.el-select[placeholder*="医院"],
.el-select[placeholder*="请选择医院"] {
  min-width: 200px;
}

// 医生选择器
.el-select[placeholder*="医生"],
.el-select[placeholder*="请选择医生"] {
  min-width: 180px;
}

// 部门选择器
.el-select[placeholder*="部门"],
.el-select[placeholder*="请选择部门"] {
  min-width: 160px;
}

// 用户选择器
.el-select[placeholder*="用户"],
.el-select[placeholder*="请选择用户"] {
  min-width: 160px;
}

// 诊断状态选择器
.el-select[placeholder*="诊断状态"],
.el-select[placeholder*="请选择诊断状态"] {
  min-width: 160px;
}

// 审核状态选择器
.el-select[placeholder*="审核状态"],
.el-select[placeholder*="请选择审核状态"] {
  min-width: 160px;
}

// 模态类型选择器
.el-select[placeholder*="模态类型"],
.el-select[placeholder*="请选择模态类型"] {
  min-width: 160px;
}

// 身体部位选择器
.el-select[placeholder*="身体部位"],
.el-select[placeholder*="请选择身体部位"] {
  min-width: 160px;
}

/* ==================== 特殊场景优化 ==================== */

// 紧凑模式下的 el-select
.compact-mode .el-select {
  min-width: 100px;
}

// 宽屏模式下的 el-select
.wide-mode .el-select {
  min-width: 200px;
}

// 移动端适配
@media (max-width: 768px) {
  .el-select {
    min-width: 100px; // 移动端减小最小宽度
    
    .el-form-item & {
      min-width: 120px;
    }
    
    .search-bar &,
    .filter-section & {
      min-width: 120px;
    }
    
    .el-dialog & {
      min-width: 140px;
    }
  }
}

// 平板端适配
@media (min-width: 769px) and (max-width: 1024px) {
  .el-select {
    min-width: 110px;
    
    .el-form-item & {
      min-width: 130px;
    }
  }
}

/* ==================== 下拉选项优化 ==================== */

// 确保下拉选项有足够的宽度显示内容
.el-select-dropdown {
  min-width: 120px;
  
  .el-select-dropdown__item {
    padding: 8px 12px;
    line-height: 1.5;
    min-height: 32px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 多选模式下的优化
.el-select.el-select--multiple {
  min-width: 200px; // 多选需要更大宽度
  
  .el-select__tags {
    max-width: calc(100% - 30px); // 为清除按钮留出空间
  }
}

/* ==================== 特定组件的覆盖样式 ==================== */

// 诊断记录查看器中的选择器
.diagnosis-record-viewer .el-select {
  &[placeholder*="操作类型"] {
    min-width: 180px;
  }
  
  &[placeholder*="状态"] {
    min-width: 140px;
  }
}

// 模板选择器中的选择器
.template-selector .el-select {
  &[placeholder*="检查类型"] {
    min-width: 160px;
  }
}

// 搜索栏中的特殊处理
.search-bar {
  .el-select {
    &[placeholder*="检查类型"] {
      min-width: 160px;
    }
    
    &[placeholder*="诊断状态"] {
      min-width: 160px;
    }
  }
}

/* ==================== 性能优化 ==================== */

// 避免不必要的重绘
.el-select {
  will-change: auto;
}

// 优化动画性能
.el-select-dropdown {
  transform: translateZ(0);
}

/* ==================== 可访问性优化 ==================== */

// 确保焦点状态清晰可见
.el-select:focus-within {
  .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .el-select {
    .el-input__wrapper {
      border-width: 2px;
    }
  }
}

/* ==================== 调试辅助 ==================== */

// 开发环境下显示选择器边界（可选）
.debug-mode .el-select {
  outline: 1px dashed rgba(255, 0, 0, 0.3);
  
  &::before {
    content: attr(placeholder);
    position: absolute;
    top: -20px;
    left: 0;
    font-size: 10px;
    color: #999;
    pointer-events: none;
  }
}
