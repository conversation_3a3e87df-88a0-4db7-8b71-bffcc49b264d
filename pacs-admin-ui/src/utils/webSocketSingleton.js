import { getToken } from '@/utils/auth'

/**
 * 全局唯一的 WebSocket 连接管理器
 * 替代Socket.IO，使用原生WebSocket与后端通信
 */
class GlobalWebSocketManager {
  constructor() {
    if (GlobalWebSocketManager.instance) {
      console.warn('GlobalWebSocketManager 已存在实例，返回现有实例')
      return GlobalWebSocketManager.instance
    }

    this.ws = null
    this.isConnected = false
    this.isConnecting = false
    this.connectionPromise = null
    this.eventBus = new EventTarget()
    this.messageQueue = [] // 离线消息队列
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 10
    this.reconnectTimer = null
    this.heartbeatTimer = null
    this.lastHeartbeat = Date.now()
    
    this.config = {
      url: import.meta.env.VITE_APP_WEBSOCKET_URL || 'ws://localhost:8080/ws/consultation',
      reconnectDelay: 1000,
      maxReconnectDelay: 30000,
      heartbeatInterval: 30000,
      heartbeatTimeout: 90000, // 增加到90秒，更宽松的超时设置
      messageTimeout: 10000,
      // 暂时禁用SockJS，使用原生WebSocket
      useSockJS: false,
      sockjsTransports: ['websocket', 'xhr-polling']
    }

    GlobalWebSocketManager.instance = this
    console.log('GlobalWebSocketManager 单例实例已创建')
  }

  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!GlobalWebSocketManager.instance) {
      new GlobalWebSocketManager()
    }
    return GlobalWebSocketManager.instance
  }

  /**
   * 建立WebSocket连接
   */
  async connect() {
    const connectionId = Date.now() + '-' + Math.random().toString(36).substr(2, 9)
    console.log(`[${connectionId}] 尝试建立 WebSocket 连接`)

    // 严格的状态检查
    if (this.isConnected && this.ws?.readyState === WebSocket.OPEN) {
      console.log(`[${connectionId}] WebSocket 已连接，直接返回`)
      return this.ws
    }

    if (this.isConnecting) {
      console.log(`[${connectionId}] WebSocket 正在连接中，等待完成`)
      return this.connectionPromise
    }

    this.isConnecting = true
    console.log(`[${connectionId}] 开始建立新的 WebSocket 连接`)

    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        const token = getToken()
        if (!token) {
          console.error(`[${connectionId}] 未找到认证 token`)
          this.isConnecting = false
          reject(new Error('未找到认证token'))
          return
        }

        // 清理旧连接
        if (this.ws) {
          console.log(`[${connectionId}] 清理旧的 WebSocket 连接`)
          this.ws.close()
          this.ws = null
        }

        // 构建WebSocket URL - 修复SockJS连接问题
        let wsUrl
        if (this.config.useSockJS) {
          // SockJS需要使用HTTP协议，然后升级为WebSocket
          // 正确的SockJS WebSocket URL格式
          const baseUrl = this.config.url.replace('ws://', 'http://').replace('wss://', 'https://')
          const sessionId = Math.random().toString(36).substr(2, 8)
          const serverNumber = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
          wsUrl = `${baseUrl}/${serverNumber}/${sessionId}/websocket?token=${encodeURIComponent(token)}`
          console.log(`[${connectionId}] 使用SockJS WebSocket URL: ${wsUrl}`)
        } else {
          // 原生WebSocket - 暂时禁用SockJS，直接使用原生WebSocket
          wsUrl = `${this.config.url}?token=${encodeURIComponent(token)}`
          console.log(`[${connectionId}] 使用原生 WebSocket URL: ${wsUrl}`)
        }

        // 临时禁用SockJS，使用原生WebSocket连接
        wsUrl = `${this.config.url}?token=${encodeURIComponent(token)}`
        console.log(`[${connectionId}] 临时使用原生 WebSocket URL: ${wsUrl}`)

        // 创建新连接
        this.ws = new WebSocket(wsUrl)

        // 连接成功处理
        this.ws.onopen = (event) => {
          console.log(`[${connectionId}] WebSocket 连接成功`)
          this.isConnected = true
          this.isConnecting = false
          this.reconnectAttempts = 0
          this.lastHeartbeat = Date.now()
          
          // 启动心跳
          this.startHeartbeat()
          
          // 处理离线消息队列
          this.processMessageQueue()
          
          this.emit('connected', { event })
          resolve(this.ws)
        }

        // 消息处理
        this.ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data)
            console.log(`[${connectionId}] 收到 WebSocket 消息:`, message)
            this.handleMessage(message)
          } catch (error) {
            console.error(`[${connectionId}] 解析 WebSocket 消息失败:`, error, event.data)
          }
        }

        // 连接断开处理
        this.ws.onclose = (event) => {
          console.warn(`[${connectionId}] WebSocket 连接断开:`, event.code, event.reason)
          this.isConnected = false
          this.isConnecting = false
          this.connectionPromise = null
          this.stopHeartbeat()
          
          this.emit('disconnected', { code: event.code, reason: event.reason })
          
          // 自动重连（除非是正常关闭）
          if (event.code !== 1000) {
            this.scheduleReconnect()
          }
        }

        // 连接错误处理
        this.ws.onerror = (error) => {
          console.error(`[${connectionId}] WebSocket 连接错误:`, error)
          this.isConnected = false
          this.isConnecting = false
          this.connectionPromise = null
          this.emit('connect_error', { error })
          reject(error)
        }

      } catch (error) {
        console.error(`[${connectionId}] WebSocket 连接异常:`, error)
        this.isConnecting = false
        this.connectionPromise = null
        reject(error)
      }
    })

    return this.connectionPromise
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(message) {
    const { type, data } = message

    // 更新心跳时间 - 任何消息都表示连接活跃
    this.lastHeartbeat = Date.now()

    switch (type) {
      case 'connected':
        console.log('服务器确认连接成功')
        this.emit('server-connected', data)
        break
      case 'consultation-notification':
        this.emit('consultation-notification', data)
        break
      case 'consultation-broadcast':
        this.emit('consultation-broadcast', data)
        break
      case 'offline-notifications':
        this.emit('offline-notifications', data)
        break
      case 'heartbeat':
      case 'ping':
        this.handleHeartbeat(data)
        break
      case 'pong':
        console.debug('收到pong响应:', data)
        // pong消息表示服务器响应，更新心跳时间
        this.lastHeartbeat = Date.now()
        break
      default:
        console.log('未知消息类型:', type, data)
        this.emit(type, data)
    }
  }

  /**
   * 处理心跳消息
   */
  handleHeartbeat(data) {
    console.debug('收到心跳/ping消息:', data)
    // 发送心跳响应或pong
    this.send({
      type: 'pong',
      data: {
        timestamp: Date.now(),
        serverTimestamp: data?.timestamp
      }
    })
  }

  /**
   * 启动心跳检测
   */
  startHeartbeat() {
    this.stopHeartbeat()
    
    // 初始化心跳时间
    this.lastHeartbeat = Date.now()
    console.log('启动心跳检测，间隔:', this.config.heartbeatInterval, 'ms')

    this.heartbeatTimer = setInterval(() => {
      const now = Date.now()
      const timeSinceLastHeartbeat = now - this.lastHeartbeat
      
      console.debug('心跳检查:', {
        timeSinceLastHeartbeat,
        timeout: this.config.heartbeatTimeout,
        connected: this.isConnected,
        readyState: this.ws?.readyState
      })

      // 如果连接状态正常，定期发送ping消息和检查心跳超时
      if (this.isConnected && this.ws?.readyState === WebSocket.OPEN) {
        // 每个心跳间隔都发送ping消息保持连接活跃
        this.sendPing()

        // 检查是否心跳超时
        if (timeSinceLastHeartbeat > this.config.heartbeatTimeout) {
          console.warn('心跳超时，主动关闭连接触发重连')
          this.ws.close(1006, 'heartbeat timeout')
        }
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * 发送ping消息保持连接活跃
   */
  sendPing() {
    if (this.isConnected && this.ws?.readyState === WebSocket.OPEN) {
      const pingMessage = {
        type: 'ping',
        data: {
          timestamp: Date.now()
        }
      }
      this.send(pingMessage)
      console.debug('发送ping消息保持连接活跃')
    }
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('达到最大重连次数，停止重连')
      this.emit('max_reconnect_attempts_reached')
      return
    }

    const delay = Math.min(
      this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts),
      this.config.maxReconnectDelay
    )

    this.reconnectAttempts++
    console.log(`${delay}ms后进行第${this.reconnectAttempts}次重连`)

    this.reconnectTimer = setTimeout(() => {
      this.connect().catch(error => {
        console.error('重连失败:', error)
      })
    }, delay)
  }

  /**
   * 发送消息
   */
  send(message) {
    if (!this.isConnected || this.ws?.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket未连接，消息加入队列:', message)
      this.messageQueue.push(message)
      return false
    }

    try {
      const jsonMessage = JSON.stringify(message)
      this.ws.send(jsonMessage)
      console.debug('发送WebSocket消息:', message)
      return true
    } catch (error) {
      console.error('发送WebSocket消息失败:', error)
      return false
    }
  }

  /**
   * 处理离线消息队列
   */
  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      this.send(message)
    }
  }

  /**
   * 断开连接
   */
  disconnect() {
    console.log('断开 WebSocket 连接')
    this.isConnecting = false
    this.connectionPromise = null
    this.stopHeartbeat()

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.ws) {
      this.ws.close(1000, 'manual disconnect')
      this.ws = null
    }

    this.isConnected = false
    this.emit('disconnected', { reason: 'manual' })
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 发送事件到事件总线
   */
  emit(eventName, data) {
    this.eventBus.dispatchEvent(new CustomEvent(eventName, { detail: data }))
  }

  /**
   * 监听事件
   */
  on(eventName, handler) {
    this.eventBus.addEventListener(eventName, (event) => {
      handler(event.detail)
    })
  }

  /**
   * 移除事件监听
   */
  off(eventName, handler) {
    this.eventBus.removeEventListener(eventName, handler)
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      readyState: this.ws?.readyState,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length
    }
  }

  /**
   * 发送通知确认
   */
  acknowledgeNotification(notificationId) {
    return this.send({
      type: 'notification-ack',
      data: {
        notificationId,
        timestamp: Date.now()
      }
    })
  }

  /**
   * 请求离线通知
   */
  requestOfflineNotifications() {
    return this.send({
      type: 'request-offline-notifications',
      data: {
        timestamp: Date.now()
      }
    })
  }
}

// 导出单例实例
const globalWebSocket = GlobalWebSocketManager.getInstance()

export default globalWebSocket
