/**
 * 诊断状态常量定义
 * 统一管理诊断相关的状态值、文本和样式
 */

// 诊断状态枚举 - 只保留系统实际使用的4个状态
export const DIAGNOSIS_STATUS = {
  PENDING: '-1',      // 待诊断
  DIAGNOSED: '1',     // 已诊断
  AUDITED: '2',       // 已审核
  HOSPITAL_INTERNAL: '9'  // 院内诊断
}

// 诊断状态文本映射 - 只保留系统实际使用的4个状态
export const DIAGNOSIS_STATUS_TEXT = {
  [DIAGNOSIS_STATUS.PENDING]: '待诊断',
  [DIAGNOSIS_STATUS.DIAGNOSED]: '已诊断',
  [DIAGNOSIS_STATUS.AUDITED]: '已审核',
  [DIAGNOSIS_STATUS.HOSPITAL_INTERNAL]: '院内诊断'
}

// 诊断状态标签类型映射（用于Element Plus的tag组件）- 只保留系统实际使用的4个状态
export const DIAGNOSIS_STATUS_TAG_TYPE = {
  [DIAGNOSIS_STATUS.PENDING]: 'info',
  [DIAGNOSIS_STATUS.DIAGNOSED]: 'primary',
  [DIAGNOSIS_STATUS.AUDITED]: 'success',
  [DIAGNOSIS_STATUS.HOSPITAL_INTERNAL]: 'success'
}

// 诊断按钮文本映射 - 只保留系统实际使用的4个状态
export const DIAGNOSIS_BUTTON_TEXT = {
  [DIAGNOSIS_STATUS.PENDING]: '诊断',
  [DIAGNOSIS_STATUS.DIAGNOSED]: '查看',
  [DIAGNOSIS_STATUS.AUDITED]: '查看',
  [DIAGNOSIS_STATUS.HOSPITAL_INTERNAL]: '查看'
}

// 诊断状态选项列表（用于下拉框）- 只保留系统实际使用的4个状态
export const DIAGNOSIS_STATUS_OPTIONS = [
  { label: '全部', value: '' },
  { label: DIAGNOSIS_STATUS_TEXT[DIAGNOSIS_STATUS.PENDING], value: DIAGNOSIS_STATUS.PENDING },
  { label: DIAGNOSIS_STATUS_TEXT[DIAGNOSIS_STATUS.DIAGNOSED], value: DIAGNOSIS_STATUS.DIAGNOSED },
  { label: DIAGNOSIS_STATUS_TEXT[DIAGNOSIS_STATUS.AUDITED], value: DIAGNOSIS_STATUS.AUDITED },
  { label: DIAGNOSIS_STATUS_TEXT[DIAGNOSIS_STATUS.HOSPITAL_INTERNAL], value: DIAGNOSIS_STATUS.HOSPITAL_INTERNAL }
]

// 工具函数：获取诊断状态文本
export function getDiagnosisStatusText(status) {
  return DIAGNOSIS_STATUS_TEXT[status] || '未知'
}

// 工具函数：获取诊断状态标签类型
export function getDiagnosisStatusTagType(status) {
  return DIAGNOSIS_STATUS_TAG_TYPE[status] || 'info'
}

// 工具函数：获取诊断按钮文本
export function getDiagnosisButtonText(status) {
  return DIAGNOSIS_BUTTON_TEXT[status] || '诊断'
}

// 工具函数：检查状态是否可编辑 - 只有待诊断状态可编辑
export function isDiagnosisEditable(status) {
  return status === DIAGNOSIS_STATUS.PENDING
}

// 工具函数：检查状态是否可审核
export function isDiagnosisAuditable(status) {
  return status === DIAGNOSIS_STATUS.DIAGNOSED
}

// 工具函数：检查状态是否已完成
export function isDiagnosisCompleted(status) {
  return [
    DIAGNOSIS_STATUS.AUDITED,
    DIAGNOSIS_STATUS.HOSPITAL_INTERNAL
  ].includes(status)
}

// 默认导出所有常量
export default {
  DIAGNOSIS_STATUS,
  DIAGNOSIS_STATUS_TEXT,
  DIAGNOSIS_STATUS_TAG_TYPE,
  DIAGNOSIS_BUTTON_TEXT,
  DIAGNOSIS_STATUS_OPTIONS,
  getDiagnosisStatusText,
  getDiagnosisStatusTagType,
  getDiagnosisButtonText,
  isDiagnosisEditable,
  isDiagnosisAuditable,
  isDiagnosisCompleted
}
