/**
 * 诊断状态常量定义
 */

// 诊断状态枚举
export const DIAGNOSIS_STATUS = {
  PENDING: '-1',      // 待诊断
  DIAGNOSED: '1',     // 已诊断
  AUDITED: '2',       // 已审核
  REJECTED: '3',      // 已拒绝
  ARCHIVED: '9'       // 院内诊断
}

// 诊断状态文本映射
export const DIAGNOSIS_STATUS_TEXT = {
  [DIAGNOSIS_STATUS.PENDING]: '待诊断',
  [DIAGNOSIS_STATUS.DIAGNOSED]: '已诊断',
  [DIAGNOSIS_STATUS.AUDITED]: '已审核',
  [DIAGNOSIS_STATUS.REJECTED]: '已拒绝',
  [DIAGNOSIS_STATUS.ARCHIVED]: '院内诊断'
}

// 诊断状态标签类型映射（用于Element Plus Tag组件）
export const DIAGNOSIS_STATUS_TAG_TYPE = {
  [DIAGNOSIS_STATUS.PENDING]: 'info',
  [DIAGNOSIS_STATUS.DIAGNOSED]: 'warning',
  [DIAGNOSIS_STATUS.AUDITED]: 'success',
  [DIAGNOSIS_STATUS.REJECTED]: 'danger',
  [DIAGNOSIS_STATUS.ARCHIVED]: 'primary'
}

// 诊断状态图标映射
export const DIAGNOSIS_STATUS_ICON = {
  [DIAGNOSIS_STATUS.PENDING]: 'Clock',
  [DIAGNOSIS_STATUS.DIAGNOSED]: 'Edit',
  [DIAGNOSIS_STATUS.AUDITED]: 'Check',
  [DIAGNOSIS_STATUS.REJECTED]: 'Close',
  [DIAGNOSIS_STATUS.ARCHIVED]: 'Folder'
}

// 诊断状态颜色映射
export const DIAGNOSIS_STATUS_COLOR = {
  [DIAGNOSIS_STATUS.PENDING]: '#909399',
  [DIAGNOSIS_STATUS.DIAGNOSED]: '#e6a23c',
  [DIAGNOSIS_STATUS.AUDITED]: '#67c23a',
  [DIAGNOSIS_STATUS.REJECTED]: '#f56c6c',
  [DIAGNOSIS_STATUS.ARCHIVED]: '#409eff'
}

// 获取诊断状态文本
export function getDiagnosisStatusText(status) {
  return DIAGNOSIS_STATUS_TEXT[status] || '未知状态'
}

// 获取诊断状态标签类型
export function getDiagnosisStatusTagType(status) {
  return DIAGNOSIS_STATUS_TAG_TYPE[status] || 'info'
}

// 获取诊断状态图标
export function getDiagnosisStatusIcon(status) {
  return DIAGNOSIS_STATUS_ICON[status] || 'QuestionFilled'
}

// 获取诊断状态颜色
export function getDiagnosisStatusColor(status) {
  return DIAGNOSIS_STATUS_COLOR[status] || '#909399'
}

// 检查是否为有效的诊断状态
export function isValidDiagnosisStatus(status) {
  return Object.values(DIAGNOSIS_STATUS).includes(status)
}

// 获取所有诊断状态选项（用于下拉框等）
export function getDiagnosisStatusOptions() {
  return Object.entries(DIAGNOSIS_STATUS_TEXT).map(([value, label]) => ({
    value,
    label
  }))
}

// 诊断状态统计键映射
export const DIAGNOSIS_STATUS_STATS_KEY = {
  [DIAGNOSIS_STATUS.PENDING]: 'pending',
  [DIAGNOSIS_STATUS.DIAGNOSED]: 'diagnosed',
  [DIAGNOSIS_STATUS.AUDITED]: 'audited',
  [DIAGNOSIS_STATUS.REJECTED]: 'rejected',
  [DIAGNOSIS_STATUS.ARCHIVED]: 'archived'
}

// 获取状态统计键
export function getDiagnosisStatusStatsKey(status) {
  return DIAGNOSIS_STATUS_STATS_KEY[status]
}

// 诊断状态变更权限检查
export const DIAGNOSIS_STATUS_PERMISSIONS = {
  // 从待诊断可以变更到
  [DIAGNOSIS_STATUS.PENDING]: [DIAGNOSIS_STATUS.DIAGNOSED, DIAGNOSIS_STATUS.ARCHIVED],
  // 从已诊断可以变更到
  [DIAGNOSIS_STATUS.DIAGNOSED]: [DIAGNOSIS_STATUS.AUDITED, DIAGNOSIS_STATUS.PENDING],
  // 从已审核可以变更到（需要特殊权限）
  [DIAGNOSIS_STATUS.AUDITED]: [DIAGNOSIS_STATUS.PENDING],
  // 院内诊断不可变更
  [DIAGNOSIS_STATUS.ARCHIVED]: []
}

// 检查状态变更是否允许
export function canChangeStatus(fromStatus, toStatus) {
  const allowedStatuses = DIAGNOSIS_STATUS_PERMISSIONS[fromStatus] || []
  return allowedStatuses.includes(toStatus)
}

// 获取可变更的状态列表
export function getAllowedStatusChanges(currentStatus) {
  return DIAGNOSIS_STATUS_PERMISSIONS[currentStatus] || []
}
