/**
 * 诊断场景常量定义
 */

// 诊断场景枚举
export const DIAGNOSIS_SCENARIO = {
  // 诊断医生场景
  DIAGNOSER_PENDING: 'diagnoser_pending',           // 未诊断的检查记录
  DIAGNOSER_DIAGNOSED: 'diagnoser_diagnosed',       // 已诊断检查记录（自己诊断的）
  DIAGNOSER_MY_ALL: 'diagnoser_my_all',            // 自己诊断的检查记录（所有状态）
  DIAGNOSER_AUDITED_PASS: 'diagnoser_audited_pass', // 自己诊断的审核通过记录
  DIAGNOSER_AUDITED_REJECT: 'diagnoser_audited_reject', // 自己诊断但审核没通过记录
  
  // 审核医生场景
  AUDITOR_PENDING: 'auditor_pending',               // 待审核记录
  AUDITOR_MY_AUDITED: 'auditor_my_audited',         // 自己已审核记录
  AUDITOR_MY_REJECTED: 'auditor_my_rejected'        // 自己审核没给通过记录
}

// 诊断场景文本映射
export const DIAGNOSIS_SCENARIO_TEXT = {
  [DIAGNOSIS_SCENARIO.DIAGNOSER_PENDING]: '待诊断',
  [DIAGNOSIS_SCENARIO.DIAGNOSER_DIAGNOSED]: '待审核',
  [DIAGNOSIS_SCENARIO.DIAGNOSER_MY_ALL]: '我的全部诊断',
  [DIAGNOSIS_SCENARIO.DIAGNOSER_AUDITED_PASS]: '我的已审核',
  [DIAGNOSIS_SCENARIO.DIAGNOSER_AUDITED_REJECT]: '审核未通过',
  
  [DIAGNOSIS_SCENARIO.AUDITOR_PENDING]: '待审核',
  [DIAGNOSIS_SCENARIO.AUDITOR_MY_AUDITED]: '已审核',
  [DIAGNOSIS_SCENARIO.AUDITOR_MY_REJECTED]: '拒绝记录'
}

// 诊断医生场景选项
export const DIAGNOSER_SCENARIO_OPTIONS = [
  {
    value: DIAGNOSIS_SCENARIO.DIAGNOSER_PENDING,
    label: DIAGNOSIS_SCENARIO_TEXT[DIAGNOSIS_SCENARIO.DIAGNOSER_PENDING],
    icon: 'Clock',
    color: '#909399'
  },
  {
    value: DIAGNOSIS_SCENARIO.DIAGNOSER_DIAGNOSED,
    label: DIAGNOSIS_SCENARIO_TEXT[DIAGNOSIS_SCENARIO.DIAGNOSER_DIAGNOSED],
    icon: 'Edit',
    color: '#e6a23c'
  },
  {
    value: DIAGNOSIS_SCENARIO.DIAGNOSER_MY_ALL,
    label: DIAGNOSIS_SCENARIO_TEXT[DIAGNOSIS_SCENARIO.DIAGNOSER_MY_ALL],
    icon: 'User',
    color: '#409eff'
  },
  {
    value: DIAGNOSIS_SCENARIO.DIAGNOSER_AUDITED_PASS,
    label: DIAGNOSIS_SCENARIO_TEXT[DIAGNOSIS_SCENARIO.DIAGNOSER_AUDITED_PASS],
    icon: 'Check',
    color: '#67c23a'
  },
  {
    value: DIAGNOSIS_SCENARIO.DIAGNOSER_AUDITED_REJECT,
    label: DIAGNOSIS_SCENARIO_TEXT[DIAGNOSIS_SCENARIO.DIAGNOSER_AUDITED_REJECT],
    icon: 'Close',
    color: '#f56c6c'
  }
]

// 审核医生场景选项
export const AUDITOR_SCENARIO_OPTIONS = [
  {
    value: DIAGNOSIS_SCENARIO.AUDITOR_PENDING,
    label: DIAGNOSIS_SCENARIO_TEXT[DIAGNOSIS_SCENARIO.AUDITOR_PENDING],
    icon: 'Clock',
    color: '#e6a23c'
  },
  {
    value: DIAGNOSIS_SCENARIO.AUDITOR_MY_AUDITED,
    label: DIAGNOSIS_SCENARIO_TEXT[DIAGNOSIS_SCENARIO.AUDITOR_MY_AUDITED],
    icon: 'Check',
    color: '#67c23a'
  },
  {
    value: DIAGNOSIS_SCENARIO.AUDITOR_MY_REJECTED,
    label: DIAGNOSIS_SCENARIO_TEXT[DIAGNOSIS_SCENARIO.AUDITOR_MY_REJECTED],
    icon: 'Close',
    color: '#f56c6c'
  }
]

// 获取场景文本
export function getDiagnosisScenarioText(scenario) {
  return DIAGNOSIS_SCENARIO_TEXT[scenario] || '全部记录'
}

// 根据角色获取场景选项
export function getScenarioOptionsByRole(role) {
  if (role === 'diagnoser') {
    return DIAGNOSER_SCENARIO_OPTIONS
  } else if (role === 'auditor') {
    return AUDITOR_SCENARIO_OPTIONS
  }
  return []
}

// 获取场景对应的图标
export function getScenarioIcon(scenario) {
  const allOptions = [...DIAGNOSER_SCENARIO_OPTIONS, ...AUDITOR_SCENARIO_OPTIONS]
  const option = allOptions.find(opt => opt.value === scenario)
  return option ? option.icon : 'List'
}

// 获取场景对应的颜色
export function getScenarioColor(scenario) {
  const allOptions = [...DIAGNOSER_SCENARIO_OPTIONS, ...AUDITOR_SCENARIO_OPTIONS]
  const option = allOptions.find(opt => opt.value === scenario)
  return option ? option.color : '#909399'
}

// 场景与诊断状态的映射关系
export const SCENARIO_STATUS_MAPPING = {
  [DIAGNOSIS_SCENARIO.DIAGNOSER_PENDING]: ['-1'],
  [DIAGNOSIS_SCENARIO.DIAGNOSER_DIAGNOSED]: ['1'],
  [DIAGNOSIS_SCENARIO.DIAGNOSER_MY_ALL]: ['-1', '1', '2', '9'],
  [DIAGNOSIS_SCENARIO.DIAGNOSER_AUDITED_PASS]: ['2'],
  [DIAGNOSIS_SCENARIO.DIAGNOSER_AUDITED_REJECT]: ['9'],
  
  [DIAGNOSIS_SCENARIO.AUDITOR_PENDING]: ['1'],
  [DIAGNOSIS_SCENARIO.AUDITOR_MY_AUDITED]: ['2'],
  [DIAGNOSIS_SCENARIO.AUDITOR_MY_REJECTED]: ['9']
}

// 获取场景对应的状态列表
export function getScenarioStatuses(scenario) {
  return SCENARIO_STATUS_MAPPING[scenario] || []
}
