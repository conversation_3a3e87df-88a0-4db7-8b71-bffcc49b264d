<template>
  <div class="config-image-upload">
    <el-upload
      :action="uploadUrl"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :on-remove="handleDelete"
      :show-file-list="true"
      :headers="headers"
      :data="uploadData"
      list-type="picture-card"
      :limit="1"
    >
      <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
    </el-upload>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="dialogVisible" title="图片预览" width="800px" append-to-body>
      <img
        :src="dialogImageUrl"
        style="width: 100%; height: auto;"
        alt="预览图片"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ""
  },
  configKey: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

// 上传的图片服务器地址
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + "/system/config/uploadImage");
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const fileList = ref([]);

// 上传请求头
const headers = ref({
  Authorization: "Bearer " + getToken()
});

// 上传数据
const uploadData = computed(() => ({
  configKey: props.configKey
}));

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    fileList.value = [{
      name: '图片',
      url: newVal
    }];
  } else {
    fileList.value = [];
  }
}, { immediate: true });

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (file.type.indexOf("image/") == -1) {
    ElMessage.error("文件格式错误，请上传图片类型，如：JPG，PNG后缀的文件。");
    return false;
  }
  // 校检文件大小
  if (file.size / 1024 / 1024 > 5) {
    ElMessage.error("上传图片大小不能超过 5MB!");
    return false;
  }
  return true;
}

// 文件上传成功处理
function handleUploadSuccess(response, file) {
  if (response.code === 200) {
    emit('update:modelValue', response.msg);
    ElMessage.success("上传成功");
  } else {
    ElMessage.error(response.msg || "上传失败");
  }
}

// 文件上传失败处理
function handleUploadError(error) {
  ElMessage.error("上传失败，请重试");
}

// 删除图片
function handleDelete(file) {
  emit('update:modelValue', "");
}

// 预览图片
function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
}
</script>

<style scoped>
.config-image-upload :deep(.el-upload--picture-card) {
  width: 104px;
  height: 104px;
}
</style>
