<template>
  <div class="reliable-notification">
    <!-- 浮动通知触发器 - 右下角固定位置 -->
    <div class="notification-trigger" @click="toggleNotificationPanel">
      <el-badge :value="activeNotifications.length" :hidden="activeNotifications.length === 0">
        <el-button
            type="primary"
            circle
            size="large"
            :class="{ 'notification-pulse': hasUnreadNotifications }"
        >
          <el-icon>
            <Connection/>
          </el-icon>
        </el-button>
      </el-badge>

      <!-- 连接状态指示器 - 小型 -->
      <div v-if="!isConnected" class="connection-indicator offline">
        <el-icon>
          <WarningFilled/>
        </el-icon>
      </div>
    </div>

    <!-- 侧边通知面板 -->
    <el-drawer
        v-model="showNotificationPanel"
        title="实时通知"
        direction="rtl"
        size="480px"
        :with-header="false"
        class="notification-drawer"
    >
      <!-- 自定义头部 - 更紧凑的设计 -->
      <div class="drawer-header">
        <div class="header-left">
          <el-icon class="header-icon">
            <Bell/>
          </el-icon>
          <span class="header-title">实时通知</span>
          <el-badge
              :value="activeNotifications.length"
              :hidden="activeNotifications.length === 0"
              :max="99"
              class="header-badge"
          />
        </div>
        <div class="header-right">
          <!-- 连接状态指示器 - 紧凑版 -->
          <el-tooltip :content="connectionStatusText" placement="bottom">
            <div class="connection-indicator-compact" :class="connectionStatusClass">
              <el-icon v-if="isConnected">
                <Connection/>
              </el-icon>
              <el-icon v-else>
                <WarningFilled/>
              </el-icon>
            </div>
          </el-tooltip>
          <el-button
              text
              @click="showNotificationPanel = false"
              class="close-button"
          >
            <el-icon>
              <Close/>
            </el-icon>
          </el-button>
        </div>
      </div>

      <!-- 筛选和操作区 -->
      <div class="filter-section">
        <el-select
            v-model="selectedRole"
            placeholder="筛选通知类型"
            @change="handleRoleChange"
            size="small"
            style="flex: 1;"
        >
          <el-option label="全部通知" value="all"/>
          <el-option label="会诊申请" value="consultation-request"/>
          <el-option label="会诊接受" value="consultation-accept"/>
          <el-option label="会诊拒绝" value="consultation-reject"/>
          <el-option label="会诊完成" value="consultation-complete"/>
          <el-option label="会诊取消" value="consultation-cancel"/>
          <el-option label="系统广播" value="system-broadcast"/>
          <el-option label="紧急通知" value="urgent"/>
          <el-option label="审核通知" value="audit"/>
          <el-option label="诊断通知" value="diagnosis"/>
        </el-select>

        <el-button-group class="action-buttons">
          <el-tooltip content="标记全部已读" placement="bottom">
            <el-button
                size="small"
                @click="markAllAsRead"
                :disabled="filteredNotifications.length === 0"
                text
            >
              <el-icon>
                <Check/>
              </el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="重新连接" placement="bottom">
            <el-button
                size="small"
                @click="reconnect"
                :loading="isConnecting"
                text
            >
              <el-icon>
                <Refresh/>
              </el-icon>
            </el-button>
          </el-tooltip>
        </el-button-group>
      </div>

      <!-- 通知列表 -->
      <div class="notification-list-container">

        <div v-if="filteredNotifications.length === 0" class="no-notifications">
          <el-empty description="暂无通知"/>
        </div>

        <ConsultationNotificationCard
            v-for="notification in filteredNotifications"
            :key="notification.id"
            :notification="notification"
            :compact="true"
            @close="handleNotificationClose(notification)"
            @action="handleNotificationAction"
        />
      </div>

      <!-- 操作区域 -->
      <template #footer>
        <div class="notification-actions">
          <el-button @click="markAllAsRead" :disabled="filteredNotifications.length === 0">
            全部已读
          </el-button>
          <el-button @click="requestOfflineNotifications" :loading="isConnecting">
            获取离线通知
          </el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 新消息弹窗 - 更直观的提示 -->
    <el-dialog
        v-model="showNewMessageDialog"
        :title="newMessageDialogTitle"
        width="450px"
        :modal="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        center
        class="new-message-dialog"
        :z-index="3000"
    >
      <div class="new-message-content" v-if="currentNewMessage">
        <div class="message-header">
          <el-icon class="message-icon" :class="getMessageIconClass(currentNewMessage)">
            <component :is="getMessageIcon(currentNewMessage)"/>
          </el-icon>
          <div class="message-info">
            <div class="message-title">{{ currentNewMessage.title }}</div>
            <div class="message-time">{{ formatTime(currentNewMessage.createTime) }}</div>
          </div>
          <el-tag
              :type="getMessageTagType(currentNewMessage)"
              size="small"
              class="message-priority"
          >
            {{ getMessagePriorityText(currentNewMessage) }}
          </el-tag>
        </div>

        <div class="message-body">
          <p class="message-content">{{ currentNewMessage.content }}</p>

          <div v-if="currentNewMessage.patientName || currentNewMessage.studyDescription" class="message-details">
            <div v-if="currentNewMessage.patientName" class="detail-item">
              <span class="detail-label">患者:</span>
              <span class="detail-value">{{ currentNewMessage.patientName }}</span>
            </div>
            <div v-if="currentNewMessage.studyDescription" class="detail-item">
              <span class="detail-label">检查:</span>
              <span class="detail-value">{{ currentNewMessage.studyDescription }}</span>
            </div>
            <div v-if="currentNewMessage.requesterName" class="detail-item">
              <span class="detail-label">申请者:</span>
              <span class="detail-value">{{ currentNewMessage.requesterName }}</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="message-dialog-footer">
          <el-button @click="closeNewMessageDialog" size="small">
            稍后处理
          </el-button>
          <el-button
              v-if="isActionableMessage(currentNewMessage)"
              type="success"
              @click="handleQuickAccept"
              size="small"
          >
            快速接受
          </el-button>
          <el-button
              v-if="isActionableMessage(currentNewMessage)"
              type="warning"
              @click="handleQuickReject"
              size="small"
          >
            快速拒绝
          </el-button>
          <el-button
              type="primary"
              @click="viewMessageDetail"
              size="small"
          >
            查看详情
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 离线通知提示 -->
    <el-dialog
        v-model="showOfflineDialog"
        title="离线通知"
        width="600px"
        :close-on-click-modal="false"
    >
      <div class="offline-notifications">
        <p>您有 {{ offlineNotifications.length }} 条离线通知：</p>
        <div class="notification-list">
          <div
              v-for="notification in offlineNotifications"
              :key="notification.id"
              class="notification-item"
          >
            <div class="notification-header">
              <span class="notification-title">{{ notification.title }}</span>
              <span class="notification-time">{{ formatTime(notification.createTime) }}</span>
            </div>
            <div class="notification-content">{{ notification.content }}</div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="markAllAsRead">全部标记为已读</el-button>
          <el-button type="primary" @click="showOfflineDialog = false">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 通知详情弹窗 -->
    <NotificationDetailDialog
        v-model="showDetailDialog"
        :notification="selectedNotification"
        @accept="handleAcceptFromDialog"
        @reject="handleRejectFromDialog"
    />
  </div>
</template>

<script setup>
import {computed, reactive, ref, onMounted, onUnmounted} from 'vue'
import {ElMessage} from 'element-plus'
import {Bell, Check, Close, Connection, Refresh, WarningFilled} from '@element-plus/icons-vue'
import {
  acknowledgeSocketIONotification,
  connectSocketIONotification,
  onBroadcastNotification,
  onConsultationNotification,
  onHeartbeat,
  onOfflineNotifications,
  requestOfflineNotifications
} from '@/api/consultation/socketNotification'
import {acceptConsultationRequest, rejectConsultationRequest} from '@/api/consultation/consultation'
import ConsultationNotificationCard from './ConsultationNotificationCard.vue'
import NotificationDetailDialog from './NotificationDetailDialog.vue'
// 导入声音文件
import notifySound from '@/assets/notify.mp3'

// 响应式数据
const socket = ref(null)
const isConnected = ref(false)
const isConnecting = ref(false) // 新增：连接状态
const reconnectAttempts = ref(0)
const maxReconnectAttempts = ref(10)
const reconnectInterval = ref(null)
const activeNotifications = ref([])
const offlineNotifications = ref([])
const showOfflineDialog = ref(false)
const showConnectionStatus = ref(false)
const lastHeartbeat = ref(Date.now())
const showDetailDialog = ref(false)
const selectedNotification = ref(null)
const showNewMessageDialog = ref(false) // 新增：新消息弹窗
const newMessageDialogTitle = ref('新消息通知')
const currentNewMessage = ref(null)

// 新增：消息去重和本地存储相关
const processedMessageIds = ref(new Set())
const pendingNotifications = ref([])
const isOnline = ref(navigator.onLine)
const networkStatusInterval = ref(null)

// 新增：UI状态管理
const showNotificationPanel = ref(false)
const selectedRole = ref('all')

// 音频控制状态
const audioState = reactive({
  lastSoundTime: 0, // 上次播放声音时间
  currentPlayingSounds: 0, // 当前正在播放的声音数量
  recentNotifications: [], // 最近收到的通知（用于批量检测）
  pendingSoundRequests: [], // 待播放的声音请求队列
  isProcessingBatch: false // 是否正在处理批量通知
})

// 配置
const config = reactive({
  reconnectDelay: 1000, // 初始重连延迟
  maxReconnectDelay: 30000, // 最大重连延迟
  heartbeatInterval: 30000, // 心跳间隔（与后端保持一致）
  heartbeatTimeout: 120000, // 心跳超时时间（2分钟，更加宽松）
  notificationTimeout: 5000, // 通知显示时间
  ackTimeout: 30000, // 确认超时时间
  enableSound: true, // 启用声音提醒
  enableVibration: true, // 启用震动提醒（移动端）
  // 新增配置
  localStorageKey: 'reliable_notifications_pending', // 本地存储键名
  localStorageExpiry: 24 * 60 * 60 * 1000, // 本地存储过期时间（24小时）
  networkCheckInterval: 5000, // 网络状态检查间隔
  priorityLevels: {
    LOW: 0,
    NORMAL: 1,
    HIGH: 2,
    URGENT: 3
  },
  // 音频防重复播放配置
  soundCooldown: 2000, // 声音冷却时间（毫秒）
  maxSimultaneousSounds: 1, // 同时播放的最大声音数量
  batchNotificationThreshold: 3, // 批量通知阈值
  batchNotificationWindow: 1000, // 批量通知时间窗口（毫秒）
})

// 计算属性
const connectionStatusClass = computed(() => ({
  'status-connected': isConnected.value,
  'status-disconnected': !isConnected.value,
  'status-reconnecting': reconnectAttempts.value > 0 && !isConnected.value
}))

const connectionStatusText = computed(() => {
  if (isConnected.value) {
    return '通知连接正常'
  } else if (reconnectAttempts.value > 0) {
    return `正在重连... (${reconnectAttempts.value}/${maxReconnectAttempts.value})`
  } else {
    return '通知连接断开'
  }
})

const hasUnreadNotifications = computed(() => {
  return activeNotifications.value.some(n => !n.read)
})

const filteredNotifications = computed(() => {
  let notifications = activeNotifications.value
  
  console.log('筛选通知，原始通知数量:', notifications.length)
  console.log('当前选择的角色:', selectedRole.value)

  // 根据选择的角色筛选
  if (selectedRole.value !== 'all') {
    notifications = notifications.filter(notification => {
      const userRole = getCurrentUserRole()

      // 根据通知类型筛选
      switch (selectedRole.value) {
        case 'consultation-request':
          return notification.type === 'REQUEST' || notification.type === 'consultation-request'
        case 'consultation-accept':
          return notification.type === 'ACCEPT' || notification.type === 'consultation-accept'
        case 'consultation-reject':
          return notification.type === 'REJECT' || notification.type === 'consultation-reject'
        case 'consultation-complete':
          return notification.type === 'COMPLETE' || notification.type === 'consultation-complete'
        case 'consultation-cancel':
          return notification.type === 'CANCEL' || notification.type === 'consultation-cancel'
        case 'system-broadcast':
          return notification.type === 'BROADCAST' || notification.type === 'system-broadcast'
        case 'urgent':
          return notification.priority >= config.priorityLevels.URGENT || notification.type === 'URGENT'
        case 'audit':
          return isAuditRelatedNotification(notification, userRole)
        case 'diagnosis':
          return isDiagnosisRelatedNotification(notification, userRole)
        default:
          return true // 如果是未知类型，显示所有
      }
    })
  }

  console.log('筛选后通知数量:', notifications.length)
  return notifications
})

// 新增：UI交互方法
const toggleNotificationPanel = () => {
  showNotificationPanel.value = !showNotificationPanel.value
}

const showNotificationDetail = (notification) => {
  console.log('🔍 查看通知详情:', notification)
  
  selectedNotification.value = notification
  showDetailDialog.value = true
  
  // 查看详情也算作确认消息
  const ackId = notification.notificationId || notification.id
  if (ackId) {
    console.log('查看详情时确认通知:', ackId)
    acknowledgeNotificationReceived(ackId)
    
    // 标记通知为已读状态
    const notificationIndex = activeNotifications.value.findIndex(n =>
      (n.id || n.notificationId) === ackId
    )
    if (notificationIndex > -1) {
      activeNotifications.value[notificationIndex].read = true
      console.log('标记通知为已读:', ackId)
    }
  } else {
    console.warn('查看详情时未找到可用的ID进行确认:', notification)
  }
}

// 新增：处理来自详情对话框的接受和拒绝操作
const handleAcceptFromDialog = async (notification) => {
  try {
    console.log('从对话框接受通知:', notification)

    // 详细调试通知对象结构
    console.log('🔍 通知对象详细信息 (接受):', {
      type: typeof notification,
      keys: Object.keys(notification),
      consultationId: notification.consultationId,
      requestId: notification.requestId,
      businessId: notification.businessId,
      relatedId: notification.relatedId,
      id: notification.id,
      notificationId: notification.notificationId,
      data: notification.data,
      extraData: notification.extraData,
      fullNotification: JSON.stringify(notification, null, 2)
    })
    
    // 调用后端API接受会诊请求
    const consultationId = notification.consultationId || 
                          notification.requestId || 
                          notification.businessId || 
                          notification.relatedId ||
                          notification.data?.consultationId ||
                          notification.data?.id ||
                          notification.extraData?.consultationId ||
                          notification.id
    
    console.log('🔍 会诊ID检测结果 (接受):', {
      consultationId,
      source: consultationId === notification.consultationId ? 'consultationId' :
              consultationId === notification.requestId ? 'requestId' :
              consultationId === notification.businessId ? 'businessId' :
              consultationId === notification.relatedId ? 'relatedId' :
              consultationId === notification.data?.consultationId ? 'data.consultationId' :
              consultationId === notification.data?.id ? 'data.id' :
              consultationId === notification.extraData?.consultationId ? 'extraData.consultationId' :
              consultationId === notification.id ? 'id' : 'unknown'
    })
    
    if (consultationId) {
      const acceptData = { acceptReason: '快速接受' }
      await acceptConsultationRequest(consultationId, acceptData)
      ElMessage.success('已接受会诊请求')
    } else {
      console.error('❌ 未找到会诊ID，无法接受申请')
      console.error('❌ 通知对象完整内容:', JSON.stringify(notification, null, 2))
      ElMessage.error('未找到会诊ID，无法接受申请')
      return
    }

    // 更新通知状态
    updateNotificationStatus(notification.notificationId || notification.id, 'accepted')

    // 关闭详情对话框
    showDetailDialog.value = false
    selectedNotification.value = null

    // 确认通知已处理
    acknowledgeNotificationReceived(notification.notificationId || notification.id)

  } catch (error) {
    console.error('接受会诊请求失败:', error)
    ElMessage.error('接受会诊请求失败: ' + (error.message || '未知错误'))
  }
}

const handleRejectFromDialog = async (notification) => {
  try {
    console.log('从对话框拒绝通知:', notification)

    // 详细调试通知对象结构
    console.log('🔍 通知对象详细信息 (拒绝):', {
      type: typeof notification,
      keys: Object.keys(notification),
      consultationId: notification.consultationId,
      requestId: notification.requestId,
      businessId: notification.businessId,
      relatedId: notification.relatedId,
      id: notification.id,
      notificationId: notification.notificationId,
      data: notification.data,
      extraData: notification.extraData,
      fullNotification: JSON.stringify(notification, null, 2)
    })
    
    // 调用后端API拒绝会诊请求
    const consultationId = notification.consultationId || 
                          notification.requestId || 
                          notification.businessId || 
                          notification.relatedId ||
                          notification.data?.consultationId ||
                          notification.data?.id ||
                          notification.extraData?.consultationId ||
                          notification.id
    
    console.log('🔍 会诊ID检测结果 (拒绝):', {
      consultationId,
      source: consultationId === notification.consultationId ? 'consultationId' :
              consultationId === notification.requestId ? 'requestId' :
              consultationId === notification.businessId ? 'businessId' :
              consultationId === notification.relatedId ? 'relatedId' :
              consultationId === notification.data?.consultationId ? 'data.consultationId' :
              consultationId === notification.data?.id ? 'data.id' :
              consultationId === notification.extraData?.consultationId ? 'extraData.consultationId' :
              consultationId === notification.id ? 'id' : 'unknown'
    })
    
    if (consultationId) {
      const rejectData = { rejectReason: '快速拒绝' }
      await rejectConsultationRequest(consultationId, rejectData)
      ElMessage.success('已拒绝会诊请求')
    } else {
      console.error('❌ 未找到会诊ID，无法拒绝申请')
      console.error('❌ 通知对象完整内容:', JSON.stringify(notification, null, 2))
      ElMessage.error('未找到会诊ID，无法拒绝申请')
      return
    }

    // 更新通知状态
    updateNotificationStatus(notification.notificationId || notification.id, 'rejected')

    // 关闭详情对话框
    showDetailDialog.value = false
    selectedNotification.value = null

    // 确认通知已处理
    acknowledgeNotificationReceived(notification.notificationId || notification.id)

  } catch (error) {
    console.error('拒绝会诊请求失败:', error)
    ElMessage.error('拒绝会诊请求失败: ' + (error.message || '未知错误'))
  }
}

// 更新通知状态的辅助方法
const updateNotificationStatus = (notificationId, status) => {
  const notification = activeNotifications.value.find(n =>
      (n.notificationId || n.id) === notificationId
  )
  if (notification) {
    notification.status = status
    notification.read = true
    notification.processedAt = new Date().toISOString()
  }
}

const handleRoleChange = (role) => {
  console.log('角色筛选变更:', role)
  // 可以在这里添加额外的筛选逻辑或数据加载
}

const reconnect = () => {
  disconnect()
  setTimeout(connectToNotificationService, 1000)
}

// 断开连接函数
const disconnect = () => {
  console.log('断开 WebSocket 连接')
  
  // 心跳由WebSocket单例处理，无需在这里停止
  
  // 停止重连
  if (reconnectInterval.value) {
    clearTimeout(reconnectInterval.value)
    reconnectInterval.value = null
  }
  
  // 断开 WebSocket 连接
  if (socket.value) {
    try {
      socket.value.disconnect()
    } catch (error) {
      console.warn('断开 WebSocket 连接时出错:', error)
    }
    socket.value = null
  }
  
  // 更新状态
  isConnected.value = false
  isConnecting.value = false
  reconnectAttempts.value = 0
  showConnectionStatus.value = true
}

// 新增：获取当前用户角色
const getCurrentUserRole = () => {
  // 从用户信息或权限中获取角色
  // 这里需要根据实际的用户权限系统实现
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  return {
    canAudit: userInfo.roles?.includes('auditor') || userInfo.permissions?.includes('consultation:audit'),
    canDiagnose: userInfo.roles?.includes('diagnostician') || userInfo.permissions?.includes('consultation:diagnose'),
    userId: userInfo.userId,
    auditBy: userInfo.userId // audit_by字段对应的用户ID
  }
}

// 新增：判断是否为审核相关通知
const isAuditRelatedNotification = (notification, userRole) => {
  // 检查通知是否与当前用户的审核权限相关
  if (!userRole.canAudit) return false

  // 检查是否指定了特定的审核者
  if (notification.auditBy && notification.auditBy !== userRole.auditBy) {
    return false
  }

  // 检查通知类型是否需要审核
  const auditTypes = ['REQUEST', 'PENDING_AUDIT', 'AUDIT_REQUIRED']
  if (notification.type && auditTypes.includes(notification.type)) {
    return true
  }

  // 检查通知内容是否包含审核关键词
  const auditKeywords = ['审核', '待审', '需要审核', '审批']
  const content = `${notification.title || ''} ${notification.content || ''}`.toLowerCase()
  return auditKeywords.some(keyword => content.includes(keyword))
}

// 新增：判断是否为诊断相关通知
const isDiagnosisRelatedNotification = (notification, userRole) => {
  // 检查通知是否与当前用户的诊断权限相关
  if (!userRole.canDiagnose) return false

  // 检查是否指定了特定的诊断者
  if (notification.diagnosisBy && notification.diagnosisBy !== userRole.userId) {
    return false
  }

  // 检查通知类型是否需要诊断
  const diagnosisTypes = ['DIAGNOSIS_REQUEST', 'PENDING_DIAGNOSIS', 'DIAGNOSIS_REQUIRED']
  if (notification.type && diagnosisTypes.includes(notification.type)) {
    return true
  }

  // 检查通知内容是否包含诊断关键词
  const diagnosisKeywords = ['诊断', '阅片', '会诊', '需要诊断']
  const content = `${notification.title || ''} ${notification.content || ''}`.toLowerCase()
  return diagnosisKeywords.some(keyword => content.includes(keyword))
}

// 连接到通知服务
const connectToNotificationService = async () => {
  // 防止重复连接
  if (isConnected.value || isConnecting.value) {
    console.log('Socket.IO连接中或已连接，跳过重复连接请求')
    return
  }

  try {
    isConnecting.value = true
    console.log('建立Socket.IO通知连接...')

    // 加载本地存储的待处理通知
    loadPendingNotificationsFromStorage()

    // 建立Socket.IO连接
    socket.value = await connectSocketIONotification()

    // 注册事件监听器
    onConsultationNotification(handleNotification)
    onBroadcastNotification(handleBroadcast)
    onOfflineNotifications(handleOfflineNotifications)
    onHeartbeat(handleHeartbeat)

    // 监听WebSocket连接状态变化
    setupConnectionStatusListeners()

    // 连接成功处理
    handleConnected()

    // 请求离线通知
    requestOfflineNotifications()

  } catch (error) {
    console.error('建立通知连接失败:', error)
    ElMessage.error('建立通知连接失败')
    isConnected.value = false
    scheduleReconnect()
  } finally {
    isConnecting.value = false
  }
}

// 新增：设置连接状态监听器
const setupConnectionStatusListeners = () => {
  console.log('设置WebSocket连接状态监听器')

  // 监听WebSocket事件
  if (socket.value && typeof socket.value.on === 'function') {
    console.log('注册WebSocket事件监听器')

    // 连接成功事件
    socket.value.on('connected', (data) => {
      console.log('收到WebSocket连接成功事件:', data)
      isConnected.value = true
      showConnectionStatus.value = false
      reconnectAttempts.value = 0
    })

    // 断开连接事件
    socket.value.on('disconnected', (data) => {
      console.log('收到WebSocket断开连接事件:', data)
      isConnected.value = false
      showConnectionStatus.value = true
    })

    // 连接错误事件
    socket.value.on('connect_error', (data) => {
      console.log('收到WebSocket连接错误事件:', data)
      isConnected.value = false
      showConnectionStatus.value = true
    })
  }

  // 简化的连接状态检查 - 每5秒检查一次
  const statusCheckInterval = setInterval(() => {
    try {
      if (socket.value && typeof socket.value.getStatus === 'function') {
        const status = socket.value.getStatus()
        const actuallyConnected = status.isConnected && status.readyState === 1 // WebSocket.OPEN = 1

        // 只在状态发生变化时更新UI
        if (isConnected.value !== actuallyConnected) {
          console.log('WebSocket连接状态变化:', isConnected.value, '->', actuallyConnected)
          isConnected.value = actuallyConnected
          showConnectionStatus.value = !actuallyConnected
        }
      } else {
        // 如果 socket 不存在或无法获取状态，标记为未连接
        if (isConnected.value) {
          console.warn('WebSocket实例不可用，标记为未连接')
          isConnected.value = false
          showConnectionStatus.value = true
        }
      }
    } catch (error) {
      console.error('检查WebSocket状态失败:', error)
      if (isConnected.value) {
        isConnected.value = false
        showConnectionStatus.value = true
      }
    }
  }, 5000) // 每5秒检查一次

  // 返回清理函数
  return () => {
    if (statusCheckInterval) {
      clearInterval(statusCheckInterval)
    }
  }
}

// 处理连接成功
const handleConnected = () => {
  console.log('Socket.IO通知连接已建立')
  isConnected.value = true
  reconnectAttempts.value = 0
  showConnectionStatus.value = false
  //ElMessage.success('通知连接已建立')
}

// 处理Socket.IO通知
const handleNotification = (notification) => {
  try {
    console.log('收到Socket.IO通知:', notification)

    // 消息去重检查
    if (isDuplicateMessage(notification)) {
      console.log('重复消息，已忽略:', notification.notificationId)
      return
    }

    // 标记消息为已处理
    markMessageAsProcessed(notification)

    // 添加到待处理列表并保存到本地存储
    addToPendingNotifications(notification)

    // 显示通知
    showNotification(notification)

    // 发送确认
    if (notification.notificationId) {
      acknowledgeNotificationReceived(notification.notificationId)
    }

    // 播放提示音（根据优先级，带批量检测和防重复逻辑）
    if (config.enableSound) {
      handleNotificationSound(notification)
    }

    // 震动提醒（移动端，根据优先级）
    if (config.enableVibration && 'vibrate' in navigator) {
      const vibrationPattern = getVibrationPattern(notification.priority)
      navigator.vibrate(vibrationPattern)
    }

    // 新消息弹窗提示 - 添加更多触发条件和调试日志
    console.log('检查是否显示新消息弹窗，通知类型:', notification.type)
    if (notification.type === 'REQUEST' || 
        notification.type === 'PENDING_AUDIT' || 
        notification.type === 'consultation-request' ||
        notification.type === 'consultation-notification') {
      console.log('显示新消息弹窗，通知内容:', notification)
      currentNewMessage.value = notification
      newMessageDialogTitle.value = '新会诊申请通知'
      showNewMessageDialog.value = true
    } else {
      console.log('通知类型不匹配，不显示弹窗。通知类型:', notification.type)
    }

  } catch (error) {
    console.error('处理Socket.IO通知失败:', error)
  }
}

// 处理Socket.IO广播通知
const handleBroadcast = (notification) => {
  try {
    console.log('收到Socket.IO广播通知:', notification)
    showNotification(notification)
  } catch (error) {
    console.error('处理Socket.IO广播通知失败:', error)
  }
}

// 处理心跳 - 简化逻辑，心跳由WebSocket单例自动处理
const handleHeartbeat = (data) => {
  try {
    const now = Date.now()
    lastHeartbeat.value = now
    console.debug('收到心跳:', data, '时间:', new Date(now).toLocaleTimeString())
    // 心跳响应由WebSocket单例自动处理，这里只更新UI状态
  } catch (error) {
    console.error('处理心跳失败:', error)
  }
}

// 处理Socket.IO离线通知
const handleOfflineNotifications = (notifications) => {
  try {
    console.log('收到Socket.IO离线通知:', notifications)
    if (notifications && notifications.length > 0) {
      offlineNotifications.value = notifications
      showOfflineDialog.value = true
      console.log('收到离线通知:', notifications.length, '条')
    }
  } catch (error) {
    console.error('处理离线通知失败:', error)
  }
}

// 处理Socket.IO连接错误
const handleConnectionError = (error) => {
  console.error('Socket.IO连接错误:', error)
  isConnected.value = false
  showConnectionStatus.value = true

  // Socket.IO有内置重连机制，这里主要更新UI状态
  ElMessage.warning('通知连接断开，正在尝试重连...')
}

// 处理连接打开
const handleConnectionOpen = (event) => {
  console.log('通知连接已打开:', event)
  isConnected.value = true
}

// 显示通知
const showNotification = (notification) => {
  console.log('开始显示通知:', notification)
  
  const priority = notification.priority || config.priorityLevels.NORMAL

  // 转换通知数据格式以适配ConsultationNotificationCard
  const notificationConfig = {
    id: notification.notificationId || Date.now(),
    notificationId: notification.notificationId,
    title: notification.title || '新通知',
    content: notification.content || '您有新的通知消息',
    type: notification.type,
    priority: priority,
    urgencyLevel: getUrgencyLevel(priority),
    createTime: notification.createTime || Date.now(),
    patientName: notification.patientName,
    studyDescription: notification.studyDescription,
    requestingDoctor: notification.requestingDoctor,
    requestNo: notification.requestNo,
    requesterName: notification.requesterName,
    url: notification.url,
    read: false, // 标记为未读
    // 确保所有可能包含会诊申请ID的字段都被传递
    consultationId: notification.consultationId,
    requestId: notification.requestId,
    businessId: notification.businessId,
    relatedId: notification.relatedId,
    data: notification.data,
    // 传递原始通知对象的所有属性，以防遗漏重要字段
    ...notification
  }

  console.log('转换后的通知配置:', notificationConfig)
  console.log('通知中的会诊ID字段检查:', {
    consultationId: notification.consultationId,
    requestId: notification.requestId,
    businessId: notification.businessId,
    relatedId: notification.relatedId,
    id: notification.id,
    notificationId: notification.notificationId,
    originalNotification: notification
  })

  // 添加到活动通知列表
  activeNotifications.value.push(notificationConfig)
  console.log('当前活动通知数量:', activeNotifications.value.length)
  console.log('所有活动通知:', activeNotifications.value)

  // 根据通知状态决定是否自动关闭
  // 只有待确认的会诊申请通知不自动关闭，其他类型的通知可以自动关闭
  const shouldAutoClose = !isPendingConfirmationNotification(notificationConfig)
  console.log('是否自动关闭:', shouldAutoClose)

  if (shouldAutoClose && !isUrgentNotification(notificationConfig)) {
    const delay = getAutoCloseDelay(notificationConfig)
    console.log('自动关闭延迟时间:', delay)
    if (delay > 0) {
      setTimeout(() => {
        console.log('自动关闭通知:', notificationConfig.id)
        handleNotificationClose(notificationConfig)
      }, delay)
    }
  }
}

// 获取通知类型
const getNotificationType = (type, priority = config.priorityLevels.NORMAL) => {
  // 紧急消息统一使用error类型
  if (priority >= config.priorityLevels.URGENT) {
    return 'error'
  }

  const typeMap = {
    'REQUEST': 'info',
    'ACCEPT': 'success',
    'REJECT': 'warning',
    'COMPLETE': 'success',
    'CANCEL': 'warning',
    'URGENT': 'error'
  }
  return typeMap[type] || 'info'
}

// 处理通知点击（保留兼容性，但主要逻辑已移到handleNotificationView）
const handleNotificationClick = (notification) => {
  console.log('通知被点击:', notification)
  handleNotificationView(notification)
}

// 处理通知关闭
const handleNotificationClose = (notification) => {
  console.log('🔔 处理通知关闭:', notification)
  
  const notificationId = notification.id || notification.notificationId
  const index = activeNotifications.value.findIndex(n =>
      (n.id || n.notificationId) === notificationId
  )

  if (index > -1) {
    activeNotifications.value.splice(index, 1)
    console.log('从活动通知列表中移除通知:', notificationId)
  }

  // 从待处理列表中移除并更新本地存储
  removeFromPendingNotifications(notificationId)

  // 确认通知（优先使用 notificationId，如果没有则使用 id）
  const ackId = notification.notificationId || notification.id
  if (ackId) {
    console.log('确认通知已处理:', ackId)
    acknowledgeNotificationReceived(ackId)
  } else {
    console.warn('通知关闭时未找到可用的ID进行确认:', notification)
  }
}

// 消息去重检查
const isDuplicateMessage = (notification) => {
  const messageId = notification.notificationId || notification.id
  if (!messageId) return false
  return processedMessageIds.value.has(messageId.toString())
}

// 标记消息为已处理
const markMessageAsProcessed = (notification) => {
  const messageId = notification.notificationId || notification.id
  if (messageId) {
    processedMessageIds.value.add(messageId.toString())

    // 限制Set大小，避免内存泄漏
    if (processedMessageIds.value.size > 1000) {
      const firstItem = processedMessageIds.value.values().next().value
      processedMessageIds.value.delete(firstItem)
    }
  }
}

// 添加到待处理通知列表
const addToPendingNotifications = (notification) => {
  const pendingNotification = {
    ...notification,
    timestamp: Date.now(),
    priority: notification.priority || config.priorityLevels.NORMAL
  }

  // 根据优先级插入到合适位置
  const insertIndex = findInsertIndex(pendingNotification)
  pendingNotifications.value.splice(insertIndex, 0, pendingNotification)

  // 保存到本地存储
  savePendingNotificationsToStorage()
}

// 从待处理通知列表中移除
const removeFromPendingNotifications = (notificationId) => {
  const index = pendingNotifications.value.findIndex(n =>
      (n.notificationId || n.id) === notificationId
  )
  if (index > -1) {
    pendingNotifications.value.splice(index, 1)
    savePendingNotificationsToStorage()
  }
}

// 根据优先级找到插入位置
const findInsertIndex = (notification) => {
  const priority = notification.priority || config.priorityLevels.NORMAL

  for (let i = 0; i < pendingNotifications.value.length; i++) {
    const existingPriority = pendingNotifications.value[i].priority || config.priorityLevels.NORMAL
    if (priority > existingPriority) {
      return i
    }
  }

  return pendingNotifications.value.length
}

// 确认Socket.IO通知已收到
const acknowledgeNotificationReceived = async (notificationId) => {
  try {
    // 使用Socket.IO发送确认
    const success = acknowledgeSocketIONotification(notificationId)
    if (success) {
      console.log('Socket.IO通知确认成功:', notificationId)
    } else {
      console.warn('Socket.IO通知确认失败：连接不可用')
    }

    // 从待处理列表中移除
    removeFromPendingNotifications(notificationId)
  } catch (error) {
    console.error('通知确认失败:', notificationId, error)
  }
}

// 保存待处理通知到本地存储
const savePendingNotificationsToStorage = () => {
  try {
    const data = {
      notifications: pendingNotifications.value,
      timestamp: Date.now()
    }
    localStorage.setItem(config.localStorageKey, JSON.stringify(data))
  } catch (error) {
    console.warn('保存通知到本地存储失败:', error)
  }
}

// 从本地存储加载待处理通知
const loadPendingNotificationsFromStorage = () => {
  try {
    const stored = localStorage.getItem(config.localStorageKey)
    if (!stored) return

    const data = JSON.parse(stored)
    const now = Date.now()

    // 检查数据是否过期
    if (now - data.timestamp > config.localStorageExpiry) {
      localStorage.removeItem(config.localStorageKey)
      return
    }

    // 过滤掉过期的通知
    const validNotifications = data.notifications.filter(notification => {
      const notificationAge = now - (notification.timestamp || 0)
      return notificationAge < config.localStorageExpiry
    })

    pendingNotifications.value = validNotifications

    // 重新显示未确认的通知
    validNotifications.forEach(notification => {
      if (!isDuplicateMessage(notification)) {
        showNotification(notification)
        markMessageAsProcessed(notification)
      }
    })

    console.log('从本地存储恢复通知:', validNotifications.length, '条')
  } catch (error) {
    console.warn('从本地存储加载通知失败:', error)
    localStorage.removeItem(config.localStorageKey)
  }
}

// 清理本地存储
const cleanupLocalStorage = () => {
  try {
    const stored = localStorage.getItem(config.localStorageKey)
    if (!stored) return

    const data = JSON.parse(stored)
    const now = Date.now()

    // 清理过期数据
    if (now - data.timestamp > config.localStorageExpiry) {
      localStorage.removeItem(config.localStorageKey)
      return
    }

    // 清理过期通知
    const validNotifications = data.notifications.filter(notification => {
      const notificationAge = now - (notification.timestamp || 0)
      return notificationAge < config.localStorageExpiry
    })

    if (validNotifications.length !== data.notifications.length) {
      pendingNotifications.value = validNotifications
      savePendingNotificationsToStorage()
    }
  } catch (error) {
    console.warn('清理本地存储失败:', error)
  }
}

// requestOfflineNotifications 已从 socketNotification API 中导入

// 标记所有通知为已读
const markAllAsRead = async () => {
  try {
    let processedCount = 0
    
    // 处理离线通知
    for (const notification of offlineNotifications.value) {
      if (notification.notificationId) {
        await acknowledgeNotificationReceived(notification.notificationId)
        processedCount++
      }
    }
    
    // 处理当前活动通知
    for (const notification of activeNotifications.value) {
      if (notification.notificationId) {
        await acknowledgeNotificationReceived(notification.notificationId)
        processedCount++
      }
    }
    
    // 处理待处理通知
    for (const notification of pendingNotifications.value) {
      if (notification.notificationId) {
        await acknowledgeNotificationReceived(notification.notificationId)
        processedCount++
      }
    }
    
    console.log(`标记所有通知为已读，共处理 ${processedCount} 条通知`)
    
    // 清空所有通知列表
    offlineNotifications.value = []
    activeNotifications.value = []
    pendingNotifications.value = []
    
    // 更新本地存储
    savePendingNotificationsToStorage()
    
    // 关闭相关对话框
    showOfflineDialog.value = false
    showNewMessageDialog.value = false
    
    ElMessage.success(`所有通知已标记为已读 (${processedCount} 条)`)
    
  } catch (error) {
    console.error('标记通知为已读失败:', error)
    ElMessage.error('标记通知为已读失败: ' + (error.message || '未知错误'))
  }
}

// 安排重连
const scheduleReconnect = () => {
  if (reconnectAttempts.value >= maxReconnectAttempts.value) {
    console.error('达到最大重连次数，停止重连')
    ElMessage.error('通知连接失败，请刷新页面重试')
    return
  }

  // 根据网络状态调整重连策略
  let delay = Math.min(
      config.reconnectDelay * Math.pow(2, reconnectAttempts.value),
      config.maxReconnectDelay
  )

  // 如果网络离线，延长重连间隔
  if (!isOnline.value) {
    delay = Math.max(delay, 10000) // 至少10秒
  }

  reconnectAttempts.value++
  console.log(`${delay}ms后进行第${reconnectAttempts.value}次重连`)

  reconnectInterval.value = setTimeout(() => {
    // 重连前再次检查网络状态
    if (isOnline.value) {
      connectToNotificationService()
    } else {
      console.log('网络离线，延迟重连')
      scheduleReconnect()
    }
  }, delay)
}

// 网络状态监听
const setupNetworkStatusMonitoring = () => {
  // 监听网络状态变化
  window.addEventListener('online', handleNetworkOnline)
  window.addEventListener('offline', handleNetworkOffline)

  // 定期检查网络状态
  networkStatusInterval.value = setInterval(() => {
    const currentOnlineStatus = navigator.onLine
    if (currentOnlineStatus !== isOnline.value) {
      isOnline.value = currentOnlineStatus
      console.log('网络状态变化:', currentOnlineStatus ? '在线' : '离线')
    }
  }, config.networkCheckInterval)
}

// 网络恢复处理
const handleNetworkOnline = () => {
  console.log('网络已恢复')
  isOnline.value = true

  // 如果连接断开，立即尝试重连
  if (!isConnected.value) {
    console.log('网络恢复，立即尝试重连')
    reconnectAttempts.value = 0 // 重置重连次数
    connectToNotificationService()
  }
}

// 网络断开处理
const handleNetworkOffline = () => {
  console.log('网络已断开')
  isOnline.value = false
  showConnectionStatus.value = true
}

// 心跳检查由WebSocket单例自动处理，此处保留状态更新
const startHeartbeat = () => {
  // 心跳检查功能已移至WebSocket单例，这里只记录日志
  console.log('心跳检查由WebSocket单例自动处理')
  lastHeartbeat.value = Date.now()
}

// ==================== 音频优化控制 ====================

/**
 * 处理通知声音（带批量检测和防重复逻辑）
 */
const handleNotificationSound = (notification) => {
  const currentTime = Date.now()
  const priority = notification.priority || config.priorityLevels.NORMAL

  // 如果正在处理批量通知，跳过新的音频播放
  if (audioState.isProcessingBatch) {
    console.log('正在处理批量通知，跳过当前通知的音频播放')
    return
  }

  // 添加到最近通知列表用于批量检测
  audioState.recentNotifications.push({
    time: currentTime,
    priority: priority,
    id: notification.notificationId || notification.id
  })

  // 清理过期的通知记录
  audioState.recentNotifications = audioState.recentNotifications.filter(
    n => currentTime - n.time < config.batchNotificationWindow
  )

  // 检测是否为批量通知
  if (audioState.recentNotifications.length >= config.batchNotificationThreshold) {
    handleBatchNotificationSound()
    return
  }

  // 单个通知的音频处理
  handleSingleNotificationSound(priority)
}

/**
 * 处理批量通知音频
 */
const handleBatchNotificationSound = () => {
  if (audioState.isProcessingBatch) {
    console.log('正在处理批量通知音频，跳过新的批量请求')
    return
  }

  audioState.isProcessingBatch = true
  console.log(`检测到批量通知 (${audioState.recentNotifications.length} 条)，播放批量提示音`)

  // 获取最高优先级
  const highestPriority = Math.max(...audioState.recentNotifications.map(n => n.priority))

  // 播放批量通知音效（只播放一次）
  playBatchNotificationSound(highestPriority)

  // 更新最后播放时间，防止后续单个通知立即播放
  audioState.lastSoundTime = Date.now()

  // 清理批量标记和通知记录
  setTimeout(() => {
    audioState.isProcessingBatch = false
    audioState.recentNotifications = []
    console.log('批量通知音频处理完成，重置状态')
  }, config.soundCooldown)
}

/**
 * 处理单个通知音频
 */
const handleSingleNotificationSound = (priority) => {
  const currentTime = Date.now()
  
  // 检查声音冷却时间
  if (currentTime - audioState.lastSoundTime < config.soundCooldown) {
    console.log(`声音冷却中，跳过播放 (剩余: ${config.soundCooldown - (currentTime - audioState.lastSoundTime)}ms)`)
    return
  }
  
  // 检查同时播放的声音数量限制
  if (audioState.currentPlayingSounds >= config.maxSimultaneousSounds) {
    console.log(`达到同时播放声音数量限制 (${config.maxSimultaneousSounds})，跳过播放`)
    return
  }
  
  // 播放声音
  playNotificationSound(priority)
  audioState.lastSoundTime = currentTime
}

/**
 * 播放批量通知专用音效
 */
const playBatchNotificationSound = (priority) => {
  if (!config.enableSound) {
    return
  }

  try {
    console.log('播放批量通知音效，优先级:', priority)

    // 优先使用音频文件，失败时才使用生成的提示音
    const audio = new Audio(notifySound)
    audio.volume = Math.min(0.8, getVolumeByPriority(priority) + 0.2) // 稍微增加音量
    audio.playbackRate = 1.2 // 稍微加快播放速度

    audioState.currentPlayingSounds++

    const playPromise = audio.play()
    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          console.log('批量通知音效播放成功')
        })
        .catch(error => {
          console.warn('批量通知音效播放失败，使用备用提示音:', error)
          // 只有在音频文件播放失败时才使用生成的提示音
          generateBatchNotificationBeep(priority)
        })
        .finally(() => {
          audioState.currentPlayingSounds = Math.max(0, audioState.currentPlayingSounds - 1)
        })
    }

  } catch (error) {
    console.warn('创建批量通知音频对象失败，使用备用提示音:', error)
    // 备用方案：使用Web Audio API生成提示音
    generateBatchNotificationBeep(priority)
  }
}

/**
 * 生成批量通知专用提示音
 */
const generateBatchNotificationBeep = (priority) => {
  try {
    const AudioContext = window.AudioContext || window.webkitAudioContext
    if (!AudioContext) {
      console.warn('浏览器不支持Web Audio API')
      return
    }

    const audioContext = new AudioContext()
    
    // 批量通知使用三声短促的提示音
    const frequencies = [800, 1000, 800] // 低-高-低的音调组合
    const duration = 150 // 每个音的持续时间
    const gap = 100 // 音与音之间的间隔
    const volume = getVolumeByPriority(priority)

    frequencies.forEach((frequency, index) => {
      const startTime = audioContext.currentTime + index * (duration + gap) / 1000
      
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.frequency.setValueAtTime(frequency, startTime)
      oscillator.type = 'sine'

      // 音量包络
      gainNode.gain.setValueAtTime(0, startTime)
      gainNode.gain.linearRampToValueAtTime(volume, startTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration / 1000)

      oscillator.start(startTime)
      oscillator.stop(startTime + duration / 1000)
    })

    console.log('批量通知专用提示音生成成功')
    
  } catch (error) {
    console.warn('生成批量通知提示音失败:', error)
  }
}

/**
 * 根据优先级获取音量
 */
const getVolumeByPriority = (priority) => {
  switch (priority) {
    case config.priorityLevels.URGENT:
      return 0.8
    case config.priorityLevels.HIGH:
      return 0.7
    case config.priorityLevels.LOW:
      return 0.3
    default:
      return 0.5
  }
}

/**
 * 重置音频状态
 */
const resetAudioState = () => {
  audioState.lastSoundTime = 0
  audioState.currentPlayingSounds = 0
  audioState.recentNotifications = []
  audioState.pendingSoundRequests = []
  audioState.isProcessingBatch = false
  console.log('音频状态已重置')
}

/**
 * 获取音频状态统计
 */
const getAudioStatistics = () => {
  return {
    lastSoundTime: audioState.lastSoundTime,
    currentPlayingSounds: audioState.currentPlayingSounds,
    recentNotificationsCount: audioState.recentNotifications.length,
    isProcessingBatch: audioState.isProcessingBatch,
    soundCooldownRemaining: Math.max(0, config.soundCooldown - (Date.now() - audioState.lastSoundTime))
  }
}

/**
 * 测试音频功能（开发调试用）
 */
const testAudioFeatures = () => {
  console.log('=== 音频功能测试开始 ===')
  console.log('当前音频配置:', {
    enableSound: config.enableSound,
    soundCooldown: config.soundCooldown,
    maxSimultaneousSounds: config.maxSimultaneousSounds,
    batchThreshold: config.batchNotificationThreshold,
    batchWindow: config.batchNotificationWindow
  })
  
  // 测试单个通知
  console.log('测试单个通知音频...')
  handleNotificationSound({ priority: config.priorityLevels.NORMAL, id: 'test-1' })
  
  // 延迟测试批量通知
  setTimeout(() => {
    console.log('测试批量通知音频...')
    for (let i = 0; i < 4; i++) {
      handleNotificationSound({ 
        priority: config.priorityLevels.HIGH, 
        id: `test-batch-${i}` 
      })
    }
  }, 1000)
  
  // 显示音频状态
  setTimeout(() => {
    console.log('音频状态统计:', getAudioStatistics())
    console.log('=== 音频功能测试结束 ===')
  }, 3000)
}

/**
 * 显示消息确认状态总结（开发调试用）
 */
const checkNotificationAcknowledgmentStatus = () => {
  console.log('=== 消息确认状态检查 ===')
  console.log('消息确认逻辑覆盖的场景:')
  console.log('1. ✅ 通知卡片关闭按钮 -> handleNotificationClose -> acknowledgeNotificationReceived')
  console.log('2. ✅ 新消息弹窗关闭 -> closeNewMessageDialog -> acknowledgeNotificationReceived') 
  console.log('3. ✅ 自动关闭通知 -> handleNotificationClose -> acknowledgeNotificationReceived')
  console.log('4. ✅ 快速接受操作 -> handleAcceptFromDialog -> acknowledgeNotificationReceived')
  console.log('5. ✅ 快速拒绝操作 -> handleRejectFromDialog -> acknowledgeNotificationReceived')
  console.log('6. ✅ 标记全部已读 -> markAllAsRead -> acknowledgeNotificationReceived (所有通知)')
  console.log('7. ✅ 查看详情 -> showNotificationDetail -> acknowledgeNotificationReceived (已修改)')
  
  console.log('\n当前状态:')
  console.log('- 活动通知数量:', activeNotifications.value.length)
  console.log('- 离线通知数量:', offlineNotifications.value.length)
  console.log('- 待处理通知数量:', pendingNotifications.value.length)
  console.log('- 已处理消息ID数量:', processedMessageIds.value.size)
  
  console.log('\n确认机制:')
  console.log('- acknowledgeNotificationReceived 函数负责向后端发送确认')
  console.log('- removeFromPendingNotifications 函数负责从本地存储中移除')
  console.log('- updateNotificationStatus 函数负责更新通知状态')
  
  console.log('=== 检查完成 ===')
}

// 开发环境下暴露测试函数到全局
if (process.env.NODE_ENV === 'development') {
  window.testNotificationAudio = testAudioFeatures
  window.getNotificationAudioStats = getAudioStatistics
  window.resetNotificationAudio = resetAudioState
  window.checkNotificationAck = checkNotificationAcknowledgmentStatus
}

// 播放通知声音（优化版本）
const playNotificationSound = (priority = config.priorityLevels.NORMAL) => {
  if (!config.enableSound) {
    console.log('声音提醒已禁用')
    return
  }

  try {
    // 增加播放计数
    audioState.currentPlayingSounds++
    
    // 使用导入的声音文件
    let soundFile = notifySound
    let volume = getVolumeByPriority(priority)

    console.log('播放通知声音:', soundFile, '音量:', volume, '当前播放数量:', audioState.currentPlayingSounds)

    const audio = new Audio(soundFile)
    audio.volume = volume

    // 设置音频预加载
    audio.preload = 'auto'

    // 播放音频并处理错误
    const playPromise = audio.play()

    if (playPromise !== undefined) {
      playPromise
          .then(() => {
            console.log('通知声音播放成功')
          })
          .catch(error => {
            console.warn('播放通知声音失败:', error)

            // 如果播放失败，尝试生成备用提示音
            if (error.name === 'NotAllowedError') {
              console.warn('浏览器禁止自动播放音频，用户需要先与页面交互')
              // 可以显示一个提示让用户点击启用声音
              showAudioPermissionTip()
            } else {
              // 使用Web Audio API生成备用提示音
              generateNotificationBeep(priority)
            }
          })
          .finally(() => {
            // 播放完成后减少计数
            audioState.currentPlayingSounds = Math.max(0, audioState.currentPlayingSounds - 1)
          })
    }

    // 根据优先级播放多次（优化：只在非批量模式下才重复播放）
    if (priority >= config.priorityLevels.HIGH && !audioState.isProcessingBatch) {
      setTimeout(() => {
        // 检查是否还在允许的播放数量范围内
        if (audioState.currentPlayingSounds < config.maxSimultaneousSounds) {
          audioState.currentPlayingSounds++
          const secondAudio = new Audio(soundFile)
          secondAudio.volume = volume * 0.8
          secondAudio.play()
            .then(() => {
              console.log('高优先级二次播放成功')
            })
            .catch(err => {
              console.warn('第二次播放失败:', err)
            })
            .finally(() => {
              audioState.currentPlayingSounds = Math.max(0, audioState.currentPlayingSounds - 1)
            })
        } else {
          console.log('达到播放数量限制，跳过高优先级二次播放')
        }
      }, 600)
    }

  } catch (error) {
    console.warn('创建音频对象失败:', error)
    // 备用方案：使用Web Audio API生成提示音
    generateNotificationBeep(priority)
    // 减少计数
    audioState.currentPlayingSounds = Math.max(0, audioState.currentPlayingSounds - 1)
  }
}

// 显示音频权限提示
const showAudioPermissionTip = () => {
  ElMessage({
    message: '点击此处启用声音通知',
    type: 'info',
    duration: 3000,
    showClose: true,
    onClick: () => {
      // 用户点击后尝试播放一个静音的音频来获取权限
      const silentAudio = new Audio(notifySound)
      silentAudio.volume = 0
      silentAudio.play().then(() => {
        console.log('音频权限已获取')
        ElMessage.success('声音通知已启用')
      }).catch(err => {
        console.warn('获取音频权限失败:', err)
      })
    }
  })
}

// 使用Web Audio API生成备用提示音
const generateNotificationBeep = (priority = config.priorityLevels.NORMAL) => {
  try {
    const AudioContext = window.AudioContext || window.webkitAudioContext
    if (!AudioContext) {
      console.warn('浏览器不支持Web Audio API')
      return
    }

    const audioContext = new AudioContext()

    // 根据优先级设置不同的音调和持续时间
    let frequency = 800 // 默认频率
    let duration = 200  // 默认持续时间(ms)
    let volume = 0.3    // 默认音量

    switch (priority) {
      case config.priorityLevels.URGENT:
        frequency = 1000
        duration = 300
        volume = 0.5
        break
      case config.priorityLevels.HIGH:
        frequency = 900
        duration = 250
        volume = 0.4
        break
      case config.priorityLevels.LOW:
        frequency = 600
        duration = 150
        volume = 0.2
        break
    }

    // 创建音频节点
    const oscillator = audioContext.createOscillator()
    const gainNode = audioContext.createGain()

    // 连接节点
    oscillator.connect(gainNode)
    gainNode.connect(audioContext.destination)

    // 设置音调
    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime)
    oscillator.type = 'sine' // 正弦波，听起来比较温和

    // 设置音量包络（渐进渐出效果）
    const now = audioContext.currentTime
    gainNode.gain.setValueAtTime(0, now)
    gainNode.gain.linearRampToValueAtTime(volume, now + 0.01) // 快速渐入
    gainNode.gain.exponentialRampToValueAtTime(0.01, now + duration / 1000) // 渐出

    // 播放声音
    oscillator.start(now)
    oscillator.stop(now + duration / 1000)

    console.log(`生成提示音播放成功 - 频率: ${frequency}Hz, 时长: ${duration}ms, 音量: ${volume}`)

    // 根据优先级播放多次提示音
    if (priority >= config.priorityLevels.HIGH) {
      // 高优先级播放两次
      setTimeout(() => {
        generateNotificationBeep(config.priorityLevels.NORMAL)
      }, duration + 100)
    }

  } catch (error) {
    console.warn('生成提示音失败:', error)
    // 最后的备用方案：控制台提示
    console.log('🔔 新消息通知！')
  }
}

// ==================== 辅助函数 ====================

/**
 * 获取通知的紧急程度级别
 */
const getUrgencyLevel = (priority) => {
  if (priority >= config.priorityLevels.URGENT) {
    return 'urgent'
  } else if (priority >= config.priorityLevels.HIGH) {
    return 'high'
  } else if (priority <= config.priorityLevels.LOW) {
    return 'low'
  }
  return 'normal'
}

/**
 * 判断是否为待确认的通知
 */
const isPendingConfirmationNotification = (notification) => {
  // 会诊申请类型的通知需要用户确认，不自动关闭
  return notification.type === 'REQUEST' || 
         notification.type === 'PENDING_AUDIT' ||
         notification.type === 'consultation-request'
}

/**
 * 判断是否为紧急通知
 */
const isUrgentNotification = (notification) => {
  return notification.priority >= config.priorityLevels.URGENT ||
         notification.urgencyLevel === 'urgent' ||
         notification.type === 'URGENT'
}

/**
 * 获取自动关闭延迟时间
 */
const getAutoCloseDelay = (notification) => {
  const priority = notification.priority || config.priorityLevels.NORMAL
  
  // 根据优先级返回不同的延迟时间
  switch (priority) {
    case config.priorityLevels.LOW:
      return 3000 // 3秒
    case config.priorityLevels.NORMAL:
      return 5000 // 5秒
    case config.priorityLevels.HIGH:
      return 8000 // 8秒
    case config.priorityLevels.URGENT:
      return 0 // 紧急通知不自动关闭
    default:
      return 5000
  }
}

/**
 * 获取震动模式
 */
const getVibrationPattern = (priority) => {
  switch (priority) {
    case config.priorityLevels.URGENT:
      return [200, 100, 200, 100, 200] // 长短长短长
    case config.priorityLevels.HIGH:
      return [150, 100, 150] // 长短长
    case config.priorityLevels.LOW:
      return [100] // 短震动
    default:
      return [150] // 默认震动
  }
}

/**
 * 判断是否为可操作的消息（显示快速操作按钮）
 */
const isActionableMessage = (message) => {
  return message && (
    message.type === 'REQUEST' || 
    message.type === 'consultation-request' ||
    message.type === 'PENDING_AUDIT'
  )
}

/**
 * 获取消息图标
 */
const getMessageIcon = (message) => {
  if (!message) return 'Bell'
  
  switch (message.type) {
    case 'REQUEST':
    case 'consultation-request':
      return 'User'
    case 'ACCEPT':
    case 'consultation-accept':
      return 'Check'
    case 'REJECT':
    case 'consultation-reject':
      return 'Close'
    case 'COMPLETE':
    case 'consultation-complete':
      return 'CircleCheck'
    case 'URGENT':
      return 'Warning'
    default:
      return 'Bell'
  }
}

/**
 * 获取消息图标样式类
 */
const getMessageIconClass = (message) => {
  if (!message) return 'message-icon-normal'
  
  switch (message.type) {
    case 'REQUEST':
    case 'consultation-request':
      return 'message-icon-request'
    case 'ACCEPT':
    case 'consultation-accept':
      return 'message-icon-success'
    case 'REJECT':
    case 'consultation-reject':
      return 'message-icon-warning'
    case 'URGENT':
      return 'message-icon-urgent'
    default:
      return 'message-icon-normal'
  }
}

/**
 * 获取消息标签类型
 */
const getMessageTagType = (message) => {
  if (!message) return 'info'
  
  const priority = message.priority || config.priorityLevels.NORMAL
  if (priority >= config.priorityLevels.URGENT) {
    return 'danger'
  } else if (priority >= config.priorityLevels.HIGH) {
    return 'warning'
  } else if (priority <= config.priorityLevels.LOW) {
    return 'info'
  }
  return 'primary'
}

/**
 * 获取消息优先级文本
 */
const getMessagePriorityText = (message) => {
  if (!message) return '普通'
  
  const priority = message.priority || config.priorityLevels.NORMAL
  if (priority >= config.priorityLevels.URGENT) {
    return '紧急'
  } else if (priority >= config.priorityLevels.HIGH) {
    return '高'
  } else if (priority <= config.priorityLevels.LOW) {
    return '低'
  }
  return '普通'
}

/**
 * 格式化时间显示
 */
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  // 小于1分钟显示"刚刚"
  if (diff < 60000) {
    return '刚刚'
  }
  
  // 小于1小时显示分钟
  if (diff < 3600000) {
    return Math.floor(diff / 60000) + '分钟前'
  }
  
  // 小于24小时显示小时
  if (diff < 86400000) {
    return Math.floor(diff / 3600000) + '小时前'
  }
  
  // 超过24小时显示具体日期时间
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// ==================== 新消息弹窗处理函数 ====================

/**
 * 关闭新消息弹窗
 */
const closeNewMessageDialog = (skipAcknowledge = false) => {
  // 如果有当前消息，关闭时也应该确认该消息（除非明确跳过）
  if (currentNewMessage.value && !skipAcknowledge) {
    console.log('🔔 关闭新消息弹窗，确认消息:', currentNewMessage.value)
    
    // 确认通知已处理
    const ackId = currentNewMessage.value.notificationId || currentNewMessage.value.id
    if (ackId) {
      acknowledgeNotificationReceived(ackId)
      
      // 标记通知为已读状态
      const notificationIndex = activeNotifications.value.findIndex(n =>
        (n.id || n.notificationId) === ackId
      )
      if (notificationIndex > -1) {
        activeNotifications.value[notificationIndex].read = true
        console.log('标记通知为已读:', ackId)
      }
    }
    
    // 从待处理列表中移除
    const notificationId = currentNewMessage.value.id || currentNewMessage.value.notificationId
    if (notificationId) {
      removeFromPendingNotifications(notificationId)
    }
  }
  
  showNewMessageDialog.value = false
  currentNewMessage.value = null
}

/**
 * 查看消息详情
 */
const viewMessageDetail = () => {
  if (currentNewMessage.value) {
    console.log('🔍 从新消息弹窗查看详情:', currentNewMessage.value)
    
    // 查看详情会在 showNotificationDetail 中自动确认
    showNotificationDetail(currentNewMessage.value)
    
    // 关闭新消息弹窗，但不需要重复确认（已在 showNotificationDetail 中确认）
    showNewMessageDialog.value = false
    currentNewMessage.value = null
  }
}

/**
 * 快速接受操作
 */
const handleQuickAccept = async () => {
  if (currentNewMessage.value) {
    await handleAcceptFromDialog(currentNewMessage.value)
    // 快速操作已经在 handleAcceptFromDialog 中确认了通知，所以跳过重复确认
    showNewMessageDialog.value = false
    currentNewMessage.value = null
  }
}

/**
 * 快速拒绝操作
 */
const handleQuickReject = async () => {
  if (currentNewMessage.value) {
    await handleRejectFromDialog(currentNewMessage.value)
    // 快速操作已经在 handleRejectFromDialog 中确认了通知，所以跳过重复确认
    showNewMessageDialog.value = false
    currentNewMessage.value = null
  }
}

/**
 * 处理通知操作（用于NotificationCard组件的回调）
 */
const handleNotificationAction = (action, notification) => {
  console.log('处理通知操作:', action, notification)
  
  switch (action) {
    case 'accept':
      handleAcceptFromDialog(notification)
      break
    case 'reject':
      handleRejectFromDialog(notification)
      break
    case 'view':
      showNotificationDetail(notification)
      break
    default:
      console.warn('未知的通知操作:', action)
  }
}

/**
 * 处理通知查看
 */
const handleNotificationView = (notification) => {
  console.log('查看通知详情:', notification)
  showNotificationDetail(notification)
}

// ==================== 生命周期钩子 ====================


/**
 * 组件挂载时自动建立连接
 */
onMounted(async () => {
  console.log('ReliableNotification 组件已挂载，开始初始化连接')
  
  // 设置网络状态监听
  setupNetworkStatusMonitoring()
  
  // 定期清理本地存储
  const cleanupInterval = setInterval(cleanupLocalStorage, 60000) // 每分钟清理一次
  
  // 等待少许时间确保页面完全加载后再建立连接
  setTimeout(async () => {
    try {
      await connectToNotificationService()
      console.log('ReliableNotification 组件初始化连接完成')
      
    } catch (error) {
      console.error('ReliableNotification 组件初始化连接失败:', error)
      ElMessage.warning('通知服务连接失败，请检查网络连接')
    }
  }, 1000) // 延迟1秒连接，确保页面已稳定
  
  // 保存清理函数到组件实例
  onUnmounted(() => {
    clearInterval(cleanupInterval)
  })
})

/**
 * 组件卸载时清理资源
 */
onUnmounted(() => {
  console.log('ReliableNotification 组件即将卸载，清理资源')
  
  // 断开 WebSocket 连接
  disconnect()
  
  // 清理定时器
  if (reconnectInterval.value) {
    clearTimeout(reconnectInterval.value)
    reconnectInterval.value = null
  }
  
  if (networkStatusInterval.value) {
    clearInterval(networkStatusInterval.value)
    networkStatusInterval.value = null
  }
  
  // 清理事件监听
  window.removeEventListener('online', handleNetworkOnline)
  window.removeEventListener('offline', handleNetworkOffline)
  
  // 清理音频状态
  resetAudioState()
  
  console.log('ReliableNotification 组件资源清理完成')
})
</script>

<style scoped>
.reliable-notification {
  position: relative;
}

/* 通知触发器 */
.notification-trigger {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 2001;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

/* 连接状态指示器 - 小型 */
.connection-indicator {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.connection-indicator.offline {
  background-color: #fef2f2;
  border: 1px solid #ef4444;
}

/* 侧边通知面板 */
.el-drawer {
  overflow-y: auto;
}

.notification-drawer {
  /* 自定义抽屉样式 */

  .el-drawer__body {
    padding: 0;
    display: flex;
    flex-direction: column;
  }
}

/* 自定义头部 - 更紧凑的设计 */
.drawer-header {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f7f9fc;
  border-bottom: 1px solid #e5e7eb;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  font-size: 18px;
  color: #409eff;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.header-badge {
  /* 自定义徽章样式 */

  .el-badge__content {
    font-size: 12px;
    padding: 0 6px;
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 筛选和操作区 */
.filter-section {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #fff;
  border-bottom: 1px solid #e5e7eb;
}

.el-select {
  flex: 1;
}

/* 操作区域 */
.notification-actions {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 通知列表容器 */
.notification-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 2001;
  max-width: 450px;
  max-height: 80vh;
  overflow-y: auto;
  pointer-events: none;
}

.notification-container > * {
  pointer-events: auto;
  margin-bottom: 12px;
}

.connection-status {
  position: fixed;
  top: 60px;
  right: 20px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 2000;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.status-connected {
  background-color: #f0f9ff;
  color: #10b981;
  border: 1px solid #10b981;
}

.status-disconnected {
  background-color: #fef2f2;
  color: #ef4444;
  border: 1px solid #ef4444;
}

.status-reconnecting {
  background-color: #fffbeb;
  color: #f59e0b;
  border: 1px solid #f59e0b;
}

.offline-notifications {
  max-height: 400px;
  overflow-y: auto;
}

.notification-list {
  margin-top: 16px;
}

.notification-item {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #f9fafb;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notification-title {
  font-weight: 500;
  color: #374151;
}

.notification-time {
  font-size: 12px;
  color: #6b7280;
}

.notification-content {
  color: #4b5563;
  font-size: 14px;
  line-height: 1.4;
}

/* 优先级样式 */
:deep(.notification-urgent) {
  border-left: 4px solid #ef4444 !important;
  background-color: #fef2f2 !important;
}

:deep(.notification-urgent .el-notification__title) {
  color: #ef4444 !important;
  font-weight: bold !important;
}

:deep(.notification-high) {
  border-left: 4px solid #f59e0b !important;
  background-color: #fffbeb !important;
}

:deep(.notification-high .el-notification__title) {
  color: #f59e0b !important;
  font-weight: 600 !important;
}

:deep(.notification-normal) {
  border-left: 4px solid #3b82f6 !important;
}

:deep(.notification-low) {
  border-left: 4px solid #6b7280 !important;
  opacity: 0.8;
}

:deep(.notification-low .el-notification__title) {
  color: #6b7280 !important;
}

/* 紧急通知闪烁效果 */
:deep(.notification-urgent) {
  animation: urgent-blink 1s infinite alternate;
}

@keyframes urgent-blink {
  0% {
    box-shadow: 0 0 5px rgba(239, 68, 68, 0.3);
  }
  100% {
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.6);
  }
}

/* 通知触发器脉动效果 */
.notification-pulse {
  animation: notification-pulse 2s infinite;
}

@keyframes notification-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

/* 抽屉面板内容布局优化 */
.el-drawer__body {
  padding: 0;
  display: flex;
  flex-direction: column;
}

.connection-status-detail.status-connected {
  background-color: #f0f9ff;
  color: #10b981;
  border-left: 4px solid #10b981;
}

.connection-status-detail.status-disconnected {
  background-color: #fef2f2;
  color: #ef4444;
  border-left: 4px solid #ef4444;
}

.connection-status-detail.status-reconnecting {
  background-color: #fffbeb;
  color: #f59e0b;
  border-left: 4px solid #f59e0b;
}

/* 角色筛选样式 */
.role-filter-section .el-select {
  --el-select-input-focus-border-color: #409eff;
}

/* 通知列表容器滚动优化 */
.notification-list-container {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.notification-list-container::-webkit-scrollbar {
  width: 6px;
}

.notification-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.notification-list-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.notification-list-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 新消息弹窗样式 */
.new-message-dialog {
  .el-dialog__header {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f7f9fc;
  }

  .el-dialog__title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }

  .el-dialog__body {
    padding: 0;
  }

  .message-header {
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid #e5e7eb;
  }

  .message-icon {
    font-size: 24px;
    color: #409eff;
  }

  .message-info {
    flex: 1;
  }

  .message-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }

  .message-time {
    font-size: 12px;
    color: #6b7280;
  }

  .message-priority {
    margin-left: 8px;
  }

  .message-body {
    padding: 16px;
    color: #4b5563;
    font-size: 14px;
    line-height: 1.4;
  }

  .message-content {
    margin-bottom: 12px;
  }

  .message-details {
    margin-top: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    color: #374151;
    font-size: 14px;
  }

  .detail-label {
    font-weight: 500;
    color: #333;
  }

  .detail-value {
    color: #4b5563;
  }

  .message-dialog-footer {
    padding: 12px 16px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    border-top: 1px solid #e5e7eb;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-drawer {
    --el-drawer-size: 100% !important;
  }

  .notification-trigger {
    bottom: 80px; /* 避免与移动端底部导航栏冲突 */
  }
}

@media (max-width: 600px) {
  .notification-drawer {
    --el-drawer-size: 100% !important;
  }
}

@media (min-width: 601px) and (max-width: 1024px) {
  .notification-drawer {
    --el-drawer-size: 420px !important;
  }
}

@media (min-width: 1025px) {
  .notification-drawer {
    --el-drawer-size: 480px !important;
  }
}

/* 空状态优化 */
.no-notifications .el-empty {
  padding: 20px 0;
}

.no-notifications .el-empty__description {
  color: #909399;
  font-size: 14px;
}
</style>
