<template>
  <el-dialog
    v-model="visible"
    title="通知详情"
    width="80%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    class="notification-detail-dialog"
  >
    <!-- 通知基本信息 -->
    <div class="notification-header">
      <el-card class="notification-basic-info">
        <div slot="header">
          <span>通知信息</span>
        </div>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="通知类型">
            <el-tag :type="getTypeTagType(notification.type)" size="small">
              {{ getTypeText(notification.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="通知时间">
            {{ formatDateTime(notification.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="紧急程度" v-if="notification.urgencyLevel">
            <el-tag :type="getUrgencyTagType(notification.urgencyLevel)" size="small">
              {{ getUrgencyText(notification.urgencyLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="通知标题" span="3">
            {{ notification.title }}
          </el-descriptions-item>
          <el-descriptions-item label="通知内容" span="3" v-if="notification.content">
            <div class="notification-content">{{ notification.content }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <!-- 会诊详情 - 使用新的会诊工作台组件 -->
    <div class="consultation-detail-section" v-if="consultationId">
      <el-empty description="会诊详情功能已升级">
        <template #image>
          <el-icon size="80" color="#409eff">
            <View />
          </el-icon>
        </template>
        <template #description>
          <span>请点击下方按钮打开新的会诊详情页面</span>
        </template>
      </el-empty>
    </div>

    <!-- 无会诊ID时的提示 -->
    <div v-else class="no-consultation-info">
      <el-empty description="无法获取会诊详情信息">
        <el-button type="primary" @click="openConsultationList">查看会诊列表</el-button>
      </el-empty>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          v-if="isConsultationRequest"
          type="success"
          @click="handleAccept"
          :loading="actionLoading"
          :icon="Check"
        >
          接受会诊
        </el-button>
        <el-button
          v-if="isConsultationRequest"
          type="danger"
          @click="handleReject"
          :loading="actionLoading"
          :icon="Close"
        >
          拒绝会诊
        </el-button>
        <el-button
          v-if="consultationId"
          type="primary"
          @click="openConsultationDetail"
          :icon="View"
        >
          打开会诊详情页
        </el-button>
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { Check, Close, View } from '@element-plus/icons-vue'
export default {
  name: 'NotificationDetailDialog',
  components: {
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    notification: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'accept', 'reject'],
  data() {
    return {
      actionLoading: false,
      Check,
      Close,
      View
    }
  },
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    },
    // 判断是否为会诊申请
    isConsultationRequest() {
      return this.notification.type === 'REQUEST'
    },
    // 提取会诊ID
    consultationId() {
      return this.notification.consultationId ||
             this.notification.requestId ||
             this.extractIdFromNotification(this.notification)
    }
  },
  methods: {
    // 从通知中提取ID
    extractIdFromNotification(notification) {
      // 尝试从内容中提取ID
      if (notification.content) {
        const idMatch = notification.content.match(/ID[：:]\s*(\d+)/)
        if (idMatch) return parseInt(idMatch[1])
      }

      // 尝试从请求编号中提取
      if (notification.requestNo) {
        const noMatch = notification.requestNo.match(/\d+/)
        if (noMatch) return parseInt(noMatch[0])
      }

      // 尝试从URL中提取
      if (notification.url) {
        return this.extractIdFromUrl(notification.url)
      }

      return null
    },
    // 从URL中提取ID
    extractIdFromUrl(url) {
      if (!url) return null

      const patterns = [
        /\/consultation\/detail\/(\d+)/,
        /\/consultation\/request\/(\d+)/,
        /\/consultation\/(\d+)/,
        /id=(\d+)/,
        /requestId=(\d+)/,
        /consultationId=(\d+)/
      ]

      for (const pattern of patterns) {
        const match = url.match(pattern)
        if (match) {
          return parseInt(match[1])
        }
      }

      return null
    },
    // 获取类型标签类型
    getTypeTagType(type) {
      const typeMap = {
        'REQUEST': 'primary',
        'ACCEPT': 'success',
        'REJECT': 'danger',
        'COMPLETE': 'success',
        'CANCEL': 'info',
        'URGENT': 'warning'
      }
      return typeMap[type] || 'info'
    },
    // 获取类型文本
    getTypeText(type) {
      const textMap = {
        'REQUEST': '会诊申请',
        'ACCEPT': '会诊接受',
        'REJECT': '会诊拒绝',
        'COMPLETE': '会诊完成',
        'CANCEL': '会诊取消',
        'URGENT': '紧急通知'
      }
      return textMap[type] || type
    },
    // 获取紧急程度标签类型
    getUrgencyTagType(urgencyLevel) {
      const typeMap = {
        'URGENT': 'danger',
        'HIGH': 'warning',
        'NORMAL': 'info',
        'LOW': 'info'
      }
      return typeMap[urgencyLevel] || 'info'
    },
    // 获取紧急程度文本
    getUrgencyText(urgencyLevel) {
      const textMap = {
        'URGENT': '紧急',
        'HIGH': '高',
        'NORMAL': '普通',
        'LOW': '低'
      }
      return textMap[urgencyLevel] || '普通'
    },
    // 格式化日期时间
    formatDateTime(time) {
      if (!time) return '-'
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    // 处理接受
    handleAccept() {
      this.actionLoading = true
      this.$emit('accept', this.notification)
      setTimeout(() => {
        this.actionLoading = false
        this.visible = false
      }, 500)
    },
    // 处理拒绝
    handleReject() {
      this.actionLoading = true
      this.$emit('reject', this.notification)
      setTimeout(() => {
        this.actionLoading = false
        this.visible = false
      }, 500)
    },
    // 处理会诊
    handleConsult(consultation) {
      this.$emit('consult', consultation)
    },
    // 打开会诊详情页面
    openConsultationDetail() {
      if (this.consultationId) {
        const detailUrl = `/consultation/detail/${this.consultationId}`
        window.open(detailUrl, '_blank')
      } else {
        this.openConsultationList()
      }
    },
    // 打开会诊列表
    openConsultationList() {
      window.open('/consultation/my-consultation', '_blank')
    }
  }
}
</script>

<style scoped>
.notification-detail-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
}

.notification-header {
  margin-bottom: 20px;
}

.notification-basic-info {
  margin-bottom: 0;
}

.notification-content {
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #303133;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

.consultation-detail-section {
  margin-top: 20px;
}

.no-consultation-info {
  margin: 40px 0;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .notification-detail-dialog :deep(.el-dialog) {
    width: 95% !important;
  }
}

@media (max-width: 768px) {
  .notification-detail-dialog :deep(.el-dialog) {
    width: 98% !important;
  }

  .notification-detail-dialog :deep(.el-descriptions) {
    font-size: 12px;
  }
}
</style>
