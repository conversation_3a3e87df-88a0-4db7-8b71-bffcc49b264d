<template>
  <div class="consultation-notification-card">
    <!-- 通知头部 -->
    <div class="notification-header">
      <div class="notification-icon">
        <el-icon :size="24" :color="getIconColor(notification.type)">
          <component :is="getIconComponent(notification.type)" />
        </el-icon>
      </div>
      <div class="notification-title">
        <h4>{{ notification.title }}</h4>
        <span class="notification-time">{{ formatTime(notification.createTime) }}</span>
      </div>
      <div class="notification-close">
        <el-button
          type="text"
          :icon="Close"
          size="small"
          @click="handleClose"
        />
      </div>
    </div>

    <!-- 通知内容 -->
    <div class="notification-content">
      <div class="consultation-info">
        <div class="info-row" v-if="notification.requestNo">
          <span class="label">申请编号：</span>
          <span class="value">{{ notification.requestNo }}</span>
        </div>
        
        <div class="info-row" v-if="notification.requesterName">
          <span class="label">申请医生：</span>
          <span class="value">{{ notification.requesterName }}</span>
        </div>
        
        <div class="info-row" v-if="notification.patientName">
          <span class="label">患者姓名：</span>
          <span class="value">{{ notification.patientName }}</span>
        </div>
        
        <div class="info-row" v-if="notification.urgencyLevel">
          <span class="label">紧急程度：</span>
          <el-tag 
            :type="getUrgencyTagType(notification.urgencyLevel)" 
            size="small"
          >
            {{ getUrgencyText(notification.urgencyLevel) }}
          </el-tag>
        </div>
        
        <div class="info-row" v-if="notification.content">
          <span class="value description">{{ notification.content }}</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="notification-actions">
      <el-button 
        type="primary" 
        size="small" 
        @click="handleViewDetails"
        :icon="View"
      >
        查看详情
      </el-button>
      
      <!-- 会诊申请快捷操作 -->
      <template v-if="isConsultationRequest">
        <el-button 
          type="success" 
          size="small" 
          @click="handleQuickAccept"
          :icon="Check"
        >
          接受
        </el-button>
        
        <el-button 
          type="warning" 
          size="small" 
          @click="handleQuickReject"
          :icon="Close"
        >
          拒绝
        </el-button>
      </template>
      
      <el-button
        size="small"
        @click="handleClose"
        :icon="Close"
      >
        关闭
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Close, 
  View, 
  Check, 
  User, 
  Warning,
  SuccessFilled,
  InfoFilled 
} from '@element-plus/icons-vue'

const props = defineProps({
  notification: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'action'])

const router = useRouter()

// 判断是否为会诊申请通知
const isConsultationRequest = computed(() => {
  return props.notification.type === 'REQUEST' || 
         props.notification.type === 'consultation-request' ||
         props.notification.type === 'PENDING_AUDIT'
})

// 获取图标组件
const getIconComponent = (type) => {
  const iconMap = {
    'REQUEST': User,
    'ACCEPT': SuccessFilled,
    'REJECT': Warning,
    'COMPLETE': Check,
    'CANCEL': Close,
    'URGENT': Warning
  }
  return iconMap[type] || InfoFilled
}

// 获取图标颜色
const getIconColor = (type) => {
  const colorMap = {
    'REQUEST': '#409EFF',
    'ACCEPT': '#67C23A',
    'REJECT': '#F56C6C',
    'COMPLETE': '#67C23A',
    'CANCEL': '#909399',
    'URGENT': '#E6A23C'
  }
  return colorMap[type] || '#409EFF'
}

// 获取紧急程度标签类型
const getUrgencyTagType = (urgencyLevel) => {
  const typeMap = {
    'URGENT': 'danger',      // 紧急
    'NORMAL': 'info',        // 普通
    'LOW': 'success'         // 非紧急
  }
  return typeMap[urgencyLevel] || 'info'
}

// 获取紧急程度文本
const getUrgencyText = (urgencyLevel) => {
  const textMap = {
    'URGENT': '紧急',
    'NORMAL': '普通',
    'LOW': '非紧急'
  }
  return textMap[urgencyLevel] || '普通'
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

// 查看详情
const handleViewDetails = () => {
  console.log('查看会诊详情:', props.notification)

  // 触发查看操作，让父组件处理具体的查看逻辑
  emit('action', 'view', props.notification)
}

// 快速接受
const handleQuickAccept = () => {
  console.log('快速接受会诊:', props.notification)

  // 触发接受操作，让父组件处理具体的接受逻辑
  emit('action', 'accept', props.notification)

  // 不在这里立即显示成功消息和关闭通知
  // 等待父组件处理完成后再由父组件决定是否关闭
}

// 快速拒绝
const handleQuickReject = () => {
  console.log('快速拒绝会诊:', props.notification)

  // 触发拒绝操作，让父组件处理具体的拒绝逻辑
  emit('action', 'reject', props.notification)
}

// 关闭通知
const handleClose = () => {
  console.log('🔔 关闭通知卡片')
  emit('close')
}
</script>

<style scoped>
.consultation-notification-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 16px;
  min-width: 300px;
  max-width: 100%;
  width: calc(100% - 24px);
  margin: 12px;
  box-sizing: border-box;
}

.notification-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.notification-icon {
  margin-right: 12px;
  flex-shrink: 0;
}

.notification-title {
  flex: 1;
}

.notification-title h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-close {
  flex-shrink: 0;
}

.notification-content {
  margin-bottom: 16px;
}

.consultation-info {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  flex-wrap: wrap;
  gap: 4px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #606266;
  min-width: 70px;
  flex-shrink: 0;
}

.value {
  color: #303133;
  flex: 1;
  min-width: 0;
  word-break: break-word;
}

.description {
  line-height: 1.5;
}

.notification-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-end;
  align-items: center;
}

/* 紧急通知样式 */
.consultation-notification-card[data-urgent="true"] {
  border-left-color: #E6A23C;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
  }
  100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* 响应式设计 - 小屏幕优化 */
@media (max-width: 480px) {
  .consultation-notification-card {
    padding: 12px;
    margin: 8px;
    min-width: 280px;
  }
  
  .notification-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .notification-actions .el-button {
    margin-bottom: 4px;
    width: 100%;
  }
  
  .notification-actions .el-button:last-child {
    margin-bottom: 0;
  }
}

/* 中等屏幕优化 */
@media (max-width: 600px) and (min-width: 481px) {
  .consultation-notification-card {
    padding: 14px;
  }
  
  .notification-actions {
    justify-content: center;
  }
}

/* 按钮优化 */
.notification-actions .el-button {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 确保按钮在小屏幕下的最小宽度 */
@media (max-width: 400px) {
  .notification-actions .el-button {
    min-width: 60px;
    padding: 6px 8px;
  }
}
</style>
