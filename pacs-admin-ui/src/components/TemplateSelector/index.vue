<template>
  <div class="template-selector">
    <!-- 模板选择按钮 -->
    <el-button 
      type="primary" 
      size="small" 
      @click="openSelector"
      :icon="Document"
    >
      选择模板
    </el-button>

    <!-- 模板选择对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择诊断模板"
      width="80%"
      :before-close="handleClose"
    >
      <div class="template-selector-content">
        <!-- 搜索和筛选区域 -->
        <div class="filter-section">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-select
                v-model="filterParams.modalityType"
                placeholder="检查类型"
                clearable
                style="width: 100%"
                @change="handleFilterChange"
              >
                <el-option label="CT" value="CT" />
                <el-option label="MRI" value="MRI" />
                <el-option label="DR" value="DR" />
                <el-option label="US" value="US" />
                <el-option label="其他" value="OTHER" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input
                v-model="filterParams.bodyPart"
                placeholder="身体部位"
                clearable
                @input="handleFilterChange"
              />
            </el-col>
            <el-col :span="8">
              <el-input
                v-model="filterParams.keywords"
                placeholder="关键词搜索"
                clearable
                @input="handleFilterChange"
              />
            </el-col>
          </el-row>
        </div>

        <!-- 模板列表 -->
        <div class="template-tabs">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="推荐模板" name="recommended">
              <div class="template-grid">
                <div
                  v-for="template in recommendedTemplates"
                  :key="template.id"
                  class="template-card"
                  @click="selectTemplate(template)"
                >
                  <div class="template-header">
                    <span class="template-name">{{ template.name }}</span>
                    <el-tag size="small" type="primary">
                      {{ template.modalityType }}
                    </el-tag>
                  </div>
                  <div class="template-meta">
                    <span class="template-part">{{ template.bodyPart }}</span>
                    <span class="template-usage">使用{{ template.usageCount || 0 }}次</span>
                  </div>
                  <div class="template-preview">
                    <div v-if="template.findings" class="preview-item">
                      <strong>影像所见：</strong>{{ template.findings.substring(0, 50) + '...' }}
                    </div>
                    <div v-if="template.opinion" class="preview-item">
                      <strong>影像意见：</strong>{{ template.opinion.substring(0, 50) + '...' }}
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="我的模板" name="user">
              <div class="template-grid">
                <div
                  v-for="template in userTemplates"
                  :key="template.id"
                  class="template-card"
                  @click="selectTemplate(template)"
                >
                  <div class="template-header">
                    <span class="template-name">{{ template.name }}</span>
                    <el-tag size="small" type="success">
                      {{ template.modalityType }}
                    </el-tag>
                  </div>
                  <div class="template-meta">
                    <span class="template-part">{{ template.bodyPart }}</span>
                    <span class="template-usage">使用{{ template.usageCount || 0 }}次</span>
                  </div>
                  <div class="template-preview">
                    <div v-if="template.findings" class="preview-item">
                      <strong>影像所见：</strong>{{ template.findings.substring(0, 50) + '...' }}
                    </div>
                    <div v-if="template.opinion" class="preview-item">
                      <strong>影像意见：</strong>{{ template.opinion.substring(0, 50) + '...' }}
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="公共模板" name="public">
              <div class="template-grid">
                <div
                  v-for="template in publicTemplates"
                  :key="template.id"
                  class="template-card"
                  @click="selectTemplate(template)"
                >
                  <div class="template-header">
                    <span class="template-name">{{ template.name }}</span>
                    <el-tag size="small" type="info">
                      {{ template.modalityType }}
                    </el-tag>
                  </div>
                  <div class="template-meta">
                    <span class="template-part">{{ template.bodyPart }}</span>
                    <span class="template-usage">使用{{ template.usageCount || 0 }}次</span>
                  </div>
                  <div class="template-preview">
                    <div v-if="template.findings" class="preview-item">
                      <strong>影像所见：</strong>{{ template.findings.substring(0, 50) + '...' }}
                    </div>
                    <div v-if="template.opinion" class="preview-item">
                      <strong>影像意见：</strong>{{ template.opinion.substring(0, 50) + '...' }}
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { Document } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { 
  searchTemplates, 
  getUserTemplates, 
  getPublicTemplates, 
  useTemplate,
  getDefaultTemplate 
} from '@/api/diagnosis/template'

// Props
const props = defineProps({
  modalityType: {
    type: String,
    default: ''
  },
  bodyPart: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['template-selected'])

// 响应式数据
const dialogVisible = ref(false)
const activeTab = ref('recommended')
const loading = ref(false)

// 筛选参数
const filterParams = reactive({
  modalityType: props.modalityType,
  bodyPart: props.bodyPart,
  keywords: ''
})

// 模板数据
const recommendedTemplates = ref([])
const userTemplates = ref([])
const publicTemplates = ref([])

// 方法
const openSelector = () => {
  dialogVisible.value = true
  loadTemplates()
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleTabChange = (tabName) => {
  activeTab.value = tabName
  loadTemplates()
}

const handleFilterChange = () => {
  loadTemplates()
}

const loadTemplates = async () => {
  loading.value = true
  try {
    if (activeTab.value === 'recommended') {
      await loadRecommendedTemplates()
    } else if (activeTab.value === 'user') {
      await loadUserTemplates()
    } else if (activeTab.value === 'public') {
      await loadPublicTemplates()
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
  } finally {
    loading.value = false
  }
}

const loadRecommendedTemplates = async () => {
  const params = {
    modalityType: filterParams.modalityType || props.modalityType,
    bodyPart: filterParams.bodyPart || props.bodyPart
  }
  
  const res = await searchTemplates(params)
  if (res.code === 200) {
    recommendedTemplates.value = res.data || []
  }
}

const loadUserTemplates = async () => {
  const res = await getUserTemplates()
  if (res.code === 200) {
    userTemplates.value = res.data || []
  }
}

const loadPublicTemplates = async () => {
  const res = await getPublicTemplates()
  if (res.code === 200) {
    publicTemplates.value = res.data || []
  }
}

const selectTemplate = async (template) => {
  try {
    // 更新使用次数
    await useTemplate(template.id)

    // 触发选择事件
    emit('template-selected', template)

    // 关闭对话框
    handleClose()

    // 不在这里显示成功消息，由父组件在确认应用后显示
  } catch (error) {
    console.error('更新模板使用次数失败:', error)
    ElMessage.error('选择模板失败')
  }
}



// 监听props变化
watch(() => props.modalityType, (newVal) => {
  filterParams.modalityType = newVal
})

watch(() => props.bodyPart, (newVal) => {
  filterParams.bodyPart = newVal
})

// 暴露方法
defineExpose({
  openSelector
})
</script>

<style scoped>
.template-selector-content {
  max-height: 600px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.template-tabs {
  height: 500px;
  overflow-y: auto;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  padding: 16px 0;
}

.template-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;
}

.template-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.template-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 12px;
  color: #909399;
}

.template-meta span {
  padding: 2px 6px;
  background-color: #f0f2f5;
  border-radius: 4px;
}

.template-preview {
  font-size: 12px;
  color: #606266;
  line-height: 1.5;
  max-height: 80px;
  overflow: hidden;
}

.preview-item {
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dialog-footer {
  text-align: right;
}
</style>
