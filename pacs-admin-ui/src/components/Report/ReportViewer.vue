<template>
  <el-dialog
    v-model="visible"
    :title="getTitle"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    append-to-body
   :fullscreen="true"
  >
    <template #header>
      <div class="dialog-header">
        <span>{{ getTitle }}</span>
        <div class="toolbar-buttons">
<!--          <el-button
            type="primary"
            :icon="DocumentCopy"
            @click="exportPDF()"
            :loading="btnLoading"
            size="small"
          >
            导出PDF
          </el-button>-->
          <el-button
            type="primary"
            :icon="Printer"
            @click="printReport()"
            :loading="btnLoading"
            size="small"
          >
            打印
          </el-button>
        </div>
      </div>
    </template>
    <div class="viewer-host">
      <Viewer ref="reportViewer" language="zh" />
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
  // 报表查看器包
  import { Core, PdfExport } from '@grapecity/activereports';
  import { Viewer } from '@grapecity/activereports-vue';
  import '@grapecity/activereports-localization'; //预览汉化
  import '@grapecity/activereports/styles/ar-js-ui.css';
  import '@grapecity/activereports/styles/ar-js-viewer.css';
  import { computed, nextTick, ref } from 'vue';
  import { DocumentCopy, Printer } from '@element-plus/icons-vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { getFontConfigResourceUrl, updateFontConfigPaths } from '@/utils/resource';

  const visible = ref(false);
  const btnLoading = ref(false);
  let report = {};
  const filename = ref('');
  const reportData = ref(null);
  const reportViewer = ref(); // 恢复为ref引用

  // 打开报告
  function open(options) {
    visible.value = true;
    filename.value = options.filename || '报告预览';
    reportData.value = options.data;

    // 优先使用动态模板，如果没有则使用静态模板
    if (options.templateJson) {
      try {
        const parsedTemplate = JSON.parse(options.templateJson);
        report = parsedTemplate;
        openReport(report);
      } catch (error) {
        console.error('动态模板解析失败:', error);
        ElMessage.error('模板格式错误：' + error.message);
        return;
      }
    } else if (options.template) {
      report = options.template;
      openReport(report);
    } else {
      const staticTemplatePath = options.rptPath || (import.meta.env.BASE_URL + 'report.rdlx-json');
      loadAndOpenReport(staticTemplatePath);
    }
  }

  // 从文件加载并打开报告
  async function loadAndOpenReport(rptPath) {
    try {
      const reportResponse = await fetch(rptPath);
      if (!reportResponse.ok) {
        throw new Error(`Failed to load template: ${reportResponse.status} ${reportResponse.statusText}`);
      }

      const text = await reportResponse.text();
      report = JSON.parse(text);
      openReport(report);
    } catch (error) {
      console.error('静态模板加载失败:', error);
      ElMessage.error('模版加载失败：' + error.message);
    }
  }

  // 获取字体配置URL
  function getFontConfigUrl() {
    // 直接使用本地字体配置文件
    return getFontConfigResourceUrl();
  }

  // 加载并处理字体配置
  async function loadFontConfig(fontConfigUrl) {
    try {
      const response = await fetch(fontConfigUrl);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const fontConfig = await response.json();
      return updateFontConfigPaths(fontConfig);
    } catch (error) {
      console.error('字体配置加载失败:', error);
      // 返回一个基础的字体配置
      return {
        path: "",
        descriptors: [
          {
            name: "Arial",
            source: "Arial"
          },
          {
            name: "SimSun",
            source: "SimSun"
          }
        ]
      };
    }
  }

  // 打开报告
  async function openReport(reportTemplate) {
    // 先注册字体
    try {
      const fontConfigUrl = getFontConfigUrl();
      await Core.FontStore.registerFonts(fontConfigUrl);
    } catch (fontError) {
      console.warn('字体注册失败，继续执行:', fontError);
    }

    nextTick(async () => {
      try {
        const viewer = reportViewer.value.Viewer();
        if (!viewer) {
          ElMessage.error('查看器实例获取失败');
          return;
        }

        // 设置查看器
        viewer.resetDocument();
        viewer.availableExports = ['pdf'];

        // 处理数据绑定
        if (reportTemplate.DataSources && reportTemplate.DataSources[0] && reportData.value) {
          const dataSource = reportTemplate.DataSources[0];
          const jsonData = JSON.stringify(reportData.value);
          dataSource.ConnectionProperties.ConnectString = 'jsondata=' + jsonData;
        }

        // 打开报告
        viewer.open(reportTemplate);
        viewer.viewMode = 'Continuous';

      } catch (error) {
        console.error('报告打开失败:', error);
        ElMessage.error('报告打开失败：' + error.message);
      }
    });
  }

  // 导出PDF
  async function exportPDF() {
    try {
      btnLoading.value = true;
      const viewer = reportViewer.value.Viewer();
      const doc = viewer.getDocument();
      const result = await PdfExport.exportDocument(doc);
      result.download(filename.value + '.pdf');
      ElMessage.success('导出成功');
    } catch (error) {
      console.error('PDF export failed:', error);
      ElMessage.error('PDF导出失败：' + error.message);
    } finally {
      btnLoading.value = false;
    }
  }

  // 打印报告
  async function printReport() {
    try {
      btnLoading.value = true;
      const viewer = reportViewer.value.Viewer();
      await viewer.print();
      ElMessage.success('打印成功');
    } catch (error) {
      console.error('Print failed:', error);
      ElMessage.error('打印失败：' + error.message);
    } finally {
      btnLoading.value = false;
    }
  }



  // 转换自定义格式为ActiveReports标准格式
  function convertToActiveReportsFormat(customTemplate) {
    console.log('Converting template format...');

    // 尝试直接使用原格式，让ActiveReports自动处理
    if (customTemplate.Name && customTemplate.DataSources) {
      console.log('Template appears to be in a compatible format, using as-is');
      return customTemplate;
    }

    // 如果格式不兼容，返回一个基础的模版
    console.warn('Unknown template format, using fallback template');
    return {
      "$schema": "https://www.grapecity.com/activereportsjs/schemas/report-15.json",
      "Type": "report",
      "Version": "15.1.0",
      "Name": "FallbackTemplate",
      "Body": {
        "Type": "bandedReportItem",
        "Name": "body",
        "Height": "11in",
        "Sections": [
          {
            "Type": "detail",
            "Name": "detail",
            "Height": "8in",
            "Items": [
              {
                "Type": "textBox",
                "Name": "message",
                "Value": "模版格式转换中，请联系管理员更新模版格式",
                "Style": {
                  "fontSize": "14pt",
                  "textAlign": "center"
                },
                "Location": "1in, 2in",
                "Size": "6in, 1in"
              }
            ]
          }
        ]
      },
      "DataSources": [
        {
          "Name": "reportData",
          "ConnectionProperties": {
            "DataProvider": "JSON",
            "ConnectString": "jsondata="
          }
        }
      ]
    };
  }

  const getTitle = computed(() => '预览报表-' + filename.value);

  defineExpose({
    open,
  });
</script>

<style src="@grapecity/activereports/styles/ar-js-ui.css"></style>
<style src="@grapecity/activereports/styles/ar-js-viewer.css"></style>
<style scoped>
  .report-dialog :deep(.el-dialog) {
    height: 100vh;
  }

  .report-dialog :deep(.el-dialog__body) {
    padding: 0;
    height: 100vh;
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .dialog-header .toolbar-buttons {
    display: flex;
    gap: 8px;
  }

  .viewer-host {
    width: 100%;
    height: calc(100vh - 100px);
  }
</style>