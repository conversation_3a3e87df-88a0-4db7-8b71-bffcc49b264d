import Layout from '@/layout'

// 诊断管理路由
export default [
  {
    path: '/diagnosis',
    component: Layout,
    alwaysShow: true,
    name: 'Diagnosis',
    meta: { title: '诊断管理', icon: 'form' },

    children: [
      {
        path: 'workspace',
        component: () => import('@/views/diagnosis/DiagnosisWorkspace'),
        name: 'DiagnosisWorkspace',
        meta: { title: '诊断工作台', icon: 'monitor' }
      },
      {
        path: 'study-list',
        component: () => import('@/views/diagnosis/study-list'),
        name: 'StudyList',
        meta: { title: '检查列表(旧版)', icon: 'table' }
      },
      {
        path: 'editor',
        component: () => import('@/views/diagnosis/diagnose-editor-new'),
        name: 'DiagnosisEditor',
        meta: { title: '编写诊断', activeMenu: '/diagnosis/study-list' },
        hidden: true
      },
      {
        path: 'view',
        component: () => import('@/views/diagnosis/diagnose-editor-new'),
        name: 'DiagnosisView',
        meta: { title: '查看诊断', activeMenu: '/diagnosis/study-list' },
        hidden: true
      },
      {
        path: 'template',
        component: () => import('@/views/diagnosis/template/index'),
        name: 'DiagnosisTemplate',
        meta: { title: '诊断文本模版', icon: 'documentation' }
      },
      {
        path: 'reportTemplate',
        component: () => import('@/views/diagnosis/reportTemplate/index'),
        name: 'ReportTemplate',
        meta: { title: '报告模版管理', icon: 'edit' }
      }
    ]
  }
]
