<template>
  <div class="consultation-workspace">
    <!-- 顶部搜索栏 -->
    <div class="top-search-bar">
      <ConsultationSearchBar
        :queryParams="queryParams"
        :current-role="currentRole"
        @search="handleSearch"
        @reset="handleReset"
      />
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧会诊列表 -->
      <div class="left-panel">
        <!-- 角色切换和快速筛选整合到左侧面板头部 -->
        <div class="panel-header">
          <div class="role-switch-compact">
            <el-radio-group v-model="currentRole" @change="handleRoleChange" size="small">
              <el-radio-button label="requester">申请医生</el-radio-button>
              <el-radio-button label="consultant">会诊专家</el-radio-button>
            </el-radio-group>
          </div>

          <div class="quick-filters-compact">
            <el-dropdown @command="setQuickFilter" trigger="click">
              <el-button size="small" type="primary">
                {{ getFilterLabel() }}
                <el-badge :value="getTotalCount()" :max="99" class="filter-badge-compact" />
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <!-- 申请医生场景 -->
                  <template v-if="currentRole === 'requester'">
                    <el-dropdown-item command="pending">
                      <div class="scenario-menu-item">
                        <span>我的待会诊</span>
                        <el-badge :value="scenarioStatusCounts.pendingCount" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="accepted">
                      <div class="scenario-menu-item">
                        <span>进行中</span>
                        <el-badge :value="scenarioStatusCounts.acceptedCount" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="completed">
                      <div class="scenario-menu-item">
                        <span>已完成</span>
                        <el-badge :value="scenarioStatusCounts.completedCount" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="rejected">
                      <div class="scenario-menu-item">
                        <span>已拒绝</span>
                        <el-badge :value="scenarioStatusCounts.rejectedCount" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="withdrawn">
                      <div class="scenario-menu-item">
                        <span>已撤回</span>
                        <el-badge :value="scenarioStatusCounts.withdrawnCount" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                  </template>

                  <!-- 会诊专家场景 -->
                  <template v-else-if="currentRole === 'consultant'">
                    <el-dropdown-item command="pending">
                      <div class="scenario-menu-item">
                        <span>待接受</span>
                        <el-badge :value="scenarioStatusCounts.pendingCount" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="accepted">
                      <div class="scenario-menu-item">
                        <span>诊断中</span>
                        <el-badge :value="scenarioStatusCounts.acceptedCount" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="completed">
                      <div class="scenario-menu-item">
                        <span>已完成</span>
                        <el-badge :value="scenarioStatusCounts.completedCount" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                  </template>

                  <el-dropdown-item divided command="all">
                    <div class="scenario-menu-item">
                      <el-icon color="#909399"><List /></el-icon>
                      <span>全部记录</span>
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <ConsultationList
          :loading="loading"
          :data="consultationList"
          :total="total"
          :current-consultation="currentConsultation"
          :current-role="currentRole"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          @consultation-select="handleConsultationSelect"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
          @refresh="getConsultationList"
        />
      </div>

      <!-- 中间信息展示区域 -->
      <div class="center-panel">
        <ConsultationTabs
          ref="consultationTabsRef"
          v-if="currentConsultation"
          :consultation-data="currentConsultation"
          :current-role="currentRole"
          :user-permissions="userPermissions"
          @consultation-accept="handleConsultationAccept"
          @consultation-reject="handleConsultationReject"
          @consultation-complete="handleConsultationComplete"
          @consultation-withdraw="handleConsultationWithdraw"
          @diagnosis-save="handleDiagnosisSave"
        />
        <div v-else class="no-consultation-selected">
          <el-empty :description="getEmptyDescription()" />
        </div>
      </div>

      <!-- 右侧诊断模板区域 -->
      <div class="right-panel">
        <DiagnosisTemplatePanel
          v-if="currentConsultation"
          :modality="currentConsultation.patientStudy?.modality"
          :body-part="currentConsultation.patientStudy?.organ"
          :current-role="currentRole"
          @template-apply="handleTemplateApply"
          @add-template="handleAddTemplate"
        />
      </div>
    </div>

    <!-- 模板管理对话框 -->
    <el-dialog
      v-model="templateDialogVisible"
      title="诊断模板管理"
      width="90%"
      :before-close="handleTemplateDialogClose"
    >
      <DiagnosisTemplateManagement
        v-if="templateDialogVisible"
        @template-apply="handleTemplateApply"
        @close="templateDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Clock, Edit, Check, Close, User, List, ArrowDown } from '@element-plus/icons-vue'
import ConsultationSearchBar from './components/ConsultationSearchBar.vue'
import ConsultationList from './components/ConsultationList.vue'
import ConsultationTabs from './components/ConsultationTabs.vue'
import DiagnosisTemplatePanel from '../diagnosis/components/TemplatePanel.vue'
import DiagnosisTemplateManagement from '../diagnosis/template/index.vue'
import { 
  listConsultationRequest, 
  acceptConsultationRequest, 
  rejectConsultationRequest, 
  completeConsultationRequest,
  withdrawConsultationRequest,
  getConsultationStatusCount 
} from '@/api/consultation/consultation'

// 响应式数据
const loading = ref(false)
const total = ref(0)
const consultationList = ref([])
const currentConsultation = ref(null)
const consultationTabsRef = ref(null)
const templateDialogVisible = ref(false)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  requestNo: undefined,        // 申请编号
  patientName: undefined,      // 患者姓名
  patientId: undefined,        // 患者ID
  requesterName: undefined,    // 申请医生
  consultantName: undefined,   // 会诊专家
  urgencyLevel: undefined,     // 紧急程度
  status: undefined,           // 会诊状态
  startTime: undefined,        // 开始时间
  endTime: undefined,          // 结束时间
  hospitalName: undefined,     // 医院名称
  examItem: undefined,         // 检查项目
  modality: undefined,         // 检查方式
  // 确保场景筛选参数正确传递
  scenario: undefined,         // 场景筛选参数
  currentRole: undefined       // 当前角色参数
})

// 角色和权限相关
const currentRole = ref('consultant') // 当前角色：requester | consultant
const quickFilter = ref('pending') // 快速筛选
const userPermissions = ref({
  canAccept: true,    // 能否接受会诊
  canReject: true,    // 能否拒绝会诊
  canComplete: true,  // 能否完成会诊
  canDiagnose: true,  // 能否写诊断
  canEdit: true       // 能否编辑
})

// 场景状态统计 - 直接使用接口返回的key
const scenarioStatusCounts = reactive({
  pendingCount: 0,      // 待接受/我的待会诊
  acceptedCount: 0,     // 进行中/诊断中
  completedCount: 0,    // 已完成
  rejectedCount: 0,     // 已拒绝
  cancelledCount: 0,    // 已取消
  withdrawnCount: 0,    // 已撤回
  totalCount: 0         // 总数
})

// 会诊状态常量
const CONSULTATION_STATUS = {
  PENDING: 'PENDING',       // 待接受
  ACCEPTED: 'ACCEPTED',     // 已接受/进行中
  COMPLETED: 'COMPLETED',   // 已完成
  REJECTED: 'REJECTED',     // 已拒绝
  CANCELLED: 'CANCELLED',   // 已取消
  WITHDRAWN: 'WITHDRAWN'    // 已撤回
}

// 获取会诊列表
const getConsultationList = async () => {
  loading.value = true
  try {
    // 构建请求参数，过滤undefined值
    const params = {}
    Object.keys(queryParams).forEach(key => {
      const value = queryParams[key]
      if (value !== undefined && value !== null && value !== '') {
        params[key] = value
      }
    })

    // 确保角色参数正确传递
    params.currentRole = currentRole.value

    // 场景筛选逻辑
    if (quickFilter.value && quickFilter.value !== 'all') {
      params.scenario = quickFilter.value
    }

    // 同时获取列表数据和统计数据
    const [listRes, statsRes] = await Promise.all([
      listConsultationRequest(params),
      getScenarioStatusCounts()
    ])

    if (listRes.code === 200) {
      consultationList.value = listRes.rows || []
      total.value = listRes.total || 0

      // 特别调试紧急程度筛选结果
      if (queryParams.urgencyLevel) {
        const returnedUrgencyLevels = consultationList.value.map(item => item.urgencyLevel)
        const uniqueLevels = [...new Set(returnedUrgencyLevels)]
        const isMatched = returnedUrgencyLevels.every(level => level === queryParams.urgencyLevel)
        
        console.log('🔍 紧急程度筛选结果分析:', {
          searchUrgencyLevel: queryParams.urgencyLevel,
          totalResults: total.value,
          dataLength: consultationList.value.length,
          returnedUniqueUrgencyLevels: uniqueLevels,
          isAllMatched: isMatched,
          mismatchCount: returnedUrgencyLevels.filter(level => level !== queryParams.urgencyLevel).length,
          sampleData: consultationList.value.slice(0, 5).map(item => ({
            id: item.id,
            requestNo: item.requestNo,
            urgencyLevel: item.urgencyLevel,
            patientName: item.patientName
          })),
          finalApiParams: params
        })
        
        if (!isMatched) {
          console.error('❌ 后端API筛选错误！返回的数据与搜索条件不匹配', {
            expected: queryParams.urgencyLevel,
            actualLevels: uniqueLevels,
            apiEndpoint: '/consultation/request/list',
            suggestion: '请检查后端API的urgencyLevel参数处理逻辑'
          })
        }
      }

      // 调试信息
      if (quickFilter.value === 'all') {
        console.log('全部会诊数据加载:', {
          total: total.value,
          dataLength: consultationList.value.length,
          params: params
        })
      }
    } else {
      ElMessage.error(listRes.msg || '获取会诊列表失败')
      consultationList.value = []
      total.value = 0
    }

    // 更新状态统计
    if (statsRes) {
      Object.assign(scenarioStatusCounts, statsRes)
    }

  } catch (error) {
    console.error('获取会诊列表出错', error)
    ElMessage.error('系统错误，请联系管理员')
    consultationList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 获取场景状态统计
const getScenarioStatusCounts = async () => {
  try {
    // 构建统计查询参数
    const statsParams = { currentRole: currentRole.value }
    Object.keys(queryParams).forEach(key => {
      if (!['pageNum', 'pageSize', 'status', 'scenario'].includes(key)) {
        const value = queryParams[key]
        if (value !== undefined && value !== null && value !== '') {
          statsParams[key] = value
        }
      }
    })

    const res = await getConsultationStatusCount(statsParams)
    
    if (res.code === 200 && res.data) {
      return res.data // 直接返回接口数据
    }
  } catch (error) {
    console.warn('获取统计数据失败:', error)
  }

  // 失败时返回空统计
  return {
    pendingCount: 0,
    acceptedCount: 0,
    completedCount: 0,
    rejectedCount: 0,
    cancelledCount: 0,
    withdrawnCount: 0,
    totalCount: 0
  }
}


// 处理搜索
const handleSearch = (localQueryParams) => {
  Object.assign(queryParams, localQueryParams)
  queryParams.pageNum = 1
  getConsultationList()
}

// 处理重置
const handleReset = () => {
  // 重置所有查询参数为undefined，但保留分页参数
  Object.keys(queryParams).forEach(key => {
    if (key !== 'pageNum' && key !== 'pageSize') {
      queryParams[key] = undefined
    }
  })
  queryParams.pageNum = 1

  // 清空当前选中的会诊
  currentConsultation.value = null

  // 重置后重新应用当前的快速筛选
  applyQuickFilter()
}

// 应用快速筛选逻辑
const applyQuickFilter = () => {
  console.log('🔄 应用快速筛选 - 开始:', {
    beforeParams: { ...queryParams },
    quickFilter: quickFilter.value,
    userUrgencyLevel: queryParams.urgencyLevel,
    timestamp: new Date().toLocaleTimeString()
  })
  
  // 保存用户设置的紧急程度，避免被清除
  const userUrgencyLevel = queryParams.urgencyLevel
  
  // 清除旧的状态筛选（但保留用户筛选条件）
  queryParams.status = undefined
  queryParams.scenario = undefined

  // 根据快速筛选设置状态
  if (quickFilter.value && quickFilter.value !== 'all') {
    // 状态映射简化 - 直接使用状态名
    const statusMapping = {
      'pending': 'PENDING',
      'accepted': 'ACCEPTED', 
      'completed': 'COMPLETED',
      'rejected': 'REJECTED',
      'cancelled': 'CANCELLED',
      'withdrawn': 'WITHDRAWN'
    }
    
    // 设置对应的状态
    const mappedStatus = statusMapping[quickFilter.value]
    if (mappedStatus) {
      queryParams.status = mappedStatus
    }
  }
  
  // 恢复用户设置的紧急程度（重要！）
  if (userUrgencyLevel) {
    queryParams.urgencyLevel = userUrgencyLevel
    console.log('🔄 恢复用户紧急程度设置:', userUrgencyLevel)
  }
  
  console.log('🔄 应用快速筛选 - 完成:', {
    afterParams: { ...queryParams },
    preservedUrgencyLevel: queryParams.urgencyLevel
  })

  getConsultationList()
}

// 设置快速筛选
const setQuickFilter = (filter) => {
  quickFilter.value = filter
  queryParams.pageNum = 1
  applyQuickFilter()
}

// 处理角色切换
const handleRoleChange = (role) => {
  console.log('🔄 角色切换:', { from: currentRole.value, to: role })
  currentRole.value = role

  // 角色切换时重置统计数据
  Object.keys(scenarioStatusCounts).forEach(key => {
    scenarioStatusCounts[key] = 0
  })

  // 角色切换时重新计算统计数据
  queryParams.pageNum = 1

  // 根据角色调整默认筛选
  setQuickFilter('pending')
}

// 获取当前筛选标签
const getFilterLabel = () => {
  if (quickFilter.value === 'all') {
    return '全部记录'
  }
  
  const labels = {
    'pending': currentRole.value === 'requester' ? '我的待会诊' : '待接受',
    'accepted': currentRole.value === 'requester' ? '进行中' : '诊断中',
    'completed': '已完成',
    'rejected': '已拒绝',
    'cancelled': '已取消',
    'withdrawn': '已撤回'
  }
  
  return labels[quickFilter.value] || '未知状态'
}

// 获取总数
const getTotalCount = () => {
  if (quickFilter.value === 'all') {
    return scenarioStatusCounts.totalCount || total.value
  }
  
  const countMapping = {
    'pending': 'pendingCount',
    'accepted': 'acceptedCount',
    'completed': 'completedCount', 
    'rejected': 'rejectedCount',
    'cancelled': 'cancelledCount',
    'withdrawn': 'withdrawnCount'
  }
  
  const countKey = countMapping[quickFilter.value]
  return countKey ? scenarioStatusCounts[countKey] || 0 : 0
}

// 处理查询参数更新
const handleUpdateQueryParams = (newParams) => {
  console.log('查询参数更新:', {
    oldParams: { ...queryParams },
    newParams: newParams,
    urgencyLevelInNew: newParams.urgencyLevel,
    mergingResult: { ...queryParams, ...newParams },
    timestamp: new Date().toLocaleTimeString()
  })
  
  // 特别保护urgencyLevel参数
  const oldUrgencyLevel = queryParams.urgencyLevel
  Object.assign(queryParams, newParams)
  
  // 检查是否被意外覆盖
  if (oldUrgencyLevel && newParams.urgencyLevel && oldUrgencyLevel !== newParams.urgencyLevel) {
    console.warn('⚠️ urgencyLevel参数被覆盖:', {
      from: oldUrgencyLevel,
      to: newParams.urgencyLevel,
      source: '来源待确认'
    })
  }
  
  // 验证更新后的结果
  console.log('参数更新完成后验证:', {
    finalQueryParams: { ...queryParams },
    finalUrgencyLevel: queryParams.urgencyLevel
  })
}

// 处理会诊选择
const handleConsultationSelect = async (consultation) => {
  console.log('选择会诊:', consultation)
  currentConsultation.value = consultation
}

// 处理分页变化
const handlePageChange = (page) => {
  queryParams.pageNum = page
  getConsultationList()
}

// 处理页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.pageNum = 1
  getConsultationList()
}

// 处理会诊接受
const handleConsultationAccept = async (acceptData) => {
  try {
    const res = await acceptConsultationRequest(currentConsultation.value.id, acceptData)
    if (res.code === 200) {
      ElMessage.success('会诊接受成功')
      // 更新当前会诊状态
      currentConsultation.value.status = CONSULTATION_STATUS.ACCEPTED
      // 刷新列表
      getConsultationList()
    } else {
      ElMessage.error(res.msg || '会诊接受失败')
    }
  } catch (error) {
    console.error('会诊接受失败:', error)
    ElMessage.error('会诊接受失败')
  }
}

// 处理会诊拒绝
const handleConsultationReject = async (rejectData) => {
  try {
    const res = await rejectConsultationRequest(currentConsultation.value.id, rejectData)
    if (res.code === 200) {
      ElMessage.success('会诊拒绝成功')
      // 更新当前会诊状态
      currentConsultation.value.status = CONSULTATION_STATUS.REJECTED
      // 刷新列表
      getConsultationList()
    } else {
      ElMessage.error(res.msg || '会诊拒绝失败')
    }
  } catch (error) {
    console.error('会诊拒绝失败:', error)
    ElMessage.error('会诊拒绝失败')
  }
}

// 处理会诊完成
const handleConsultationComplete = async (completeData) => {
  try {
    const res = await completeConsultationRequest(currentConsultation.value.id, completeData)
    if (res.code === 200) {
      ElMessage.success('会诊完成成功')
      // 更新当前会诊状态
      currentConsultation.value.status = CONSULTATION_STATUS.COMPLETED
      // 刷新列表
      getConsultationList()
    } else {
      ElMessage.error(res.msg || '会诊完成失败')
    }
  } catch (error) {
    console.error('会诊完成失败:', error)
    ElMessage.error('会诊完成失败')
  }
}

// 处理会诊撤回
const handleConsultationWithdraw = async (withdrawData) => {
  try {
    console.log('撤回会诊申请:', withdrawData)
    
    const res = await withdrawConsultationRequest(currentConsultation.value.id, withdrawData)
    if (res.code === 200) {
      ElMessage.success('会诊撤回成功')
      // 更新当前会诊状态
      if (currentConsultation.value) {
        currentConsultation.value.status = CONSULTATION_STATUS.WITHDRAWN
        // 添加撤回相关信息到会诊数据
        currentConsultation.value.withdrawTime = withdrawData.withdrawTime
        currentConsultation.value.withdrawBy = withdrawData.withdrawBy
        currentConsultation.value.withdrawReason = withdrawData.reason
      }
      // 刷新列表以反映状态变化
      getConsultationList()
    } else {
      ElMessage.error(res.msg || '会诊撤回失败')
    }
  } catch (error) {
    console.error('会诊撤回失败:', error)
    ElMessage.error('会诊撤回失败')
  }
}

// 处理诊断保存
const handleDiagnosisSave = (diagnosisData) => {
  console.log('诊断保存:', diagnosisData)
  // 更新当前会诊的诊断数据
  if (currentConsultation.value) {
    currentConsultation.value.diagnosis = { ...currentConsultation.value.diagnosis, ...diagnosisData }
  }
}

// 处理模板应用
const handleTemplateApply = (template) => {
  console.log('应用诊断模板:', template)

  // 通过ref调用ConsultationTabs组件的应用模板方法
  if (consultationTabsRef.value) {
    consultationTabsRef.value.applyTemplate(template)
    // 关闭模板对话框
    templateDialogVisible.value = false
  } else {
    ElMessage.warning('请先选择会诊')
  }
}

// 关闭模板对话框
const handleTemplateDialogClose = () => {
  templateDialogVisible.value = false
}

// 处理添加模板
const handleAddTemplate = () => {
  if (!currentConsultation.value) {
    ElMessage.warning('请先选择会诊')
    return
  }

  if (!consultationTabsRef.value) {
    ElMessage.warning('请先切换到诊断页面')
    return
  }

  // 调用ConsultationTabs的保存为模板功能
  consultationTabsRef.value.handleSaveAsTemplate()
}

// 根据角色获取空状态描述
const getEmptyDescription = () => {
  if (currentRole.value === 'requester') {
    return '请选择会诊申请查看详情'
  } else {
    return '请选择会诊进行处理'
  }
}

// 监听角色变化更新权限
watch(currentRole, (newRole) => {
  if (newRole === 'requester') {
    userPermissions.value = {
      canAccept: false,
      canReject: false,
      canComplete: false,
      canDiagnose: false,
      canEdit: true
    }
  } else {
    userPermissions.value = {
      canAccept: true,
      canReject: true,
      canComplete: true,
      canDiagnose: true,
      canEdit: true
    }
  }
})

// 组件挂载时初始化
onMounted(() => {
  console.log('📱 会诊工作台组件挂载')
  // 从localStorage恢复角色选择
  const savedRole = localStorage.getItem('consultation-workspace-role')
  if (savedRole && ['requester', 'consultant'].includes(savedRole)) {
    currentRole.value = savedRole
    console.log('📱 恢复保存的角色:', savedRole)
  }

  handleRoleChange(currentRole.value)
})

// 保存角色选择到localStorage
watch(currentRole, (newRole) => {
  localStorage.setItem('consultation-workspace-role', newRole)
})

// 临时API测试工具 - 用于调试后端API
const testUrgencyLevelAPI = async () => {
  const testCases = ['URGENT', 'NORMAL', 'LOW']
  
  console.log('🧪 开始API测试 - 紧急程度筛选')
  
  for (const urgencyLevel of testCases) {
    try {
      const testParams = {
        pageNum: 1,
        pageSize: 5,
        urgencyLevel: urgencyLevel,
        currentRole: currentRole.value
      }
      
      console.log(`\n📋 测试紧急程度: ${urgencyLevel}`)
      console.log('请求参数:', testParams)
      
      const res = await listConsultationRequest(testParams)
      
      if (res.code === 200) {
        const returnedLevels = res.rows.map(item => item.urgencyLevel)
        const uniqueLevels = [...new Set(returnedLevels)]
        const isCorrect = returnedLevels.every(level => level === urgencyLevel)
        
        console.log(`✅ 测试结果 (${urgencyLevel}):`, {
          expected: urgencyLevel,
          totalCount: res.total,
          returnedCount: res.rows.length,
          uniqueLevelsReturned: uniqueLevels,
          isCorrect: isCorrect,
          firstFewResults: res.rows.slice(0, 3).map(item => ({
            id: item.id,
            urgencyLevel: item.urgencyLevel,
            requestNo: item.requestNo
          }))
        })
      } else {
        console.error(`❌ API调用失败 (${urgencyLevel}):`, res.msg)
      }
    } catch (error) {
      console.error(`❌ API测试出错 (${urgencyLevel}):`, error)
    }
  }
  
  console.log('🧪 API测试完成')
}

// 临时测试UI搜索的完整参数
const testUISearchParams = async () => {
  console.log('🎯 测试UI搜索的完整参数（包含快速筛选）')
  
  const uiParams = {}
  Object.keys(queryParams).forEach(key => {
    const value = queryParams[key]
    if (value !== undefined && value !== null && value !== '') {
      uiParams[key] = value
    }
  })
  
  // 添加角色和快速筛选参数（模拟UI搜索）
  uiParams.currentRole = currentRole.value
  if (quickFilter.value && quickFilter.value !== 'all') {
    uiParams.scenario = quickFilter.value
  }
  
  console.log('UI搜索参数:', uiParams)
  
  try {
    const res = await listConsultationRequest(uiParams)
    if (res.code === 200) {
      const returnedLevels = res.rows.map(item => item.urgencyLevel)
      const uniqueLevels = [...new Set(returnedLevels)]
      
      console.log('🎯 UI搜索测试结果:', {
        requestedUrgencyLevel: uiParams.urgencyLevel,
        hasQuickFilter: !!uiParams.scenario,
        quickFilterValue: uiParams.scenario,
        totalResults: res.total,
        returnedUrgencyLevels: uniqueLevels,
        sampleData: res.rows.slice(0, 3).map(item => ({
          urgencyLevel: item.urgencyLevel,
          status: item.status,
          requestNo: item.requestNo
        }))
      })
    }
  } catch (error) {
    console.error('UI搜索测试出错:', error)
  }
}

// 手动刷新统计数据 - 调试工具
const refreshStatsManually = async () => {
  console.log('🔄 手动刷新统计数据')
  const stats = await getScenarioStatusCounts()
  Object.assign(scenarioStatusCounts, stats)
  console.log('✅ 统计数据刷新完成:', { ...scenarioStatusCounts })
}

// 临时添加到window对象，方便在控制台调用
if (typeof window !== 'undefined') {
  window.testUrgencyAPI = testUrgencyLevelAPI
  window.testUISearch = testUISearchParams
  window.refreshStats = refreshStatsManually
}

// 暴露方法给父组件
defineExpose({
  refresh: getConsultationList,
  selectConsultation: handleConsultationSelect,
  testUrgencyAPI: testUrgencyLevelAPI  // 临时调试工具
})
</script>

<style scoped>
.consultation-workspace {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.top-search-bar {
  background: white;
  padding: 8px 12px;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.left-panel {
  width: 350px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
  max-height: 85vh;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e4e7ed;
  min-height: 40px;
}

.role-switch-compact .el-radio-group {
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.role-switch-compact .el-radio-button__inner {
  padding: 6px 12px;
  font-size: 12px;
}

.quick-filters-compact {
  position: relative;
}

.filter-badge-compact {
  position: absolute;
  top: -8px;
  right: -8px;
}

.filter-badge-compact :deep(.el-badge__content) {
  background-color: #f56c6c;
  border: 1px solid #f56c6c;
  font-size: 11px;
  height: 16px;
  line-height: 14px;
  min-width: 16px;
  padding: 0 4px;
}

.menu-badge {
  float: right;
  margin-left: 8px;
}

.menu-badge :deep(.el-badge__content) {
  background-color: #909399;
  border: 1px solid #909399;
  font-size: 11px;
  height: 16px;
  line-height: 14px;
  min-width: 16px;
  padding: 0 4px;
  position: static;
  transform: none;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 6px;
  padding: 6px;
}

.center-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
  max-height: 85vh;
}

.right-panel {
  width: 280px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: calc(100vh - 120px);
  max-height: 85vh;
}

.no-consultation-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计优化 */
@media (max-width: 1400px) {
  .right-panel {
    width: 250px;
  }

  .panel-header {
    flex-direction: column;
    gap: 8px;
    min-height: auto;
    padding: 10px 12px;
  }

  .role-switch-compact,
  .quick-filters-compact {
    width: 100%;
  }
}

@media (max-width: 1200px) {
  .left-panel {
    width: 300px;
  }
  .right-panel {
    width: 220px;
  }
}

@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
    gap: 8px;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
    height: 200px;
  }
  
  .center-panel {
    flex: 1;
    min-height: 400px;
  }

  .panel-header {
    flex-direction: row;
    justify-content: space-between;
    padding: 8px 12px;
    min-height: 50px;
  }
}

/* 场景菜单项样式 */
.scenario-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.scenario-menu-item span {
  flex: 1;
  text-align: left;
}

.menu-badge {
  margin-left: auto;
}
</style>