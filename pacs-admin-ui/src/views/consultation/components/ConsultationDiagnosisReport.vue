<template>
  <div class="consultation-diagnosis-report">
    <!-- 会诊状态提示 -->
    <div class="status-alert" v-if="showStatusAlert">
      <el-alert
        :title="getStatusAlertTitle()"
        :type="getStatusAlertType()"
        :description="getStatusAlertDescription()"
        show-icon
        :closable="false"
      />
    </div>


    <!-- 复用诊断报告组件 -->
    <DiagnosisReport
      ref="diagnosisReportRef"
      :patient-data="adaptedPatientData"
      :diagnosis-data="adaptedDiagnosisData"
      :current-role="adaptedRole"
      :user-permissions="adaptedPermissions"
      @diagnosis-save="handleDiagnosisSave"
      @diagnosis-submit="handleDiagnosisSubmit"
      @diagnosis-audit="handleDiagnosisAudit"
      @template-apply="handleTemplateApply"
    />

    <!-- 会诊特定操作按钮 -->
    <div class="consultation-specific-actions" v-if="currentRole === 'consultant'">
      <div class="button-group">
        <el-button
          v-if="canEdit && userPermissions.canComplete && consultationData.status === 'ACCEPTED'"
          type="success"
          @click="handleComplete"
          :loading="completeLoading"
          :icon="Check"
        >
          完成会诊
        </el-button>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ChatDotSquare,
  Check,
  Close
} from '@element-plus/icons-vue'
import DiagnosisReport from '../../diagnosis/components/DiagnosisReport.vue'
import useUserStore from '@/store/modules/user'

// Props
const props = defineProps({
  consultationData: {
    type: Object,
    default: () => ({})
  },
  currentRole: {
    type: String,
    default: 'consultant',
    validator: (value) => ['requester', 'consultant'].includes(value)
  },
  userPermissions: {
    type: Object,
    default: () => ({
      canAccept: true,
      canReject: true,
      canComplete: true,
      canDiagnose: true,
      canEdit: true
    })
  }
})

// Emits
const emit = defineEmits([
  'consultation-accept',
  'consultation-reject',
  'consultation-complete',
  'diagnosis-save'
])

// Store
const userStore = useUserStore()

// 响应式数据
const diagnosisReportRef = ref()
const completeLoading = ref(false)

// 计算属性
const canEdit = computed(() => {
  if (props.currentRole === 'requester') return false
  if (props.consultationData.status === 'COMPLETED') return false
  if (props.consultationData.status === 'REJECTED') return false
  if (props.consultationData.status === 'CANCELLED') return false
  return props.userPermissions.canEdit
})

const showStatusAlert = computed(() => {
  return props.consultationData.status && props.consultationData.status !== 'ACCEPTED'
})


// 适配会诊数据为诊断组件所需的患者数据格式
const adaptedPatientData = computed(() => {
  if (!props.consultationData) return {}
  
  return {
    // 患者基本信息
    patientName: props.consultationData.patientName,
    patientSex: props.consultationData.patientSex,
    patientBirthday: props.consultationData.patientBirthday,
    originalPatientId: props.consultationData.patientId,
    inPatientId: props.consultationData.inPatientId,
    bedNo: props.consultationData.bedNo,
    mobile: props.consultationData.mobile,
    
    // 检查信息（从会诊关联的检查数据中获取）
    examCode: props.consultationData.patientStudy?.examCode,
    modality: props.consultationData.patientStudy?.modality,
    organ: props.consultationData.patientStudy?.organ,
    examItem: props.consultationData.examItem,
    checkFinishTime: props.consultationData.patientStudy?.checkFinishTime,
    examDepartment: props.consultationData.patientStudy?.examDepartment,
    examDoctorName: props.consultationData.patientStudy?.examDoctorName,
    hospitalName: props.consultationData.hospitalName,
    
    // 其他必要字段
    id: props.consultationData.patientStudy?.id || props.consultationData.id
  }
})

// 适配会诊诊断数据为诊断组件所需格式
const adaptedDiagnosisData = computed(() => {
  if (!props.consultationData.diagnosis) return {}
  
  return {
    id: props.consultationData.diagnosis.id,
    studyId: props.consultationData.patientStudy?.id,
    checkId: props.consultationData.patientStudy?.id,
    // 将会诊意见映射为影像所见
    diagnose: props.consultationData.diagnosis.consultationOpinion || '',
    // 诊断建议保持不变
    recommendation: props.consultationData.diagnosis.recommendation || '',
    // 其他字段
    positiveNegative: props.consultationData.diagnosis.positiveNegative || '',
    doctor: props.consultationData.consultantName || userStore.name,
    createTime: props.consultationData.diagnosis.consultTime,
    auditBy: props.consultationData.diagnosis.auditBy,
    auditTime: props.consultationData.diagnosis.auditTime,
    status: props.consultationData.diagnosis.status || '0'
  }
})

// 适配角色 - 会诊专家对应诊断医生
const adaptedRole = computed(() => {
  return props.currentRole === 'consultant' ? 'diagnoser' : 'auditor'
})

// 适配权限
const adaptedPermissions = computed(() => {
  return {
    canDiagnose: props.userPermissions.canDiagnose && props.currentRole === 'consultant',
    canAudit: props.userPermissions.canEdit && props.currentRole === 'requester',
    canEdit: canEdit.value,
    canDelete: false
  }
})

// 获取状态提示标题
const getStatusAlertTitle = () => {
  const statusMap = {
    'PENDING': '待接受会诊',
    'COMPLETED': '会诊已完成',
    'REJECTED': '会诊已拒绝',
    'CANCELLED': '会诊已取消'
  }
  return statusMap[props.consultationData.status] || '未知状态'
}

// 获取状态提示类型
const getStatusAlertType = () => {
  const typeMap = {
    'PENDING': 'warning',
    'COMPLETED': 'success',
    'REJECTED': 'error',
    'CANCELLED': 'info'
  }
  return typeMap[props.consultationData.status] || 'info'
}

// 获取状态提示描述
const getStatusAlertDescription = () => {
  const descMap = {
    'PENDING': '该会诊申请正在等待专家接受处理',
    'COMPLETED': '该会诊已完成，可查看完整的会诊报告',
    'REJECTED': '该会诊申请已被拒绝，请查看拒绝原因',
    'CANCELLED': '该会诊申请已被取消'
  }
  return descMap[props.consultationData.status] || ''
}

// 处理诊断保存 - 适配从 DiagnosisReport 来的事件
const handleDiagnosisSave = (diagnosisData) => {
  // 将诊断数据转换为会诊数据格式
  const consultationDiagnosisData = {
    consultationId: props.consultationData.id,
    consultationOpinion: diagnosisData.diagnose, // 影像所见 -> 会诊意见
    recommendation: diagnosisData.recommendation,
    treatmentAdvice: diagnosisData.treatmentAdvice,
    consultant: userStore.name,
    consultTime: new Date().toISOString()
  }
  
  emit('diagnosis-save', consultationDiagnosisData)
}

// 处理诊断提交 - 适配从 DiagnosisReport 来的事件
const handleDiagnosisSubmit = (diagnosisData) => {
  // 对于会诊，提交相当于保存
  handleDiagnosisSave(diagnosisData)
}

// 处理诊断审核 - 适配从 DiagnosisReport 来的事件
const handleDiagnosisAudit = (diagnosisData) => {
  // 对于会诊，审核功能暂时等同于保存
  handleDiagnosisSave(diagnosisData)
}

// 处理模板应用 - 透传给 DiagnosisReport
const handleTemplateApply = (template) => {
  emit('template-apply', template)
}



// 处理完成会诊
const handleComplete = async () => {
  try {
    const result = await ElMessageBox.confirm(
      '确认完成该会诊吗？完成后将无法再修改会诊内容。',
      '确认完成',
      {
        confirmButtonText: '确认完成',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    if (result === 'confirm') {
      completeLoading.value = true
      
      // 获取 DiagnosisReport 组件的当前数据
      const diagnosisData = diagnosisReportRef.value?.getDiagnosisData() || {}
      
      const completeData = {
        consultationId: props.consultationData.id,
        consultationOpinion: diagnosisData.diagnose,
        recommendation: diagnosisData.recommendation,
        consultant: userStore.name,
        completeTime: new Date().toISOString()
      }
      
      emit('consultation-complete', completeData)
    }
  } catch (error) {
    if (error === 'cancel') return
    console.error('完成会诊失败:', error)
    ElMessage.error('完成会诊失败')
  } finally {
    completeLoading.value = false
  }
}


// 应用模板 - 透传给 DiagnosisReport 组件
const applyTemplate = (template) => {
  if (diagnosisReportRef.value) {
    diagnosisReportRef.value.applyTemplate(template)
  }
}

// 获取诊断数据 - 从 DiagnosisReport 组件获取
const getDiagnosisData = () => {
  if (diagnosisReportRef.value) {
    return diagnosisReportRef.value.getDiagnosisData()
  }
  return {}
}

// 暴露方法
defineExpose({
  applyTemplate,
  getDiagnosisData
})
</script>

<style scoped>
.consultation-diagnosis-report {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.status-alert {
  margin-bottom: 16px;
}


.consultation-specific-actions {
  padding: 16px;
  background: #fafbfc;
  border-top: 1px solid #e4e7ed;
  text-align: center;
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .consultation-actions {
    padding: 12px;
  }
  
  .action-buttons,
  .button-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-buttons .el-button,
  .button-group .el-button {
    margin-bottom: 8px;
  }
  
  .action-buttons .el-button:last-child,
  .button-group .el-button:last-child {
    margin-bottom: 0;
  }
}
</style>