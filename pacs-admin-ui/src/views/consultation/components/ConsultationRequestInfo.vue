<template>
  <div class="consultation-request-info">
    <div class="info-container">
      <!-- 申请信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><DocumentAdd /></el-icon>
            <span class="header-title">申请信息</span>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">申请编号</label>
              <span class="info-value">{{ consultationData.requestNo || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">申请医生</label>
              <span class="info-value">{{ consultationData.requesterName || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">申请科室</label>
              <span class="info-value">{{ consultationData.requesterDepartment || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">申请时间</label>
              <span class="info-value">{{ formatDateTime(consultationData.createTime) }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">会诊专家</label>
              <span class="info-value">{{ consultationData.consultantName || '待指定' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">专家科室</label>
              <span class="info-value">{{ consultationData.consultantDepartment || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">期望时间</label>
              <span class="info-value">{{ formatDateTime(consultationData.expectedTime) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">会诊状态</label>
              <el-tag 
                :type="getConsultationStatusType(consultationData.status)" 
                size="small"
                class="status-tag"
              >
                {{ getConsultationStatusText(consultationData.status) }}
              </el-tag>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">紧急程度</label>
              <el-tag 
                :type="getUrgencyTagType(consultationData.urgencyLevel)" 
                size="small"
                class="urgency-tag"
              >
                {{ getUrgencyText(consultationData.urgencyLevel) }}
              </el-tag>
            </div>
            <div class="info-item">
              <label class="info-label">完成时间</label>
              <span class="info-value">{{ formatDateTime(consultationData.completionTime) }}</span>
            </div>
            <div class="info-item" v-if="consultationData.status === 'WITHDRAWN'">
              <label class="info-label">撤回时间</label>
              <span class="info-value">{{ formatDateTime(consultationData.withdrawTime) }}</span>
            </div>
            <div class="info-item" v-if="consultationData.status === 'WITHDRAWN'">
              <label class="info-label">撤回原因</label>
              <span class="info-value">{{ consultationData.withdrawReason || '-' }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 申请原因卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><ChatLineSquare /></el-icon>
            <span class="header-title">申请原因</span>
          </div>
        </template>
        
        <div class="request-content">
          <div class="request-item">
            <label class="request-label">申请原因：</label>
            <p class="request-text">{{ consultationData.requestReason || '无' }}</p>
          </div>
          <div class="request-item" v-if="consultationData.clinicalQuestion">
            <label class="request-label">病情描述：</label>
            <p class="request-text">{{ consultationData.requestDescription }}</p>
          </div>
        </div>
      </el-card>

      <!-- 临床资料卡片 -->
      <el-card class="info-card" shadow="never" v-if="consultationData.clinicalData">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Memo /></el-icon>
            <span class="header-title">临床资料</span>
          </div>
        </template>
        
        <div class="clinical-content">
          <div class="clinical-item" v-if="consultationData.clinicalData.symptoms">
            <label class="clinical-label">主要症状：</label>
            <p class="clinical-text">{{ consultationData.clinicalData.symptoms }}</p>
          </div>
          <div class="clinical-item" v-if="consultationData.clinicalData.history">
            <label class="clinical-label">病史摘要：</label>
            <p class="clinical-text">{{ consultationData.clinicalData.history }}</p>
          </div>
          <div class="clinical-item" v-if="consultationData.clinicalData.physicalExam">
            <label class="clinical-label">体格检查：</label>
            <p class="clinical-text">{{ consultationData.clinicalData.physicalExam }}</p>
          </div>
          <div class="clinical-item" v-if="consultationData.clinicalData.labResults">
            <label class="clinical-label">实验室检查：</label>
            <p class="clinical-text">{{ consultationData.clinicalData.labResults }}</p>
          </div>
          <div class="clinical-item" v-if="consultationData.clinicalData.otherExams">
            <label class="clinical-label">其他检查：</label>
            <p class="clinical-text">{{ consultationData.clinicalData.otherExams }}</p>
          </div>
        </div>
      </el-card>

      <!-- 附件资料卡片 -->
      <el-card class="info-card" shadow="never" v-if="consultationData.attachments && consultationData.attachments.length > 0">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Paperclip /></el-icon>
            <span class="header-title">附件资料</span>
          </div>
        </template>
        
        <div class="attachments-content">
          <div class="attachment-list">
            <div 
              v-for="(attachment, index) in consultationData.attachments" 
              :key="index"
              class="attachment-item"
            >
              <div class="attachment-info">
                <el-icon class="attachment-icon">
                  <component :is="getFileIcon(attachment.type)" />
                </el-icon>
                <div class="attachment-details">
                  <span class="attachment-name">{{ attachment.name }}</span>
                  <span class="attachment-meta">{{ formatFileSize(attachment.size) }} · {{ formatDateTime(attachment.uploadTime) }}</span>
                </div>
              </div>
              <div class="attachment-actions">
                <el-button size="small" type="primary" link @click="downloadAttachment(attachment)">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
                <el-button size="small" type="primary" link @click="previewAttachment(attachment)" v-if="canPreview(attachment.type)">
                  <el-icon><View /></el-icon>
                  预览
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 会诊处理卡片 (仅会诊专家在待接受状态下可见) -->
      <el-card class="info-card consultation-actions-card" shadow="hover" v-if="shouldShowActions">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <div class="header-icon-wrapper">
                <el-icon class="header-icon"><ChatDotSquare /></el-icon>
              </div>
              <div class="header-text">
                <span class="header-title">会诊处理</span>
                <span class="header-subtitle">请选择处理方式</span>
              </div>
            </div>
            <div class="header-badge">
              <el-tag type="warning" size="small" effect="plain">待处理</el-tag>
            </div>
          </div>
        </template>
        
        <div class="consultation-actions-content">
          <div class="action-buttons">
            <el-button 
              type="success" 
              size="large"
              @click="handleAccept"
              :loading="actionLoading"
              :icon="Check"
              class="action-btn accept-btn"
            >
              <span class="btn-text">
                <strong>接受会诊</strong>
                <small>开始进行会诊诊断</small>
              </span>
            </el-button>
            <el-button 
              type="danger" 
              size="large"
              @click="showRejectDialog = true"
              :loading="actionLoading"
              :icon="Close"
              class="action-btn reject-btn"
            >
              <span class="btn-text">
                <strong>拒绝会诊</strong>
                <small>说明拒绝原因</small>
              </span>
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 申请医生撤回操作卡片 (仅申请医生在待接受状态下可见) -->
      <el-card class="info-card withdraw-actions-card" shadow="hover" v-if="shouldShowWithdrawActions">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <div class="header-icon-wrapper">
                <el-icon class="header-icon"><RefreshLeft /></el-icon>
              </div>
              <div class="header-text">
                <span class="header-title">撤回申请</span>
                <span class="header-subtitle">您可以撤回此会诊申请</span>
              </div>
            </div>
            <div class="header-badge">
              <el-tag type="info" size="small" effect="plain">可撤回</el-tag>
            </div>
          </div>
        </template>
        
        <div class="withdraw-actions-content">
          <div class="action-buttons">
            <el-button 
              type="warning" 
              size="large"
              @click="showWithdrawDialog = true"
              :loading="actionLoading"
              :icon="RefreshLeft"
              class="action-btn withdraw-btn"
            >
              <span class="btn-text">
                <strong>撤回申请</strong>
                <small>撤回此会诊申请</small>
              </span>
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 影像资料卡片 -->
      <el-card class="info-card" shadow="never" v-if="consultationData.patientStudy">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Picture /></el-icon>
            <span class="header-title">影像资料</span>
          </div>
        </template>
        
        <div class="imaging-content">
          <div class="imaging-info">
            <div class="info-row">
              <div class="info-item">
                <label class="info-label">检查编号</label>
                <span class="info-value">{{ consultationData.patientStudy.examCode || '-' }}</span>
              </div>
              <div class="info-item">
                <label class="info-label">检查类型</label>
                <el-tag 
                  :type="getModalityTagType(consultationData.patientStudy.modality)" 
                  size="small"
                  class="modality-tag"
                >
                  {{ consultationData.patientStudy.modality || '-' }}
                </el-tag>
              </div>
              <div class="info-item">
                <label class="info-label">检查部位</label>
                <span class="info-value">{{ consultationData.patientStudy.organ || '-' }}</span>
              </div>
              <div class="info-item">
                <label class="info-label">检查时间</label>
                <span class="info-value">{{ formatDateTime(consultationData.patientStudy.checkFinishTime) }}</span>
              </div>
            </div>
            
            <div class="imaging-actions">
              <el-button type="primary" @click="viewImages" v-if="consultationData.patientStudy.dicomSyncFlag === 1">
                <el-icon><View /></el-icon>
                查看影像
              </el-button>
              <el-button type="info" disabled v-else>
                <el-icon><Loading /></el-icon>
                影像同步中
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 拒绝会诊对话框 -->
    <el-dialog
      v-model="showRejectDialog"
      title="拒绝会诊"
      width="500px"
      :before-close="handleRejectDialogClose"
    >
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="拒绝原因" required>
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showRejectDialog = false">取消</el-button>
          <el-button type="danger" @click="handleReject" :loading="actionLoading">
            确认拒绝
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 撤回申请对话框 -->
    <el-dialog
      v-model="showWithdrawDialog"
      title="撤回会诊申请"
      width="500px"
      :before-close="handleWithdrawDialogClose"
    >
      <el-form :model="withdrawForm" label-width="80px">
        <el-form-item label="撤回原因" required>
          <el-input
            v-model="withdrawForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入撤回原因..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showWithdrawDialog = false">取消</el-button>
          <el-button type="warning" @click="handleWithdraw" :loading="actionLoading">
            确认撤回
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  DocumentAdd, 
  ChatLineSquare, 
  ChatDotSquare,
  Memo, 
  Paperclip, 
  Picture,
  Download,
  View,
  Loading,
  Document,
  VideoPlay,
  Picture as PictureIcon,
  Check,
  Close,
  InfoFilled,
  RefreshLeft
} from '@element-plus/icons-vue'
import useUserStore from '@/store/modules/user'

// Props
const props = defineProps({
  consultationData: {
    type: Object,
    default: () => ({})
  },
  currentRole: {
    type: String,
    default: 'consultant',
    validator: (value) => ['requester', 'consultant'].includes(value)
  },
  userPermissions: {
    type: Object,
    default: () => ({
      canAccept: true,
      canReject: true,
      canComplete: true,
      canDiagnose: true,
      canEdit: true
    })
  }
})

// Emits
const emit = defineEmits([
  'consultation-accept',
  'consultation-reject',
  'consultation-withdraw'
])

// Store
const userStore = useUserStore()

// 响应式数据
const actionLoading = ref(false)
const showRejectDialog = ref(false)
const showWithdrawDialog = ref(false)

// 拒绝表单数据
const rejectForm = reactive({
  reason: ''
})

// 撤回表单数据
const withdrawForm = reactive({
  reason: ''
})

// 计算属性 - 是否显示会诊操作按钮
const shouldShowActions = computed(() => {
  // 只有会诊专家在待接受状态下才显示操作按钮
  return props.currentRole === 'consultant' && 
         props.consultationData.status === 'PENDING' &&
         props.userPermissions.canAccept
})

// 计算属性 - 是否显示撤回操作按钮
const shouldShowWithdrawActions = computed(() => {
  // 只有申请医生在待接受状态下才能撤回申请
  return props.currentRole === 'requester' && 
         props.consultationData.status === 'PENDING' &&
         props.userPermissions.canEdit
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return '-'
  }
}

// 获取紧急程度标签类型
const getUrgencyTagType = (urgencyLevel) => {
  const typeMap = {
    'URGENT': 'danger',      // 紧急
    'NORMAL': 'info',        // 普通
    'LOW': 'success'         // 非紧急
  }
  return typeMap[urgencyLevel] || 'info'
}

// 获取紧急程度文本
const getUrgencyText = (urgencyLevel) => {
  const textMap = {
    'URGENT': '紧急',
    'NORMAL': '普通',
    'LOW': '非紧急'
  }
  return textMap[urgencyLevel] || '普通'
}

// 获取会诊状态标签类型
const getConsultationStatusType = (status) => {
  const typeMap = {
    'PENDING': 'warning',
    'ACCEPTED': 'primary',
    'COMPLETED': 'success',
    'REJECTED': 'danger',
    'CANCELLED': 'info',
    'WITHDRAWN': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取会诊状态文本
const getConsultationStatusText = (status) => {
  const textMap = {
    'PENDING': '待接受',
    'ACCEPTED': '进行中',
    'COMPLETED': '已完成',
    'REJECTED': '已拒绝',
    'CANCELLED': '已取消',
    'WITHDRAWN': '已撤回'
  }
  return textMap[status] || '未知状态'
}

// 获取检查类型标签类型
const getModalityTagType = (modality) => {
  const typeMap = {
    'CT': 'primary',
    'MRI': 'success',
    'DR': 'warning',
    'US': 'info'
  }
  return typeMap[modality] || 'default'
}

// 获取文件图标
const getFileIcon = (fileType) => {
  if (!fileType) return Document
  
  const type = fileType.toLowerCase()
  if (type.includes('image') || type.includes('jpg') || type.includes('png') || type.includes('gif')) {
    return PictureIcon
  }
  if (type.includes('video') || type.includes('mp4') || type.includes('avi')) {
    return VideoPlay
  }
  return Document
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 判断是否可以预览
const canPreview = (fileType) => {
  if (!fileType) return false
  
  const previewTypes = ['image/', 'text/', 'application/pdf']
  return previewTypes.some(type => fileType.toLowerCase().includes(type))
}

// 下载附件
const downloadAttachment = (attachment) => {
  // 这里应该实现实际的下载逻辑
  ElMessage.info('下载功能待实现')
  console.log('下载附件:', attachment)
}

// 预览附件
const previewAttachment = (attachment) => {
  // 这里应该实现实际的预览逻辑
  ElMessage.info('预览功能待实现')
  console.log('预览附件:', attachment)
}

// 查看影像
const viewImages = () => {
  // 这里应该实现实际的影像查看逻辑
  ElMessage.info('影像查看功能待实现')
  console.log('查看影像:', props.consultationData.patientStudy)
}

// 处理接受会诊
const handleAccept = async () => {
  try {
    actionLoading.value = true
    
    const acceptData = {
      consultationId: props.consultationData.id,
      acceptTime: new Date().toISOString(),
      acceptBy: userStore.name
    }
    
    emit('consultation-accept', acceptData)
  } catch (error) {
    console.error('接受会诊失败:', error)
    ElMessage.error('接受会诊失败')
  } finally {
    actionLoading.value = false
  }
}

// 处理拒绝会诊
const handleReject = async () => {
  if (!rejectForm.reason.trim()) {
    ElMessage.warning('请输入拒绝原因')
    return
  }
  
  try {
    actionLoading.value = true
    
    const rejectData = {
      consultationId: props.consultationData.id,
      rejectTime: new Date().toISOString(),
      rejectBy: userStore.name,
      reason: rejectForm.reason
    }
    
    emit('consultation-reject', rejectData)
    showRejectDialog.value = false
    rejectForm.reason = ''
  } catch (error) {
    console.error('拒绝会诊失败:', error)
    ElMessage.error('拒绝会诊失败')
  } finally {
    actionLoading.value = false
  }
}

// 关闭拒绝对话框
const handleRejectDialogClose = () => {
  showRejectDialog.value = false
  rejectForm.reason = ''
}

// 处理撤回会诊申请
const handleWithdraw = async () => {
  if (!withdrawForm.reason.trim()) {
    ElMessage.warning('请输入撤回原因')
    return
  }
  
  try {
    actionLoading.value = true
    
    const withdrawData = {
      consultationId: props.consultationData.id,
      withdrawTime: new Date().toISOString(),
      withdrawBy: userStore.name,
      reason: withdrawForm.reason
    }
    
    emit('consultation-withdraw', withdrawData)
    showWithdrawDialog.value = false
    withdrawForm.reason = ''
  } catch (error) {
    console.error('撤回会诊申请失败:', error)
    ElMessage.error('撤回会诊申请失败')
  } finally {
    actionLoading.value = false
  }
}

// 关闭撤回对话框
const handleWithdrawDialogClose = () => {
  showWithdrawDialog.value = false
  withdrawForm.reason = ''
}
</script>

<style scoped>
.consultation-request-info {
  height: 100%;
  overflow-y: auto;
}

.info-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0;
}

.info-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.info-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
  font-size: 18px;
}

.header-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.info-card :deep(.el-card__body) {
  padding: 16px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #303133;
  font-weight: 400;
  word-break: break-word;
}

.urgency-tag,
.modality-tag,
.status-tag {
  font-size: 12px;
  max-width: fit-content;
}

.request-content,
.clinical-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.request-item,
.clinical-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.request-label,
.clinical-label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.request-text,
.clinical-text {
  font-size: 14px;
  color: #303133;
  line-height: 1.6;
  margin: 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  white-space: pre-wrap;
}

.attachments-content {
  padding: 0;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.attachment-item:hover {
  background: #f0f9ff;
  border-color: #409eff;
}

.attachment-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.attachment-icon {
  color: #409eff;
  font-size: 20px;
}

.attachment-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.attachment-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.attachment-meta {
  font-size: 12px;
  color: #909399;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}

.imaging-content {
  padding: 0;
}

.imaging-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.imaging-actions {
  display: flex;
  justify-content: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .info-container {
    gap: 12px;
  }
  
  .info-card :deep(.el-card__header) {
    padding: 10px 12px;
  }
  
  .info-card :deep(.el-card__body) {
    padding: 12px;
  }
  
  .attachment-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .attachment-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .header-title {
    font-size: 13px;
  }
  
  .info-label {
    font-size: 11px;
  }
  
  .info-value {
    font-size: 13px;
  }
}

/* 卡片悬停效果 */
.info-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

/* 标签样式优化 */
.urgency-tag,
.modality-tag,
.status-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 会诊处理卡片样式 */
.consultation-actions-card {
  border: 1px solid #e1e8ed;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.consultation-actions-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 16px 20px;
  border-bottom: 1px solid #e1e8ed;
}

.consultation-actions-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #1f2937;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon-wrapper {
  width: 36px;
  height: 36px;
  background: #e0f2fe;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.consultation-actions-card .header-icon {
  color: #0369a1;
  font-size: 18px;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.consultation-actions-card .header-title {
  color: #1f2937;
  font-size: 15px;
  font-weight: 600;
  margin: 0;
}

.header-subtitle {
  color: #6b7280;
  font-size: 12px;
  font-weight: 400;
}

.header-badge :deep(.el-tag) {
  background: #fef3c7;
  color: #d97706;
  border: 1px solid #fbbf24;
  font-weight: 500;
  font-size: 11px;
}

.consultation-actions-content {
  padding: 20px;
}

.action-description {
  margin-bottom: 24px;
}

.description-text {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin: 0;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  font-size: 14px;
  color: #374151;
  line-height: 1.6;
}

.info-icon {
  color: #0369a1;
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.action-btn {
  flex: 1;
  max-width: 180px;
  height: 52px;
  border-radius: 8px;
  border: 1px solid;
  transition: all 0.2s ease;
  position: relative;
  font-weight: 500;
}

.btn-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  line-height: 1.2;
}

.btn-text strong {
  font-size: 14px;
  font-weight: 600;
}

.btn-text small {
  font-size: 11px;
  opacity: 0.85;
  font-weight: 400;
}

.accept-btn {
  background: #ffffff;
  color: #059669;
  border-color: #059669;
}

.accept-btn:hover {
  background: #f0fdf4;
  color: #047857;
  border-color: #047857;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.15);
}

.reject-btn {
  background: #ffffff;
  color: #dc2626;
  border-color: #dc2626;
}

.reject-btn:hover {
  background: #fef2f2;
  color: #b91c1c;
  border-color: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
}

.withdraw-btn {
  background: #ffffff;
  color: #d97706;
  border-color: #d97706;
}

.withdraw-btn:hover {
  background: #fffbeb;
  color: #b45309;
  border-color: #b45309;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(217, 119, 6, 0.15);
}

.action-btn:active {
  transform: translateY(0);
}

.action-btn:disabled {
  opacity: 0.5;
  transform: none !important;
  box-shadow: none !important;
  cursor: not-allowed;
}

/* 撤回操作卡片样式 */
.withdraw-actions-card {
  border: 1px solid #f3e8ff;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.06);
}

.withdraw-actions-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
  padding: 16px 20px;
  border-bottom: 1px solid #e9d5ff;
}

.withdraw-actions-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #1f2937;
}

.withdraw-actions-card .header-icon-wrapper {
  width: 36px;
  height: 36px;
  background: #fef3c7;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.withdraw-actions-card .header-icon {
  color: #d97706;
  font-size: 18px;
}

.withdraw-actions-card .header-title {
  color: #1f2937;
  font-size: 15px;
  font-weight: 600;
  margin: 0;
}

.withdraw-actions-card .header-subtitle {
  color: #6b7280;
  font-size: 12px;
  font-weight: 400;
}

.withdraw-actions-card .header-badge :deep(.el-tag) {
  background: #e0f2fe;
  color: #0369a1;
  border: 1px solid #bae6fd;
  font-weight: 500;
  font-size: 11px;
}

.withdraw-actions-content {
  padding: 20px;
}

.withdraw-actions-content .description-text {
  border-left: 3px solid #d97706;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 响应式优化 - 会诊处理部分 */
@media (max-width: 768px) {
  .consultation-actions-card :deep(.el-card__header) {
    padding: 14px 16px;
  }
  
  .header-left {
    gap: 10px;
  }
  
  .header-icon-wrapper {
    width: 36px;
    height: 36px;
  }
  
  .consultation-actions-card .header-icon {
    font-size: 18px;
  }
  
  .consultation-actions-card .header-title {
    font-size: 15px;
  }
  
  .header-subtitle {
    font-size: 11px;
  }
  
  .consultation-actions-content {
    padding: 16px;
  }
  
  .action-description {
    margin-bottom: 20px;
  }
  
  .description-text {
    padding: 12px 14px;
    font-size: 13px;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .action-btn {
    max-width: none;
    height: 50px;
  }
  
  .btn-text strong {
    font-size: 14px;
  }
  
  .btn-text small {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .header-badge {
    margin-top: 8px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>