<template>
  <div class="consultation-history">
    <div class="history-container">
      <!-- 时间线 -->
      <el-timeline class="history-timeline">
        <el-timeline-item
          v-for="(item, index) in historyList"
          :key="index"
          :timestamp="formatDateTime(item.createTime)"
          :type="getTimelineType(item.type)"
          :icon="getTimelineIcon(item.type)"
          placement="top"
          class="timeline-item"
        >
          <div class="timeline-content">
            <div class="content-header">
              <h4 class="content-title">{{ item.title }}</h4>
              <el-tag :type="getStatusTagType(item.type)" size="small">
                {{ getStatusText(item.type) }}
              </el-tag>
            </div>
            
            <div class="content-body">
              <div class="content-info">
                <div class="info-item" v-if="item.operator">
                  <span class="info-label">操作人：</span>
                  <span class="info-value">{{ item.operator }}</span>
                </div>
                <div class="info-item" v-if="item.department">
                  <span class="info-label">科室：</span>
                  <span class="info-value">{{ item.department }}</span>
                </div>
                <div class="info-item" v-if="item.duration">
                  <span class="info-label">耗时：</span>
                  <span class="info-value">{{ item.duration }}</span>
                </div>
              </div>
              
              <div class="content-description" v-if="item.description">
                <p>{{ item.description }}</p>
              </div>
              
              <!-- 会诊内容详情 -->
              <div class="consultation-details" v-if="item.details">
                <div class="details-section" v-if="item.details.opinion">
                  <label class="details-label">会诊意见：</label>
                  <div class="details-content">{{ item.details.opinion }}</div>
                </div>
                <div class="details-section" v-if="item.details.recommendation">
                  <label class="details-label">诊断建议：</label>
                  <div class="details-content">{{ item.details.recommendation }}</div>
                </div>
                <div class="details-section" v-if="item.details.treatmentAdvice">
                  <label class="details-label">治疗建议：</label>
                  <div class="details-content">{{ item.details.treatmentAdvice }}</div>
                </div>
                <div class="details-section" v-if="item.details.reason">
                  <label class="details-label">{{ item.type === 'REJECTED' ? '拒绝原因' : '原因' }}：</label>
                  <div class="details-content">{{ item.details.reason }}</div>
                </div>
              </div>
              
              <!-- 附件 -->
              <div class="attachments" v-if="item.attachments && item.attachments.length > 0">
                <div class="attachments-header">
                  <el-icon class="attachments-icon"><Paperclip /></el-icon>
                  <span>相关附件</span>
                </div>
                <div class="attachment-list">
                  <div 
                    v-for="(attachment, aIndex) in item.attachments" 
                    :key="aIndex"
                    class="attachment-item"
                  >
                    <el-icon class="attachment-icon">
                      <component :is="getFileIcon(attachment.type)" />
                    </el-icon>
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <el-button size="small" type="primary" link @click="downloadAttachment(attachment)">
                      <el-icon><Download /></el-icon>
                      下载
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
      
      <!-- 空状态 -->
      <div class="empty-state" v-if="historyList.length === 0">
        <el-empty description="暂无历史记录">
          <el-button type="primary" @click="refreshHistory">刷新记录</el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Paperclip,
  Download,
  Document,
  Picture,
  VideoPlay,
  Plus,
  Check,
  Close,
  Edit,
  User,
  RefreshLeft
} from '@element-plus/icons-vue'
import { getConsultationAuditLogs } from '@/api/consultation/consultation'

// Props
const props = defineProps({
  consultationData: {
    type: Object,
    default: () => ({})
  }
})

// 响应式数据
const historyList = ref([])
const loading = ref(false)

// 计算属性
const consultationId = computed(() => {
  return props.consultationData?.id
})

// 获取审计日志数据
const fetchAuditLogs = async () => {
  if (!consultationId.value) {
    historyList.value = []
    return
  }

  loading.value = true
  try {
    const response = await getConsultationAuditLogs(consultationId.value)
    if (response.code === 200) {
      historyList.value = transformAuditLogs(response.data || [])
    } else {
      console.error('获取审计日志失败:', response.msg)
      // 如果API失败，回退到模拟数据
      historyList.value = mockHistoryData.value
    }
  } catch (error) {
    console.error('获取审计日志错误:', error)
    // 如果API失败，回退到模拟数据
    historyList.value = mockHistoryData.value
  } finally {
    loading.value = false
  }
}

// 转换审计日志数据为历史记录格式
const transformAuditLogs = (auditLogs) => {
  return auditLogs.map(log => {
    const operationTypeMap = {
      'CREATE': { type: 'CREATED', title: '创建会诊申请', icon: Plus },
      'ACCEPT': { type: 'ACCEPTED', title: '接受会诊申请', icon: Check },
      'REJECT': { type: 'REJECTED', title: '拒绝会诊申请', icon: Close },
      'COMPLETE': { type: 'COMPLETED', title: '完成会诊', icon: Check },
      'CANCEL': { type: 'CANCELLED', title: '取消会诊申请', icon: Close },
      'WITHDRAW': { type: 'WITHDRAWN', title: '撤回会诊申请', icon: RefreshLeft },
      'UPDATE': { type: 'UPDATED', title: '更新会诊申请', icon: Edit },
      'DELETE': { type: 'DELETED', title: '删除会诊申请', icon: Close }
    }

    const operation = operationTypeMap[log.operationType] || { type: 'UNKNOWN', title: '未知操作', icon: User }
    
    // 解析操作内容
    let details = null
    if (log.operationContent) {
      try {
        const content = JSON.parse(log.operationContent)
        details = {
          reason: content.withdrawReason || content.rejectReason || content.cancelReason || content.acceptReason,
          opinion: content.consultationFindings,
          recommendation: content.consultationOpinion,
          treatmentAdvice: content.consultantSuggestion
        }
      } catch (e) {
        console.warn('解析操作内容失败:', e)
      }
    }

    return {
      type: operation.type,
      title: operation.title,
      createTime: log.operationTime,
      operator: log.operationUserName,
      description: log.operationDescription,
      details: details,
      oldStatus: log.oldStatus,
      newStatus: log.newStatus,
      operationIp: log.operationIp
    }
  }).sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
}

// 模拟历史记录数据
const mockHistoryData = computed(() => {
  if (!props.consultationData?.id) return []
  
  const history = []
  
  // 创建申请记录
  if (props.consultationData.createTime) {
    history.push({
      type: 'CREATED',
      title: '创建会诊申请',
      createTime: props.consultationData.createTime,
      operator: props.consultationData.requesterName,
      department: props.consultationData.requesterDepartment,
      description: '会诊申请已创建，等待专家接受',
      details: {
        reason: props.consultationData.reason,
        purpose: props.consultationData.purpose
      }
    })
  }
  
  // 接受记录
  if (props.consultationData.acceptTime) {
    history.push({
      type: 'ACCEPTED',
      title: '接受会诊申请',
      createTime: props.consultationData.acceptTime,
      operator: props.consultationData.consultantName,
      department: props.consultationData.consultantDepartment,
      description: '专家已接受会诊申请，开始会诊流程'
    })
  }
  
  // 诊断记录
  if (props.consultationData.diagnosis && props.consultationData.diagnosis.consultTime) {
    history.push({
      type: 'DIAGNOSED',
      title: '提交会诊意见',
      createTime: props.consultationData.diagnosis.consultTime,
      operator: props.consultationData.consultantName,
      department: props.consultationData.consultantDepartment,
      description: '专家已提交会诊意见和诊断建议',
      details: {
        opinion: props.consultationData.diagnosis.consultationOpinion,
        recommendation: props.consultationData.diagnosis.recommendation,
        treatmentAdvice: props.consultationData.diagnosis.treatmentAdvice
      }
    })
  }
  
  // 完成记录
  if (props.consultationData.completedTime) {
    const duration = calculateDuration(props.consultationData.createTime, props.consultationData.completedTime)
    history.push({
      type: 'COMPLETED',
      title: '完成会诊',
      createTime: props.consultationData.completedTime,
      operator: props.consultationData.consultantName,
      department: props.consultationData.consultantDepartment,
      description: '会诊已完成，申请医生可查看完整报告',
      duration: duration
    })
  }
  
  // 拒绝记录
  if (props.consultationData.status === 'REJECTED' && props.consultationData.rejectTime) {
    history.push({
      type: 'REJECTED',
      title: '拒绝会诊申请',
      createTime: props.consultationData.rejectTime,
      operator: props.consultationData.rejectBy,
      description: '专家已拒绝该会诊申请',
      details: {
        reason: props.consultationData.rejectReason
      }
    })
  }
  
  // 按时间倒序排列
  return history.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
})

// 获取时间线类型
const getTimelineType = (type) => {
  const typeMap = {
    'CREATED': 'primary',
    'ACCEPTED': 'success',
    'DIAGNOSED': 'primary',
    'COMPLETED': 'success',
    'REJECTED': 'danger',
    'CANCELLED': 'info',
    'WITHDRAWN': 'warning',
    'UPDATED': 'primary',
    'DELETED': 'danger'
  }
  return typeMap[type] || 'primary'
}

// 获取时间线图标
const getTimelineIcon = (type) => {
  const iconMap = {
    'CREATED': Plus,
    'ACCEPTED': Check,
    'DIAGNOSED': Edit,
    'COMPLETED': Check,
    'REJECTED': Close,
    'CANCELLED': Close,
    'WITHDRAWN': RefreshLeft,
    'UPDATED': Edit,
    'DELETED': Close
  }
  return iconMap[type] || User
}

// 获取状态标签类型
const getStatusTagType = (type) => {
  const typeMap = {
    'CREATED': 'primary',
    'ACCEPTED': 'success',
    'DIAGNOSED': 'warning',
    'COMPLETED': 'success',
    'REJECTED': 'danger',
    'CANCELLED': 'info',
    'WITHDRAWN': 'warning',
    'UPDATED': 'primary',
    'DELETED': 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取状态文本
const getStatusText = (type) => {
  const textMap = {
    'CREATED': '已创建',
    'ACCEPTED': '已接受',
    'DIAGNOSED': '已诊断',
    'COMPLETED': '已完成',
    'REJECTED': '已拒绝',
    'CANCELLED': '已取消',
    'WITHDRAWN': '已撤回',
    'UPDATED': '已更新',
    'DELETED': '已删除'
  }
  return textMap[type] || '未知'
}

// 获取文件图标
const getFileIcon = (fileType) => {
  if (!fileType) return Document
  
  const type = fileType.toLowerCase()
  if (type.includes('image') || type.includes('jpg') || type.includes('png') || type.includes('gif')) {
    return Picture
  }
  if (type.includes('video') || type.includes('mp4') || type.includes('avi')) {
    return VideoPlay
  }
  return Document
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return ''
  }
}

// 计算持续时间
const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return ''
  
  try {
    const start = new Date(startTime)
    const end = new Date(endTime)
    const diffMs = end - start
    
    if (diffMs < 0) return ''
    
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)
    
    if (diffDays > 0) {
      return `${diffDays}天${diffHours % 24}小时${diffMins % 60}分钟`
    } else if (diffHours > 0) {
      return `${diffHours}小时${diffMins % 60}分钟`
    } else {
      return `${diffMins}分钟`
    }
  } catch (error) {
    return ''
  }
}

// 下载附件
const downloadAttachment = (attachment) => {
  ElMessage.info('下载功能待实现')
  console.log('下载附件:', attachment)
}

// 刷新历史记录
const refreshHistory = async () => {
  await fetchAuditLogs()
  ElMessage.success('刷新成功')
}

// 初始化历史记录
onMounted(() => {
  fetchAuditLogs()
})

// 监听会诊数据变化
watch(() => props.consultationData?.id, (newId, oldId) => {
  if (newId !== oldId) {
    fetchAuditLogs()
  }
}, { immediate: true })
</script>

<style scoped>
.consultation-history {
  height: 100%;
  overflow-y: auto;
}

.history-container {
  padding: 16px;
}

.history-timeline {
  padding-left: 0;
}

.timeline-item {
  margin-bottom: 24px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-content {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.timeline-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e4e7ed;
  border-radius: 8px 8px 0 0;
}

.content-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.content-body {
  padding: 20px;
}

.content-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-label {
  font-size: 13px;
  color: #909399;
  font-weight: 500;
}

.info-value {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
}

.content-description {
  margin-bottom: 16px;
}

.content-description p {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
}

.consultation-details {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.details-section {
  margin-bottom: 12px;
}

.details-section:last-child {
  margin-bottom: 0;
}

.details-label {
  display: block;
  font-size: 13px;
  color: #606266;
  font-weight: 500;
  margin-bottom: 6px;
}

.details-content {
  font-size: 14px;
  color: #303133;
  line-height: 1.6;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.attachments {
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.attachments-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.attachments-icon {
  color: #409eff;
  font-size: 16px;
}

.attachments-header span {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.attachment-item:hover {
  background: #f0f9ff;
  border-color: #409eff;
}

.attachment-icon {
  color: #409eff;
  font-size: 16px;
}

.attachment-name {
  flex: 1;
  font-size: 13px;
  color: #303133;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* 时间线样式优化 */
.history-timeline :deep(.el-timeline-item__wrapper) {
  padding-left: 28px;
}

.history-timeline :deep(.el-timeline-item__tail) {
  border-left: 2px solid #e4e7ed;
}

.history-timeline :deep(.el-timeline-item__node) {
  width: 16px;
  height: 16px;
  left: -8px;
}

.history-timeline :deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .history-container {
    padding: 12px;
  }
  
  .content-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px 16px;
  }
  
  .content-body {
    padding: 16px;
  }
  
  .content-info {
    flex-direction: column;
    gap: 8px;
  }
  
  .consultation-details {
    padding: 12px;
  }
  
  .attachment-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .content-title {
    font-size: 14px;
  }
  
  .info-label,
  .info-value {
    font-size: 12px;
  }
  
  .details-content {
    font-size: 13px;
    padding: 6px 10px;
  }
}

/* 动画效果 */
.timeline-content {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>