<template>
  <div class="consultation-list">
    <!-- 列表头部 -->
    <div class="list-header">
      <div class="header-title">
        <el-icon><UserFilled /></el-icon>
        <span>会诊列表</span>
      </div>
      <div class="header-actions">
        <el-tooltip content="刷新列表" placement="top">
          <el-button 
            type="text" 
            :icon="Refresh" 
            @click="$emit('refresh')"
            :loading="loading"
            class="refresh-btn"
          />
        </el-tooltip>
      </div>
    </div>

    <!-- 会诊表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="data"
        :row-class-name="getRowClassName"
        @row-click="handleRowClick"
        height="100%"
        stripe
        highlight-current-row
        class="consultation-table"
      >
        <!-- 基本信息列 -->
        <el-table-column label="会诊信息" min-width="220">
          <template #default="scope">
            <div class="consultation-info">
              <div class="consultation-details">
                <div class="request-row">
                  <span class="request-no">{{ scope.row.requestNo }}</span>
                  <el-tag
                    :type="getConsultationStatusType(scope.row)"
                    size="small"
                    class="status-tag"
                  >
                    {{ getConsultationStatusText(scope.row) }}
                  </el-tag>
                </div>
                <div class="patient-row">
                  <span class="patient-name">{{ scope.row.patientName }}</span>
                  <span class="patient-meta">
                    {{ formatGender(scope.row.patientSex) }} 
                    · {{ calculateAge(scope.row.patientBirthday) }}岁
                  </span>
                </div>
                <div class="urgency-row">
                  <el-tag
                    :type="getUrgencyTagType(scope.row.urgencyLevel)"
                    size="small"
                    class="urgency-tag"
                  >
                    {{ getUrgencyText(scope.row.urgencyLevel) }}
                  </el-tag>
                  <span class="create-time">{{ formatDateTime(scope.row.createTime) }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 医生信息列 -->
        <el-table-column label="医生信息" min-width="160">
          <template #default="scope">
            <div class="doctor-info">
              <div class="requester-info">
                <span class="label">申请:</span>
                <span class="name">{{ scope.row.requesterName || '未指定' }}</span>
              </div>
              <div class="consultant-info">
                <span class="label">专家:</span>
                <span class="name">{{ scope.row.consultantName || '待指定' }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 检查信息列 -->
        <el-table-column label="检查信息" min-width="140">
          <template #default="scope">
            <div class="exam-info">
              <div class="exam-item">{{ scope.row.examItem || '未指定' }}</div>
              <div class="modality-row">
                <el-tag
                  :type="getModalityTagType(scope.row.modality)"
                  size="small"
                  class="modality-tag"
                >
                  {{ scope.row.modality || 'N/A' }}
                </el-tag>
                <span class="hospital">{{ scope.row.hospitalName }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 时间信息列 -->
        <el-table-column label="时间" width="120">
          <template #default="scope">
            <div class="time-info">
              <div class="apply-time">{{ formatDate(scope.row.createTime) }}</div>
              <div class="apply-time-detail">{{ formatTime(scope.row.createTime) }}</div>
              <div v-if="scope.row.expectedTime" class="expected-time">
                期望: {{ formatTime(scope.row.expectedTime) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="80" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click.stop="handleConsult(scope.row)"
              class="consult-btn"
            >
              {{ getConsultButtonText(scope.row) }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="Math.max(total, 0)"
        :page-sizes="[10, 20, 50, 100]"
        :layout="paginationLayout"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        small
        :hide-on-single-page="false"
        :disabled="total === 0"
        background
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { UserFilled, Refresh } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Array,
    default: () => []
  },
  total: {
    type: Number,
    default: 0
  },
  currentConsultation: {
    type: Object,
    default: null
  },
  currentRole: {
    type: String,
    default: 'consultant',
    validator: (value) => ['requester', 'consultant'].includes(value)
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 20
  }
})

// Emits
const emit = defineEmits(['consultation-select', 'page-change', 'size-change', 'refresh'])

// 响应式数据 - 使用props中的分页参数
const currentPage = ref(props.currentPage)
const pageSize = ref(props.pageSize)

// 窗口宽度响应式
const windowWidth = ref(window.innerWidth)

// 响应式分页布局
const paginationLayout = computed(() => {
  if (windowWidth.value < 350) {
    return 'prev, pager, next'  // 超小屏幕只显示基本导航
  } else if (windowWidth.value < 400) {
    return 'total, prev, pager, next'  // 小屏幕显示总数和基本导航
  } else {
    return 'total, prev, pager, next'  // 标准布局（去掉了sizes和jumper以节省空间）
  }
})


// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 计算属性
const selectedConsultationId = computed(() => {
  return props.currentConsultation?.id
})

// 监听props变化，同步分页参数
watch(() => props.currentPage, (newPage) => {
  currentPage.value = newPage
})

watch(() => props.pageSize, (newSize) => {
  pageSize.value = newSize
})

// 会诊状态常量
const CONSULTATION_STATUS = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  COMPLETED: 'COMPLETED',
  REJECTED: 'REJECTED',
  CANCELLED: 'CANCELLED',
  WITHDRAWN: 'WITHDRAWN'
}

// 格式化性别
const formatGender = (sex) => {
  if (sex === 'Male' || sex === 'M') return '男'
  if (sex === 'Female' || sex === 'F') return '女'
  return '未知'
}

// 计算年龄
const calculateAge = (birthDate) => {
  if (!birthDate) return '0'
  
  try {
    const birth = new Date(birthDate)
    const today = new Date()
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age.toString()
  } catch (error) {
    return '0'
  }
}

// 格式化日期
const formatDate = (dateTime) => {
  if (!dateTime) return ''
  try {
    const date = new Date(dateTime)
    return date.toLocaleDateString('zh-CN')
  } catch (error) {
    return ''
  }
}

// 格式化时间
const formatTime = (dateTime) => {
  if (!dateTime) return ''
  try {
    const date = new Date(dateTime)
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } catch (error) {
    return ''
  }
}

// 格式化完整日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return ''
  }
}

// 获取检查类型标签类型
const getModalityTagType = (modality) => {
  const typeMap = {
    'CT': 'primary',
    'MRI': 'success',
    'DR': 'warning',
    'US': 'info'
  }
  return typeMap[modality] || 'default'
}

// 获取紧急程度标签类型
const getUrgencyTagType = (urgencyLevel) => {
  const typeMap = {
    'URGENT': 'danger',      // 紧急
    'NORMAL': 'info',        // 普通
    'LOW': 'success'         // 非紧急
  }
  return typeMap[urgencyLevel] || 'info'
}

// 获取紧急程度文本
const getUrgencyText = (urgencyLevel) => {
  const textMap = {
    'URGENT': '紧急',
    'NORMAL': '普通',
    'LOW': '非紧急'
  }
  
  // 调试输出，检查实际接收到的紧急程度值
  if (urgencyLevel && !textMap[urgencyLevel]) {
    console.warn('未识别的紧急程度值:', urgencyLevel, '- 请检查是否与申请弹窗选项一致')
  }
  
  // 额外调试：记录所有紧急程度值的处理
  console.log('处理紧急程度显示:', {
    originalValue: urgencyLevel,
    mappedText: textMap[urgencyLevel] || '普通',
    allValidValues: Object.keys(textMap)
  })
  
  return textMap[urgencyLevel] || '普通'
}

// 获取会诊状态类型
const getConsultationStatusType = (consultation) => {
  switch (consultation.status) {
    case CONSULTATION_STATUS.PENDING:
      return 'warning'
    case CONSULTATION_STATUS.ACCEPTED:
      return 'primary'
    case CONSULTATION_STATUS.COMPLETED:
      return 'success'
    case CONSULTATION_STATUS.REJECTED:
      return 'danger'
    case CONSULTATION_STATUS.CANCELLED:
      return 'info'
    case CONSULTATION_STATUS.WITHDRAWN:
      return 'warning'
    default:
      return 'info'
  }
}

// 获取会诊状态文本
const getConsultationStatusText = (consultation) => {
  switch (consultation.status) {
    case CONSULTATION_STATUS.PENDING:
      return '待接受'
    case CONSULTATION_STATUS.ACCEPTED:
      return '进行中'
    case CONSULTATION_STATUS.COMPLETED:
      return '已完成'
    case CONSULTATION_STATUS.REJECTED:
      return '已拒绝'
    case CONSULTATION_STATUS.CANCELLED:
      return '已取消'
    case CONSULTATION_STATUS.WITHDRAWN:
      return '已撤回'
    default:
      return '未知状态'
  }
}

// 获取会诊按钮文本
const getConsultButtonText = (consultation) => {
  // 调试输出，检查数据是否正确
  console.log('按钮文本判断:', {
    currentRole: props.currentRole,
    consultationStatus: consultation.status,
    consultationId: consultation.id
  })
  
  if (props.currentRole === 'requester') {
    switch (consultation.status) {
      case CONSULTATION_STATUS.PENDING:
        return '查看'
      case CONSULTATION_STATUS.ACCEPTED:
        return '跟进'
      case CONSULTATION_STATUS.COMPLETED:
        return '查看'
      case CONSULTATION_STATUS.REJECTED:
        return '查看'
      case CONSULTATION_STATUS.CANCELLED:
        return '查看'
      case CONSULTATION_STATUS.WITHDRAWN:
        return '查看'
      default:
        return '查看'
    }
  } else { // consultant
    switch (consultation.status) {
      case CONSULTATION_STATUS.PENDING:
        return '处理'
      case CONSULTATION_STATUS.ACCEPTED:
        return '诊断'
      case CONSULTATION_STATUS.COMPLETED:
        return '查看'
      case CONSULTATION_STATUS.REJECTED:
        return '查看'
      case CONSULTATION_STATUS.CANCELLED:
        return '查看'
      case CONSULTATION_STATUS.WITHDRAWN:
        return '查看'
      default:
        return '查看'
    }
  }
}

// 获取优先级标识
const getPriorityIcon = (consultation) => {
  if (props.currentRole === 'consultant') {
    // 对于会诊专家，待接受的会诊为高优先级
    if (consultation.status === CONSULTATION_STATUS.PENDING) {
      return 'priority-high'
    }
    // 已接受但未完成的为中等优先级
    if (consultation.status === CONSULTATION_STATUS.ACCEPTED) {
      return 'priority-medium'
    }
  } else {
    // 对于申请医生，进行中的会诊为中等优先级
    if (consultation.status === CONSULTATION_STATUS.ACCEPTED) {
      return 'priority-medium'
    }
  }

  return null
}

// 获取行类名
const getRowClassName = ({ row }) => {
  let className = ''

  // 选中状态
  if (props.currentConsultation && props.currentConsultation.id === row.id) {
    className += 'selected-row '
  }

  // 根据角色和状态添加优先级样式
  const priorityIcon = getPriorityIcon(row)
  if (priorityIcon === 'priority-high') {
    className += 'priority-high-row '
  } else if (priorityIcon === 'priority-medium') {
    className += 'priority-medium-row '
  }

  // 紧急程度样式
  if (row.urgencyLevel === 'URGENT') {
    className += 'urgent-row '
  }

  return className.trim()
}

// 处理行点击
const handleRowClick = (row) => {
  emit('consultation-select', row)
}

// 处理会诊按钮点击
const handleConsult = (row) => {
  emit('consultation-select', row)
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('page-change', page)
}

// 处理页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  emit('size-change', size)
}
</script>

<style scoped>
.consultation-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 5px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafbfc;
  flex-shrink: 0;
  height: 40px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.refresh-btn {
  padding: 8px;
  border-radius: 6px;
}

.table-container {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.consultation-table {
  flex: 1;
  min-height: 0;
}

.consultation-table :deep(.el-table__row) {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.consultation-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.consultation-table :deep(.selected-row) {
  background-color: #e6f7ff !important;
}

.consultation-table :deep(.selected-row:hover) {
  background-color: #d6f3ff !important;
}

.consultation-table :deep(.priority-high-row) {
  border-left: 3px solid #f56c6c;
}

.consultation-table :deep(.priority-medium-row) {
  border-left: 3px solid #e6a23c;
}

.consultation-table :deep(.urgent-row) {
  background-color: #fef0f0;
}

.consultation-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.consultation-details {
  flex: 1;
  min-width: 0;
}

.request-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.request-no {
  font-weight: 600;
  color: #303133;
  flex: 1;
  font-size: 13px;
}

.status-tag {
  font-size: 10px;
  height: 18px;
  line-height: 16px;
  padding: 0 6px;
  border-radius: 2px;
}

.patient-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.patient-name {
  font-weight: 500;
  color: #303133;
  flex: 1;
}

.patient-meta {
  font-size: 12px;
  color: #909399;
}

.urgency-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.urgency-tag {
  font-size: 10px;
  height: 16px;
  line-height: 14px;
  padding: 0 5px;
  border-radius: 2px;
}

.create-time {
  font-size: 11px;
  color: #c0c4cc;
  flex: 1;
  text-align: right;
}

.doctor-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.requester-info,
.consultant-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.doctor-info .label {
  font-size: 11px;
  color: #909399;
  min-width: 32px;
}

.doctor-info .name {
  font-size: 12px;
  color: #303133;
  font-weight: 500;
  flex: 1;
}

.exam-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.exam-item {
  font-weight: 500;
  color: #303133;
  font-size: 12px;
}

.modality-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modality-tag {
  font-size: 10px;
  height: 16px;
  line-height: 14px;
  padding: 0 5px;
  border-radius: 2px;
}

.hospital {
  font-size: 11px;
  color: #909399;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.apply-time {
  font-weight: 500;
  color: #303133;
  font-size: 12px;
}

.apply-time-detail {
  font-size: 11px;
  color: #909399;
}

.expected-time {
  font-size: 10px;
  color: #e6a23c;
}

.consult-btn {
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 4px;
}

/* 修复按钮样式 */
.consultation-table :deep(.el-button--primary) {
  color: #ffffff !important;
  background-color: #0066cc !important;
  border-color: #0066cc !important;
}

.consultation-table :deep(.el-button--primary:hover),
.consultation-table :deep(.el-button--primary:focus) {
  color: #ffffff !important;
  background-color: #4d94ff !important;
  border-color: #4d94ff !important;
}

.consultation-table :deep(.el-button--primary:active) {
  color: #ffffff !important;
  background-color: #004499 !important;
  border-color: #004499 !important;
}

.pagination-container {
  padding: 4px 6px;
  border-top: 1px solid #e4e7ed;
  background: #fafbfc;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  height: 50px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .list-header {
    padding: 8px 12px;
  }
  
  .pagination-container {
    padding: 4px 6px;
    min-height: 45px;
  }
  
  .consultation-info {
    gap: 8px;
  }
}

/* 左侧面板的分页器优化 */
@media (max-width: 1400px) {
  .pagination-container {
    padding: 4px 6px;
    min-height: 45px;
  }
  
  .pagination-container :deep(.el-pagination) {
    flex-wrap: wrap;
    gap: 4px;
  }
}

/* 超小屏幕分页优化 */
@media (max-width: 350px) {
  .pagination-container {
    padding: 6px 8px;
    min-height: 40px;
  }
  
  .pagination-container :deep(.el-pagination__total) {
    font-size: 12px;
  }
}
</style>