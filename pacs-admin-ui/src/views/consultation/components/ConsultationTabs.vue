<template>
  <div class="consultation-tabs">
    <!-- Tab导航 -->
    <el-tabs v-model="activeTab" class="main-tabs">
      <!-- 申请信息 -->
      <el-tab-pane label="申请信息" name="request">
        <ConsultationRequestInfo 
          :consultation-data="consultationData"
          :current-role="currentRole"
          :user-permissions="userPermissions"
          @consultation-accept="handleConsultationAccept"
          @consultation-reject="handleConsultationReject"
          @consultation-withdraw="handleConsultationWithdraw"
        />
      </el-tab-pane>
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <ConsultationBasicInfo :consultation-data="consultationData" />
      </el-tab-pane>

      <!-- 诊断报告 -->
      <el-tab-pane label="诊断报告" name="diagnosis">
        <ConsultationDiagnosisReport
          ref="diagnosisReportRef"
          :consultation-data="consultationData"
          :current-role="currentRole"
          :user-permissions="userPermissions"
          @diagnosis-save="handleDiagnosisSave"
          @consultation-accept="handleConsultationAccept"
          @consultation-reject="handleConsultationReject"
          @consultation-complete="handleConsultationComplete"
          @template-apply="handleTemplateApply"
        />
      </el-tab-pane>

      <!-- 历史记录 -->
      <el-tab-pane label="历史记录" name="history">
        <ConsultationHistory :consultation-data="consultationData" />
      </el-tab-pane>
    </el-tabs>

    <!-- 保存为模板对话框 -->
    <el-dialog
      v-model="saveTemplateDialogVisible"
      title="保存为会诊模板"
      width="600px"
      :before-close="handleSaveTemplateDialogClose"
    >
      <el-form
        ref="templateFormRef"
        :model="templateForm"
        :rules="templateRules"
        label-width="100px"
      >
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="templateForm.name" placeholder="请输入模板名称" />
        </el-form-item>

        <el-form-item label="检查类型" prop="modalityType">
          <el-select v-model="templateForm.modalityType" placeholder="请选择检查类型">
            <el-option label="CT" value="CT" />
            <el-option label="MRI" value="MRI" />
            <el-option label="DR" value="DR" />
            <el-option label="US" value="US" />
            <el-option label="CR" value="CR" />
            <el-option label="DX" value="DX" />
          </el-select>
        </el-form-item>

        <el-form-item label="检查部位" prop="bodyPart">
          <el-input v-model="templateForm.bodyPart" placeholder="请输入检查部位" />
        </el-form-item>

        <el-form-item label="会诊标题">
          <el-input v-model="templateForm.title" placeholder="请输入会诊标题（可选）" />
        </el-form-item>

        <el-form-item label="关键词">
          <el-input v-model="templateForm.keywords" placeholder="请输入关键词，用逗号分隔" />
        </el-form-item>

        <el-form-item label="模板类型">
          <el-radio-group v-model="templateForm.isPublic">
            <el-radio label="0">私有模板</el-radio>
            <el-radio label="1">公共模板</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="设为默认">
          <el-switch v-model="templateForm.isDefault" active-value="1" inactive-value="0" />
        </el-form-item>

        <el-form-item label="会诊意见">
          <el-input
            v-model="templateForm.opinion"
            type="textarea"
            :rows="4"
            readonly
            placeholder="将自动填入当前会诊意见内容"
          />
        </el-form-item>

        <el-form-item label="诊断建议">
          <el-input
            v-model="templateForm.recommendation"
            type="textarea"
            :rows="3"
            readonly
            placeholder="将自动填入当前诊断建议内容"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleSaveTemplateDialogClose">取消</el-button>
          <el-button type="primary" @click="handleConfirmSaveTemplate" :loading="saveTemplateLoading">
            保存模板
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { addTemplate } from '@/api/diagnosis/template'
import ConsultationBasicInfo from './ConsultationBasicInfo.vue'
import ConsultationRequestInfo from './ConsultationRequestInfo.vue'
import ConsultationDiagnosisReport from './ConsultationDiagnosisReport.vue'
import ConsultationHistory from './ConsultationHistory.vue'
import useUserStore from '@/store/modules/user'

// Props
const props = defineProps({
  consultationData: {
    type: Object,
    default: () => ({})
  },
  currentRole: {
    type: String,
    default: 'consultant',
    validator: (value) => ['requester', 'consultant'].includes(value)
  },
  userPermissions: {
    type: Object,
    default: () => ({
      canAccept: true,
      canReject: true,
      canComplete: true,
      canDiagnose: true,
      canEdit: true
    })
  }
})

// Emits
const emit = defineEmits([
  'consultation-accept',
  'consultation-reject', 
  'consultation-complete',
  'consultation-withdraw',
  'diagnosis-save'
])

// 响应式数据
const activeTab = ref('request')
const diagnosisReportRef = ref()
const saveTemplateDialogVisible = ref(false)
const saveTemplateLoading = ref(false)
const templateFormRef = ref(null)

// Store
const userStore = useUserStore()

// 模板表单数据
const templateForm = reactive({
  name: '',
  modalityType: '',
  bodyPart: '',
  title: '',
  opinion: '',
  recommendation: '',
  keywords: '',
  isPublic: '0',
  isDefault: '0',
  sortOrder: 0
})

// 模板表单验证规则
const templateRules = reactive({
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  modalityType: [{ required: true, message: '请选择检查类型', trigger: 'change' }],
  bodyPart: [{ required: true, message: '请输入检查部位', trigger: 'blur' }]
})

// 计算属性
const isCompleted = computed(() => {
  return props.consultationData?.status === 'COMPLETED'
})

const isCreator = computed(() => {
  if (!props.consultationData?.id) return true
  
  // 检查创建者权限
  if (props.consultationData.createBy === 'admin') return true
  if (props.consultationData.createBy === userStore.id || props.consultationData.createBy === userStore.name) return true
  if (props.consultationData.requesterName === userStore.name) return true
  
  return false
})

// 新增计算属性：根据角色和权限控制功能可见性
const canShowDiagnosisTab = computed(() => {
  return props.currentRole === 'consultant' || props.userPermissions.canDiagnose
})

const shouldAutoFocusDiagnosis = computed(() => {
  return props.currentRole === 'consultant' && props.consultationData?.status === 'ACCEPTED'
})

const tabOrder = computed(() => {
  const tabs = []

  // 根据角色调整Tab顺序
  if (props.currentRole === 'consultant') {
    tabs.push(
      { name: 'diagnosis', label: '诊断报告', priority: 1 },
      { name: 'basic', label: '基本信息', priority: 2 },
      { name: 'request', label: '申请信息', priority: 3 },
      { name: 'history', label: '历史记录', priority: 4 }
    )
  } else {
    tabs.push(
      { name: 'basic', label: '基本信息', priority: 1 },
      { name: 'request', label: '申请信息', priority: 2 },
      { name: 'diagnosis', label: '诊断报告', priority: 3 },
      { name: 'history', label: '历史记录', priority: 4 }
    )
  }

  return tabs.sort((a, b) => a.priority - b.priority)
})

// 监听会诊数据变化，自动切换到相应tab
watch(() => props.consultationData, (newVal) => {
  if (newVal) {
    if (props.currentRole === 'consultant') {
      // 会诊专家优先显示诊断页面
      if (newVal.status === 'PENDING') {
        activeTab.value = 'request' // 待接受时先看基本信息
      } else {
        activeTab.value = 'diagnosis' // 已接受时直接看诊断
      }
    } else {
      // 申请医生优先显示基本信息
      activeTab.value = 'request'
    }
  }
}, { immediate: true })

// 监听角色变化
watch(() => props.currentRole, (newRole) => {
  if (newRole === 'consultant' && props.consultationData) {
    if (props.consultationData.status === 'PENDING') {
      activeTab.value = 'basic'
    } else {
      activeTab.value = 'diagnosis'
    }
  } else if (newRole === 'requester' && props.consultationData) {
    activeTab.value = 'basic'
  }
})

// 处理模板应用
const handleTemplateApply = (template) => {
  if (diagnosisReportRef.value) {
    diagnosisReportRef.value.applyTemplate(template)
  }
}

// 处理诊断保存
const handleDiagnosisSave = (data) => {
  emit('diagnosis-save', data)
}

// 处理会诊接受
const handleConsultationAccept = (data) => {
  emit('consultation-accept', data)
}

// 处理会诊拒绝
const handleConsultationReject = (data) => {
  emit('consultation-reject', data)
}

// 处理会诊完成
const handleConsultationComplete = (data) => {
  emit('consultation-complete', data)
}

// 处理会诊撤回
const handleConsultationWithdraw = (data) => {
  emit('consultation-withdraw', data)
}

// 保存为模板
const handleSaveAsTemplate = () => {
  if (!diagnosisReportRef.value) {
    ElMessage.warning('请先填写诊断报告')
    return
  }

  // 获取当前诊断报告数据
  const reportData = diagnosisReportRef.value.getDiagnosisData()

  if (!reportData.opinion && !reportData.recommendation) {
    ElMessage.warning('请先填写会诊意见或诊断建议')
    return
  }

  // 重置表单
  Object.keys(templateForm).forEach(key => {
    if (key === 'isPublic' || key === 'isDefault') {
      templateForm[key] = '0'
    } else if (key === 'sortOrder') {
      templateForm[key] = 0
    } else {
      templateForm[key] = ''
    }
  })

  // 填入报告内容
  templateForm.opinion = reportData.opinion || ''
  templateForm.recommendation = reportData.recommendation || ''
  templateForm.title = reportData.title || ''

  // 从会诊数据中获取检查类型和部位
  if (props.consultationData) {
    templateForm.modalityType = props.consultationData.patientStudy?.modality || ''
    templateForm.bodyPart = props.consultationData.patientStudy?.organ || ''
  }

  saveTemplateDialogVisible.value = true
}

// 确认保存模板
const handleConfirmSaveTemplate = async () => {
  try {
    await templateFormRef.value.validate()

    saveTemplateLoading.value = true

    const res = await addTemplate(templateForm)

    if (res.code === 200) {
      ElMessage.success('模板保存成功')
      saveTemplateDialogVisible.value = false
    } else {
      ElMessage.error(res.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存模板出错:', error)
    ElMessage.error('系统错误，请联系管理员')
  } finally {
    saveTemplateLoading.value = false
  }
}

// 关闭保存模板对话框
const handleSaveTemplateDialogClose = () => {
  saveTemplateDialogVisible.value = false
}

// 暴露方法
defineExpose({
  applyTemplate: handleTemplateApply,
  handleSaveAsTemplate,
  switchToDiagnosis: () => {
    activeTab.value = 'diagnosis'
  },
  switchToBasic: () => {
    activeTab.value = 'basic'
  }
})
</script>

<style scoped>
.consultation-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.main-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.main-tabs :deep(.el-tabs__content) {
  flex: 1;
  padding: 0;
  overflow: hidden;
  min-height: 0;
}

.main-tabs :deep(.el-tab-pane) {
  height: 100%;
  padding: 12px;
  overflow-y: auto;
  box-sizing: border-box;
}

.main-tabs :deep(.el-tabs__header) {
  margin: 0;
  border-bottom: 2px solid #e4e7ed;
  background: #fafbfc;
  padding: 0 16px;
}

.main-tabs :deep(.el-tabs__nav-wrap) {
  background: #fafbfc;
}

.main-tabs :deep(.el-tabs__item) {
  padding: 0 20px;
  height: 42px;
  line-height: 42px;
  font-weight: 500;
  color: #606266;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.main-tabs :deep(.el-tabs__item:hover) {
  color: #409eff;
}

.main-tabs :deep(.el-tabs__item.is-active) {
  color: #409eff;
  border-bottom-color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.main-tabs :deep(.el-tabs__active-bar) {
  display: none;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-tabs :deep(.el-tabs__header) {
    padding: 0 8px;
  }
  
  .main-tabs :deep(.el-tabs__item) {
    padding: 0 12px;
    font-size: 14px;
  }
  
  .main-tabs :deep(.el-tab-pane) {
    padding: 8px;
  }
}

/* Tab内容区域优化 */
.main-tabs :deep(.el-tab-pane) {
  background: #ffffff;
  border-radius: 6px;
  margin: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 确保Tab切换动画流畅 */
.main-tabs :deep(.el-tabs__content) {
  transition: all 0.3s ease;
}
</style>