<template>
  <div class="consultation-basic-info">
    <div class="info-container">
      <!-- 患者基本信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><User /></el-icon>
            <span class="header-title">患者信息</span>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">姓名</label>
              <span class="info-value">{{ consultationData.patientName || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">性别</label>
              <span class="info-value">{{ formatGender(consultationData.patientSex) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">年龄</label>
              <span class="info-value">{{ calculateAge(consultationData.patientBirthday) }}岁</span>
            </div>
            <div class="info-item">
              <label class="info-label">出生日期</label>
              <span class="info-value">{{ formatDate(consultationData.patientBirthday) }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">患者ID</label>
              <span class="info-value">{{ consultationData.patientId || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">住院号</label>
              <span class="info-value">{{ consultationData.inPatientId || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">床号</label>
              <span class="info-value">{{ consultationData.bedNo || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">联系电话</label>
              <span class="info-value">{{ formatPhone(consultationData.mobile) }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 检查信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Document /></el-icon>
            <span class="header-title">检查信息</span>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">检查编号</label>
              <span class="info-value">{{ consultationData.patientStudy?.examCode || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">检查类型</label>
              <el-tag 
                :type="getModalityTagType(consultationData.patientStudy?.modality)" 
                size="small"
                class="modality-tag"
              >
                {{ consultationData.patientStudy?.modality || '-' }}
              </el-tag>
            </div>
            <div class="info-item">
              <label class="info-label">检查部位</label>
              <span class="info-value">{{ consultationData.patientStudy?.organ || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">检查项目</label>
              <span class="info-value">{{ consultationData.examItem || '-' }}</span>
            </div>
          </div>
          
          <div class="info-row">
            <div class="info-item">
              <label class="info-label">检查时间</label>
              <span class="info-value">{{ formatDateTime(consultationData.patientStudy?.checkFinishTime) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">申请科室</label>
              <span class="info-value">{{ consultationData.patientStudy?.examDepartment || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">送检医生</label>
              <span class="info-value">{{ consultationData.patientStudy?.examDoctorName || '-' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">医院名称</label>
              <span class="info-value">{{ consultationData.hospitalName || '-' }}</span>
            </div>
          </div>
        </div>
      </el-card>


      <!-- 临床信息卡片 -->
      <el-card class="info-card" shadow="never" v-if="consultationData.clinicalInfo">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Memo /></el-icon>
            <span class="header-title">临床信息</span>
          </div>
        </template>
        
        <div class="clinical-content">
          <div class="clinical-item">
            <label class="clinical-label">临床症状：</label>
            <p class="clinical-text">{{ consultationData.clinicalInfo.symptoms || '无' }}</p>
          </div>
          <div class="clinical-item">
            <label class="clinical-label">病史：</label>
            <p class="clinical-text">{{ consultationData.clinicalInfo.history || '无' }}</p>
          </div>
          <div class="clinical-item">
            <label class="clinical-label">其他检查：</label>
            <p class="clinical-text">{{ consultationData.clinicalInfo.otherExams || '无' }}</p>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { User, Document, Memo } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  consultationData: {
    type: Object,
    default: () => ({})
  }
})

// 格式化性别
const formatGender = (sex) => {
  if (sex === 'Male' || sex === 'M') return '男'
  if (sex === 'Female' || sex === 'F') return '女'
  return '未知'
}

// 计算年龄
const calculateAge = (birthDate) => {
  if (!birthDate) return '0'
  
  try {
    const birth = new Date(birthDate)
    const today = new Date()
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age.toString()
  } catch (error) {
    return '0'
  }
}

// 格式化日期
const formatDate = (dateTime) => {
  if (!dateTime) return '-'
  try {
    const date = new Date(dateTime)
    return date.toLocaleDateString('zh-CN')
  } catch (error) {
    return '-'
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN')
  } catch (error) {
    return '-'
  }
}

// 格式化电话号码
const formatPhone = (phone) => {
  if (!phone) return '-'
  // 简单的电话号码格式化
  const phoneStr = phone.toString()
  if (phoneStr.length === 11) {
    return phoneStr.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3')
  }
  return phoneStr
}

// 获取检查类型标签类型
const getModalityTagType = (modality) => {
  const typeMap = {
    'CT': 'primary',
    'MRI': 'success',
    'DR': 'warning',
    'US': 'info'
  }
  return typeMap[modality] || 'default'
}

</script>

<style scoped>
.consultation-basic-info {
  height: 100%;
  overflow-y: auto;
}

.info-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0;
}

.info-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.info-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
  font-size: 18px;
}

.header-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.info-card :deep(.el-card__body) {
  padding: 16px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #303133;
  font-weight: 400;
  word-break: break-word;
}

.modality-tag {
  font-size: 12px;
  max-width: fit-content;
}

.clinical-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.clinical-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.clinical-label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.clinical-text {
  font-size: 14px;
  color: #303133;
  line-height: 1.6;
  margin: 0;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .info-container {
    gap: 12px;
  }
  
  .info-card :deep(.el-card__header) {
    padding: 10px 12px;
  }
  
  .info-card :deep(.el-card__body) {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .header-title {
    font-size: 13px;
  }
  
  .info-label {
    font-size: 11px;
  }
  
  .info-value {
    font-size: 13px;
  }
}

/* 卡片悬停效果 */
.info-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

/* 标签样式优化 */
.modality-tag {
  border-radius: 12px;
  font-weight: 500;
}
</style>