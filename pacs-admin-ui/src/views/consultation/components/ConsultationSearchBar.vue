<template>
  <div class="consultation-search-bar">
    <!-- 单行搜索栏 -->
    <div class="search-row">
      <!-- 左侧搜索条件 -->
      <div class="search-conditions">
        <!-- 会诊工作台 标签 -->
        <div class="workspace-label">
          <el-icon class="label-icon"><UserFilled /></el-icon>
          <span>会诊工作台</span>
        </div>

        <!-- 搜索条件按钮组 -->
        <div class="condition-buttons">
          <el-button 
            :type="activeCondition === 'patient' ? 'primary' : ''" 
            size="small"
            @click="setActiveCondition('patient')"
            class="condition-btn"
          >
            患者姓名
          </el-button>
          <el-button 
            :type="activeCondition === 'requestNo' ? 'primary' : ''" 
            size="small"
            @click="setActiveCondition('requestNo')"
            class="condition-btn"
          >
            申请编号
          </el-button>
          <el-button 
            :type="activeCondition === 'requester' ? 'primary' : ''" 
            size="small"
            @click="setActiveCondition('requester')"
            class="condition-btn"
          >
            申请医生
          </el-button>
          <el-button 
            :type="activeCondition === 'consultant' ? 'primary' : ''" 
            size="small"
            @click="setActiveCondition('consultant')"
            class="condition-btn"
          >
            会诊专家
          </el-button>
          <el-button 
            :type="activeCondition === 'urgency' ? 'primary' : ''" 
            size="small"
            @click="setActiveCondition('urgency')"
            class="condition-btn"
          >
            紧急程度
          </el-button>
          <el-button 
            size="small"
            @click="showAdvancedDialog = true"
            class="condition-btn"
          >
            更多
          </el-button>
        </div>

        <!-- 搜索输入框 -->
        <div class="search-input">
          <!-- 患者姓名搜索 -->
          <el-input
            v-if="activeCondition === 'patient'"
            v-model="localQueryParams.patientName"
            placeholder="请输入患者姓名"
            clearable
            @keyup.enter="handleSearch"
            @clear="handleSearch"
            size="small"
            style="width: 200px"
          />
          <!-- 申请编号搜索 -->
          <el-input
            v-else-if="activeCondition === 'requestNo'"
            v-model="localQueryParams.requestNo"
            placeholder="请输入申请编号"
            clearable
            @keyup.enter="handleSearch"
            @clear="handleSearch"
            size="small"
            style="width: 200px"
          />
          <!-- 申请医生搜索 -->
          <el-input
            v-else-if="activeCondition === 'requester'"
            v-model="localQueryParams.requesterName"
            placeholder="请输入申请医生"
            clearable
            @keyup.enter="handleSearch"
            @clear="handleSearch"
            size="small"
            style="width: 200px"
          />
          <!-- 会诊专家搜索 -->
          <el-input
            v-else-if="activeCondition === 'consultant'"
            v-model="localQueryParams.consultantName"
            placeholder="请输入会诊专家"
            clearable
            @keyup.enter="handleSearch"
            @clear="handleSearch"
            size="small"
            style="width: 200px"
          />
          <!-- 紧急程度搜索 -->
          <el-select
            v-else-if="activeCondition === 'urgency'"
            v-model="localQueryParams.urgencyLevel"
            placeholder="请选择紧急程度"
            clearable
            @change="handleUrgencyChange"
            @clear="handleSearch"
            size="small"
            style="width: 160px"
          >
            <!-- 与申请弹窗保持一致的紧急程度选项 -->
            <el-option label="紧急" value="URGENT" />
            <el-option label="普通" value="NORMAL" />
            <el-option label="非紧急" value="LOW" />
          </el-select>
        </div>
      </div>

      <!-- 右侧日期和操作按钮 -->
      <div class="right-actions">
        <!-- 日期范围选择 -->
        <div class="date-range">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="—"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
            size="small"
            style="width: 240px"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button type="primary" size="small" @click="handleSearch">
            查询
          </el-button>
          <el-button size="small" @click="handleReset">
            重置
          </el-button>
          <el-button size="small" @click="showAdvancedDialog = true">
            <el-icon><Setting /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 高级搜索对话框 -->
    <el-dialog
      v-model="showAdvancedDialog"
      title="高级搜索 - 会诊条件查询"
      width="70%"
      :before-close="handleAdvancedClose"
    >
      <el-form :model="localQueryParams" label-width="100px">
        <!-- 基础信息 -->
        <el-divider content-position="left">基础信息</el-divider>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="申请编号">
              <el-input
                v-model="localQueryParams.requestNo"
                placeholder="请输入申请编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="患者姓名">
              <el-input
                v-model="localQueryParams.patientName"
                placeholder="请输入患者姓名"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="患者ID">
              <el-input
                v-model="localQueryParams.patientId"
                placeholder="请输入患者ID"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 会诊信息 -->
        <el-divider content-position="left">会诊信息</el-divider>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="申请医生">
              <el-input
                v-model="localQueryParams.requesterName"
                placeholder="请输入申请医生"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="会诊专家">
              <el-input
                v-model="localQueryParams.consultantName"
                placeholder="请输入会诊专家"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="紧急程度">
              <el-select
                v-model="localQueryParams.urgencyLevel"
                placeholder="请选择紧急程度"
                clearable
                style="width: 100%"
              >
                <!-- 与申请弹窗保持一致的紧急程度选项 -->
                <el-option label="紧急" value="URGENT" />
                <el-option label="普通" value="NORMAL" />
                <el-option label="非紧急" value="LOW" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="会诊状态">
              <el-select
                v-model="localQueryParams.status"
                placeholder="请选择会诊状态"
                clearable
                style="width: 100%"
              >
                <el-option label="全部状态" value="" />
                <el-option label="待接受" value="PENDING" />
                <el-option label="已接受" value="ACCEPTED" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="已拒绝" value="REJECTED" />
                <el-option label="已取消" value="CANCELLED" />
                <el-option label="已撤回" value="WITHDRAWN" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 医院信息 -->
        <el-divider content-position="left">医院信息</el-divider>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="医院名称">
              <el-input
                v-model="localQueryParams.hospitalName"
                placeholder="请输入医院名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查项目">
              <el-input
                v-model="localQueryParams.examItem"
                placeholder="请输入检查项目"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查方式">
              <el-select
                v-model="localQueryParams.modality"
                placeholder="请选择检查方式"
                clearable
                style="width: 100%"
              >
                <el-option label="CT" value="CT" />
                <el-option label="MRI" value="MRI" />
                <el-option label="DR" value="DR" />
                <el-option label="US" value="US" />
                <el-option label="其他" value="OTHER" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 时间范围 -->
        <el-divider content-position="left">时间范围</el-divider>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="申请时间">
              <el-date-picker
                v-model="advancedDateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="快速选择">
              <el-button-group>
                <el-button size="small" @click="setQuickDateRange('today')">今天</el-button>
                <el-button size="small" @click="setQuickDateRange('week')">本周</el-button>
                <el-button size="small" @click="setQuickDateRange('month')">本月</el-button>
                <el-button size="small" @click="setQuickDateRange('quarter')">本季度</el-button>
              </el-button-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleAdvancedReset">
            <el-icon><Refresh /></el-icon>
            重置所有条件
          </el-button>
          <el-button @click="showAdvancedDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAdvancedSearch">
            <el-icon><Search /></el-icon>
            开始搜索
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { UserFilled, Setting, Search, Refresh } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  queryParams: {
    type: Object,
    required: true
  },
  currentRole: {
    type: String,
    default: 'consultant'
  }
})

// Emits
const emit = defineEmits(['search', 'reset', 'update:queryParams'])

// 响应式数据
const showAdvancedDialog = ref(false)
const activeCondition = ref('patient') // 当前激活的搜索条件
const dateRange = ref([])
const advancedDateRange = ref([])

// 本地查询参数（用于双向绑定）
const localQueryParams = reactive({ ...props.queryParams })

// 监听props变化，同步到本地参数
watch(() => props.queryParams, (newVal) => {
  console.log('Props变化，同步到本地参数:', {
    oldLocalParams: { ...localQueryParams },
    newPropsParams: { ...newVal },
    oldUrgencyLevel: localQueryParams.urgencyLevel,
    newUrgencyLevel: newVal.urgencyLevel,
    willOverride: localQueryParams.urgencyLevel !== newVal.urgencyLevel,
    timestamp: new Date().toLocaleTimeString()
  })
  Object.assign(localQueryParams, newVal)
  
  console.log('Props同步完成后:', {
    finalLocalParams: { ...localQueryParams },
    finalUrgencyLevel: localQueryParams.urgencyLevel
  })
}, { deep: true })

// 监听本地参数变化，同步到父组件
watch(localQueryParams, (newVal) => {
  console.log('本地参数变化，同步到父组件:', {
    newParams: { ...newVal },
    urgencyLevel: newVal.urgencyLevel,
    triggerTime: new Date().toLocaleTimeString()
  })
  emit('update:queryParams', newVal)
}, { deep: true })

// 设置激活的搜索条件
const setActiveCondition = (condition) => {
  // 不清空其他条件，只是切换当前激活的搜索输入框
  // 这样用户可以同时使用多个搜索条件
  activeCondition.value = condition
  
  // 调试输出
  console.log('搜索条件切换:', {
    activeCondition: condition,
    currentParams: { ...localQueryParams },
    note: '保留所有已设置的搜索条件'
  })
}

// 处理日期范围变化
const handleDateRangeChange = (value) => {
  if (value && value.length === 2) {
    localQueryParams.startTime = value[0]
    localQueryParams.endTime = value[1]
  } else {
    localQueryParams.startTime = undefined
    localQueryParams.endTime = undefined
  }
}

// 处理搜索
const handleSearch = () => {
  emit('search',localQueryParams)
}

// 处理紧急程度变化
const handleUrgencyChange = (value) => {
  console.log('紧急程度筛选变化:', {
    selectedValue: value,
    beforeParams: { ...localQueryParams },
    urgencyLevel: localQueryParams.urgencyLevel
  })
  
  // 确保参数正确设置
  localQueryParams.urgencyLevel = value
  
  console.log('紧急程度筛选变化后:', {
    afterParams: { ...localQueryParams },
    finalUrgencyLevel: localQueryParams.urgencyLevel
  })
  
  handleSearch()
}

// 处理重置
const handleReset = () => {
  // 重置所有搜索条件为undefined
  Object.keys(localQueryParams).forEach(key => {
    if (key !== 'pageNum' && key !== 'pageSize') {
      localQueryParams[key] = undefined
    }
  })

  dateRange.value = []
  advancedDateRange.value = []
  activeCondition.value = 'patient'
  emit('reset')
}

// 高级搜索相关方法
const handleAdvancedSearch = () => {
  // 处理高级日期范围
  if (advancedDateRange.value && advancedDateRange.value.length === 2) {
    localQueryParams.startTime = advancedDateRange.value[0]
    localQueryParams.endTime = advancedDateRange.value[1]
  }
  
  showAdvancedDialog.value = false
  handleSearch()
}

const handleAdvancedReset = () => {
  localQueryParams.hospitalName = undefined
  localQueryParams.examItem = undefined
  localQueryParams.modality = undefined
  advancedDateRange.value = []
  localQueryParams.startTime = undefined
  localQueryParams.endTime = undefined
}

const handleAdvancedClose = () => {
  showAdvancedDialog.value = false
}

// 快速日期选择
const setQuickDateRange = (type) => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  switch (type) {
    case 'today':
      advancedDateRange.value = [
        today.toISOString().slice(0, 19).replace('T', ' '),
        new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1000).toISOString().slice(0, 19).replace('T', ' ')
      ]
      break
    case 'week':
      const weekStart = new Date(today.getTime() - today.getDay() * 24 * 60 * 60 * 1000)
      const weekEnd = new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000 - 1000)
      advancedDateRange.value = [
        weekStart.toISOString().slice(0, 19).replace('T', ' '),
        weekEnd.toISOString().slice(0, 19).replace('T', ' ')
      ]
      break
    case 'month':
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
      advancedDateRange.value = [
        monthStart.toISOString().slice(0, 19).replace('T', ' '),
        monthEnd.toISOString().slice(0, 19).replace('T', ' ')
      ]
      break
    case 'quarter':
      const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1)
      const quarterEnd = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0, 23, 59, 59)
      advancedDateRange.value = [
        quarterStart.toISOString().slice(0, 19).replace('T', ' '),
        quarterEnd.toISOString().slice(0, 19).replace('T', ' ')
      ]
      break
  }
}
</script>

<style scoped>
.consultation-search-bar {
  background: white;
  padding: 12px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.search-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  height: 32px;
}

.search-conditions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.workspace-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
  white-space: nowrap;
}

.label-icon {
  color: #409eff;
  font-size: 16px;
}

.condition-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.condition-btn {
  height: 28px;
  padding: 0 12px;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background: white;
  color: #606266;
  transition: all 0.3s ease;
}

.condition-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.condition-btn.el-button--primary {
  background: #409eff;
  border-color: #409eff;
  color: white;
}

.search-input {
  display: flex;
  align-items: center;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.date-range {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons .el-button {
  height: 28px;
  padding: 0 12px;
  font-size: 12px;
}

/* 高级搜索对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 高级搜索表单样式 */
:deep(.el-dialog__body) {
  padding: 20px 20px 10px 20px;
}

:deep(.el-divider) {
  margin: 16px 0;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: #409eff;
}

:deep(.el-button-group .el-button) {
  padding: 5px 10px;
  font-size: 12px;
}

/* 美化样式 */
.search-input :deep(.el-input__wrapper) {
  border-radius: 4px;
  height: 28px;
}

.search-input :deep(.el-select .el-input__wrapper) {
  border-radius: 4px;
  height: 28px;
}

.date-range :deep(.el-date-editor) {
  height: 28px;
}

.date-range :deep(.el-date-editor .el-input__wrapper) {
  border-radius: 4px;
}

.action-buttons :deep(.el-button--primary) {
  background: #409eff;
  border-color: #409eff;
}

.action-buttons :deep(.el-button--primary:hover) {
  background: #337ecc;
  border-color: #337ecc;
}

/* 确保输入框和按钮高度一致 */
.search-input :deep(.el-input),
.search-input :deep(.el-select),
.date-range :deep(.el-date-editor),
.action-buttons :deep(.el-button) {
  height: 28px;
}

.search-input :deep(.el-input__inner),
.search-input :deep(.el-select__wrapper) {
  height: 28px;
  line-height: 28px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .search-row {
    flex-wrap: wrap;
    height: auto;
    gap: 12px;
  }

  .search-conditions {
    flex-wrap: wrap;
    gap: 12px;
  }

  .condition-buttons {
    flex-wrap: wrap;
    gap: 6px;
  }

  .right-actions {
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .consultation-search-bar {
    padding: 8px 12px;
  }

  .search-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .search-conditions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .condition-buttons {
    justify-content: flex-start;
  }

  .right-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .date-range {
    width: 100%;
  }

  .date-range :deep(.el-date-editor) {
    width: 100% !important;
  }

  .action-buttons {
    justify-content: center;
  }
}
</style>