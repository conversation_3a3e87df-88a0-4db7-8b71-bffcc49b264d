<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="page-title">会诊管理</div>
        <div class="header-actions">
          <el-button type="primary" link icon="download" @click="handleExport" v-hasPermi="['consultation:request:export']">导出</el-button>
          <el-button type="primary" link icon="time" @click="handleExpiredConsultations" v-hasPermi="['consultation:management:handle']">处理过期</el-button>
          <el-button type="primary" link icon="message" @click="handleSendReminders" v-hasPermi="['consultation:management:handle']">发送提醒</el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-section">
      <el-row :gutter="12">
        <el-col :span="6">
          <div class="stat-card pending" @click="filterByStatus('PENDING')">
            <div class="stat-info">
              <div class="stat-number">{{ statistics.pendingCount || 0 }}</div>
              <div class="stat-label">待处理</div>
            </div>
            <div class="stat-icon">
              <el-icon><Clock /></el-icon>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card accepted" @click="filterByStatus('ACCEPTED')">
            <div class="stat-info">
              <div class="stat-number">{{ statistics.acceptedCount || 0 }}</div>
              <div class="stat-label">已接受</div>
            </div>
            <div class="stat-icon">
              <el-icon><Select /></el-icon>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card completed" @click="filterByStatus('COMPLETED')">
            <div class="stat-info">
              <div class="stat-number">{{ statistics.completedCount || 0 }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <div class="stat-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card total" @click="filterByStatus('')">
            <div class="stat-info">
              <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
              <div class="stat-label">总计</div>
            </div>
            <div class="stat-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 紧凑搜索栏 -->
    <div v-show="showSearch" class="compact-search-bar">
      <el-form :model="queryParams" ref="queryForm" :inline="true" size="small" class="compact-form">
        <el-form-item label="申请编号" prop="requestNo">
          <el-input
            v-model="queryParams.requestNo"
            placeholder="请输入申请编号"
            clearable
            style="width: 160px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="申请医生" prop="requesterName">
          <el-input
            v-model="queryParams.requesterName"
            placeholder="请输入申请医生"
            clearable
            style="width: 160px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="待处理" value="PENDING" />
            <el-option label="已接受" value="ACCEPTED" />
            <el-option label="已拒绝" value="REJECTED" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
          <el-button type="text" icon="more" @click="showAdvancedSearch = true">更多筛选</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 高级搜索弹窗 -->
    <el-dialog
      v-model="showAdvancedSearch"
      title="高级搜索"
      width="700px"
      :before-close="handleAdvancedClose"
    >
      <el-form :model="advancedParams" label-width="100px">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="申请编号">
              <el-input
                v-model="advancedParams.requestNo"
                placeholder="请输入申请编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请医生">
              <el-input
                v-model="advancedParams.requesterName"
                placeholder="请输入申请医生"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="会诊医生">
              <el-input
                v-model="advancedParams.consultantName"
                placeholder="请输入会诊医生"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="状态">
              <el-select v-model="advancedParams.status" placeholder="请选择状态" clearable style="width: 100%">
                <el-option label="待处理" value="PENDING" />
                <el-option label="已接受" value="ACCEPTED" />
                <el-option label="已拒绝" value="REJECTED" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="已取消" value="CANCELLED" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="紧急程度">
              <el-select v-model="advancedParams.urgencyLevel" placeholder="请选择紧急程度" clearable style="width: 100%">
                <el-option label="紧急" value="URGENT" />
                <el-option label="普通" value="NORMAL" />
                <el-option label="非紧急" value="LOW" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请时间">
              <el-date-picker
                v-model="advancedDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleAdvancedClose">取消</el-button>
          <el-button @click="resetAdvancedSearch">重置</el-button>
          <el-button type="primary" @click="handleAdvancedSearch">搜索</el-button>
        </div>
      </template>
    </el-dialog>



    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="consultationList" @selection-change="handleSelectionChange" class="consultation-table">
      <el-table-column type="selection" width="50" align="center" />

      <!-- 基本信息列 -->
      <el-table-column label="基本信息" width="280" align="left">
        <template #default="scope">
          <div v-if="scope && scope.row" class="info-cell">
            <div class="info-row primary">
              <span class="request-no">{{ scope.row.requestNo }}</span>
              <el-tag v-if="scope.row.urgencyLevel === 'URGENT'" type="danger" size="small">紧急</el-tag>
              <el-tag v-else-if="scope.row.urgencyLevel === 'NORMAL'" type="primary" size="small">普通</el-tag>
              <el-tag v-else-if="scope.row.urgencyLevel === 'LOW'" type="info" size="small">非紧急</el-tag>
            </div>
            <div class="info-row secondary">
              <span class="patient-info">{{ scope.row.patientStudy?.patientName || '-' }} | {{ scope.row.studyId }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 医生信息列 -->
      <el-table-column label="医生信息" width="200" align="left">
        <template #default="scope">
          <div v-if="scope && scope.row" class="info-cell">
            <div class="info-row primary">
              <span class="doctor-name">申请：{{ scope.row.requesterName || '-' }}</span>
            </div>
            <div class="info-row secondary">
              <span class="consultant-name">会诊：{{ scope.row.consultantName || '-' }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 状态与时间列 -->
      <el-table-column label="状态与时间" width="180" align="center">
        <template #default="scope">
          <div v-if="scope && scope.row" class="info-cell">
            <div class="info-row primary">
              <el-tag v-if="scope.row.status === 'PENDING'" type="warning" size="small">待处理</el-tag>
              <el-tag v-else-if="scope.row.status === 'ACCEPTED'" type="primary" size="small">已接受</el-tag>
              <el-tag v-else-if="scope.row.status === 'REJECTED'" type="danger" size="small">已拒绝</el-tag>
              <el-tag v-else-if="scope.row.status === 'COMPLETED'" type="success" size="small">已完成</el-tag>
              <el-tag v-else-if="scope.row.status === 'CANCELLED'" type="info" size="small">已取消</el-tag>
            </div>
            <div class="info-row secondary">
              <span class="time-info">{{ parseTime(scope.row.requestTime, '{m}-{d} {h}:{i}') }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 申请原因列 -->
      <el-table-column label="申请原因" min-width="200" align="left">
        <template #default="scope">
          <div v-if="scope && scope.row" class="reason-cell">
            <el-tooltip :content="scope.row.requestReason" placement="top" :disabled="!scope.row.requestReason || scope.row.requestReason.length <= 50">
              <span class="reason-text">{{ scope.row.requestReason || '-' }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="130px" align="center" fixed="right">
        <template #default="scope">
          <div v-if="scope && scope.row" class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="handleDetail(scope.row)"
              v-hasPermi="['consultation:management:query']"
              class="action-btn"
            >
              <el-icon><View /></el-icon>
            </el-button>

            <el-dropdown @command="handleCommand" trigger="click">
              <el-button type="info" size="small" class="action-btn">
                <el-icon><More /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    :command="{action: 'logs', row: scope.row}"
                    v-if="checkPermi(['consultation:management:query'])"
                  >
                    <el-icon><Document /></el-icon>
                    操作日志
                  </el-dropdown-item>
                  <el-dropdown-item
                    :command="{action: 'delete', row: scope.row}"
                    v-if="checkPermi(['consultation:management:remove'])"
                    divided
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog title="会诊申请详情" v-model="detailDialogVisible" width="1000px" append-to-body>
      <div v-loading="detailLoading">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="申请编号">{{ detailForm.requestNo }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ parseTime(detailForm.requestTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
          <el-descriptions-item label="申请医生">{{ detailForm.requesterName }}</el-descriptions-item>
          <el-descriptions-item label="申请科室">{{ detailForm.requesterDept }}</el-descriptions-item>
          <el-descriptions-item label="会诊医生">{{ detailForm.consultantName }}</el-descriptions-item>
          <el-descriptions-item label="会诊科室">{{ detailForm.consultantDept }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ detailForm.consultantPhone }}</el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            <el-tag v-if="detailForm.urgencyLevel === 'URGENT'" type="danger" size="small">紧急</el-tag>
            <el-tag v-else-if="detailForm.urgencyLevel === 'NORMAL'" type="primary" size="small">普通</el-tag>
            <el-tag v-else-if="detailForm.urgencyLevel === 'LOW'" type="info" size="small">非紧急</el-tag>
            <span v-else>{{ detailForm.urgencyLevel }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag v-if="detailForm.status === 'PENDING'" type="warning" size="small">待处理</el-tag>
            <el-tag v-else-if="detailForm.status === 'ACCEPTED'" type="primary" size="small">已接受</el-tag>
            <el-tag v-else-if="detailForm.status === 'REJECTED'" type="danger" size="small">已拒绝</el-tag>
            <el-tag v-else-if="detailForm.status === 'COMPLETED'" type="success" size="small">已完成</el-tag>
            <el-tag v-else-if="detailForm.status === 'CANCELLED'" type="info" size="small">已取消</el-tag>
            <span v-else>{{ detailForm.status }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="期望完成时间">{{ parseTime(detailForm.expectedCompletionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        </el-descriptions>

        <!-- 患者信息 -->
        <el-descriptions title="患者信息" :column="2" border style="margin-top: 20px;" v-if="detailForm.patientStudy">
          <el-descriptions-item label="患者姓名">{{ detailForm.patientStudy.patientName }}</el-descriptions-item>
          <el-descriptions-item label="患者ID">{{ detailForm.patientStudy.patientId }}</el-descriptions-item>
          <el-descriptions-item label="检查号">{{ detailForm.studyId }}</el-descriptions-item>
          <el-descriptions-item label="检查部位">{{ detailForm.patientStudy.bodyPart }}</el-descriptions-item>
          <el-descriptions-item label="检查类型">{{ detailForm.patientStudy.modality }}</el-descriptions-item>
          <el-descriptions-item label="检查时间">{{ parseTime(detailForm.patientStudy.studyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        </el-descriptions>

        <!-- 申请信息 -->
        <el-descriptions title="申请信息" :column="1" border style="margin-top: 20px;">
          <el-descriptions-item label="申请原因">
            <div style="white-space: pre-wrap;">{{ detailForm.requestReason }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="临床症状" v-if="detailForm.clinicalSymptoms">
            <div style="white-space: pre-wrap;">{{ detailForm.clinicalSymptoms }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 会诊结果 -->
        <el-descriptions title="会诊结果" :column="1" border style="margin-top: 20px;" v-if="detailForm.consultationResult">
          <el-descriptions-item label="会诊意见">
            <div style="white-space: pre-wrap;">{{ detailForm.consultationResult }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="完成时间" v-if="detailForm.completionTime">
            {{ parseTime(detailForm.completionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 操作日志 -->
        <el-card style="margin-top: 20px;" v-if="detailAuditLogs && detailAuditLogs.length > 0">
          <template #header>
            <span>操作日志</span>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="log in detailAuditLogs"
              :key="log.id"
              :timestamp="parseTime(log.operationTime, '{y}-{m}-{d} {h}:{i}:{s}')"
              placement="top"
            >
              <el-card>
                <div class="log-header">
                  <h4>{{ log.operationDescription || getOperationTypeText(log.operationType) }}</h4>
                  <span class="log-time">{{ parseTime(log.operationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </div>
                <div class="log-content">
                  <p><strong>操作人：</strong>{{ log.operationUserName || '未知' }}</p>
                  <p v-if="log.operationIp"><strong>操作IP：</strong>{{ log.operationIp }}</p>
                  <p v-if="log.remark"><strong>备注：</strong>{{ log.remark }}</p>
                  <p v-if="log.oldStatus && log.newStatus">
                    <strong>状态变更：</strong>
                    <el-tag size="small" type="info">{{ log.oldStatus }}</el-tag>
                    <el-icon style="margin: 0 8px;"><ArrowRight /></el-icon>
                    <el-tag size="small" type="success">{{ log.newStatus }}</el-tag>
                  </p>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 操作日志对话框 -->
    <el-dialog title="操作日志" v-model="auditLogDialogVisible" width="800px" append-to-body>
      <el-timeline>
        <el-timeline-item
          v-for="log in auditLogs"
          :key="log.id"
          :timestamp="parseTime(log.operationTime, '{y}-{m}-{d} {h}:{i}')"
          placement="top">
          <el-card>
            <h4>{{ log.operationDescription }}</h4>
            <p>操作人：{{ log.operationUserName }}</p>
            <p>操作IP：{{ log.operationIp }}</p>
            <p v-if="log.oldStatus && log.newStatus">状态变更：{{ log.oldStatus }} → {{ log.newStatus }}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="auditLogDialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { listConsultationRequest, getConsultationRequest, delConsultationRequest, getConsultationStatistics, getConsultationAuditLogs, handleExpiredConsultations, sendConsultationReminders } from "@/api/consultation/request";
import { Clock, Select, CircleCheck, DataAnalysis, View, More, Document, Delete, Search, CaretTop, CaretBottom, ArrowRight } from '@element-plus/icons-vue';
import { checkPermi } from "@/utils/permission";

export default {
  name: "ConsultationManagement",
  components: {
    Clock,
    Select,
    CircleCheck,
    DataAnalysis,
    View,
    More,
    Document,
    Delete,
    Search,
    CaretTop,
    CaretBottom,
    ArrowRight
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 显示高级搜索弹窗
      showAdvancedSearch: false,
      // 总条数
      total: 0,
      // 会诊申请表格数据
      consultationList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        requestNo: null,
        requesterName: null,
        consultantName: null,
        status: null,
        urgencyLevel: null,
      },
      // 日期范围
      dateRange: [],
      // 高级搜索参数
      advancedParams: {
        requestNo: '',
        requesterName: '',
        consultantName: '',
        status: '',
        urgencyLevel: ''
      },
      // 高级搜索日期范围
      advancedDateRange: [],
      // 统计数据
      statistics: {},
      // 详情对话框
      detailDialogVisible: false,
      detailLoading: false,
      detailForm: {},
      detailAuditLogs: [],
      // 操作日志对话框
      auditLogDialogVisible: false,
      auditLogs: []
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    // 权限检查方法
    checkPermi(permissions) {
      return checkPermi(permissions);
    },
    /** 查询会诊申请列表 */
    getList() {
      this.loading = true;
      const finalParams = this.addDateRange(this.queryParams, this.dateRange);
      console.log('getList - 发送到后端的最终参数:', JSON.stringify(finalParams));
      listConsultationRequest(finalParams).then(response => {
        console.log('getList - 后端返回数据:', response.rows?.length, '条记录');
        this.consultationList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
      }).catch(error => {
        console.error('查询会诊申请列表失败：', error);
        this.loading = false;
        this.$modal.msgError("查询数据失败");
      });
    },
    /** 添加日期范围参数 */
    addDateRange(params, dateRange, propName) {
      let search = { ...params }; // 创建副本避免修改原对象

      if (null != dateRange && '' != dateRange && dateRange.length === 2) {
        if (typeof (propName) === "undefined") {
          // 直接添加到查询对象根级别，而不是params子对象
          search["beginTime"] = dateRange[0];
          search["endTime"] = dateRange[1];
        } else {
          search["begin" + propName] = dateRange[0];
          search["end" + propName] = dateRange[1];
        }
      }
      return search;
    },
    /** 获取统计数据 */
    getStatistics() {
      getConsultationStatistics().then(response => {
        this.statistics = response.data || {};
      }).catch(error => {
        console.error('获取统计数据失败：', error);
        this.statistics = {
          pendingCount: 0,
          acceptedCount: 0,
          completedCount: 0,
          totalCount: 0
        };
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        requestNo: null,
        requesterName: null,
        consultantName: null,
        status: null,
        urgencyLevel: null,
      };
      this.handleQuery();
    },
    /** 高级搜索弹窗关闭处理 */
    handleAdvancedClose() {
      this.showAdvancedSearch = false;
    },
    /** 重置高级搜索 */
    resetAdvancedSearch() {
      this.advancedParams = {
        requestNo: '',
        requesterName: '',
        consultantName: '',
        status: '',
        urgencyLevel: ''
      };
      this.advancedDateRange = [];
    },
    /** 高级搜索 */
    handleAdvancedSearch() {
      // 参数验证
      const hasSearchCondition = this.advancedParams.requestNo ||
                                this.advancedParams.requesterName ||
                                this.advancedParams.consultantName ||
                                this.advancedParams.status ||
                                this.advancedParams.urgencyLevel ||
                                (this.advancedDateRange && this.advancedDateRange.length > 0);

      if (!hasSearchCondition) {
        this.$modal.msgWarning("请至少输入一个搜索条件");
        return;
      }

      // 将高级搜索参数同步到查询参数
      this.queryParams.requestNo = this.advancedParams.requestNo || null;
      this.queryParams.requesterName = this.advancedParams.requesterName || null;
      this.queryParams.consultantName = this.advancedParams.consultantName || null;
      this.queryParams.status = this.advancedParams.status || null;
      this.queryParams.urgencyLevel = this.advancedParams.urgencyLevel || null;

      // 处理日期范围
      this.dateRange = this.advancedDateRange && this.advancedDateRange.length > 0 ? this.advancedDateRange : [];

      // 重置页码
      this.queryParams.pageNum = 1;

      // 关闭弹窗并执行搜索
      this.showAdvancedSearch = false;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 处理下拉菜单命令 */
    handleCommand(command) {
      const { action, row } = command;
      switch (action) {
        case 'logs':
          this.handleAuditLogs(row);
          break;
        case 'delete':
          this.handleDelete(row);
          break;
      }
    },
    /** 按状态筛选 */
    filterByStatus(status) {
      console.log('filterByStatus called with status:', status);
      this.queryParams.status = status;
      this.queryParams.pageNum = 1;
      console.log('queryParams after filter:', JSON.stringify(this.queryParams));
      this.handleQuery();
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      if (!row || !row.id) {
        this.$modal.msgError("无法获取要查看的数据");
        return;
      }

      this.getDetail(row.id);
    },
    /** 获取详情 */
    getDetail(id) {
      this.detailLoading = true;
      this.detailDialogVisible = true;

      getConsultationRequest(id).then(response => {
        this.detailForm = response.data;
        this.detailLoading = false;
        // 同时获取操作日志
        this.getDetailAuditLogs(id);
      }).catch(() => {
        this.detailLoading = false;
        this.$modal.msgError("获取详情失败");
        this.detailDialogVisible = false;
      });
    },
    /** 获取详情操作日志 */
    getDetailAuditLogs(id) {
      getConsultationAuditLogs(id).then(response => {
        this.detailAuditLogs = response.data || [];
      }).catch(() => {
        this.detailAuditLogs = [];
      });
    },
    /** 获取操作类型文本 */
    getOperationTypeText(operationType) {
      const typeMap = {
        'CREATE': '创建申请',
        'ACCEPT': '接受会诊',
        'REJECT': '拒绝会诊',
        'COMPLETE': '完成会诊',
        'CANCEL': '取消申请',
        'UPDATE': '更新信息'
      };
      return typeMap[operationType] || operationType;
    },
    /** 操作日志按钮操作 */
    handleAuditLogs(row) {
      if (!row || !row.id) {
        this.$modal.msgError("无法获取要查看的数据");
        return;
      }

      getConsultationAuditLogs(row.id).then(response => {
        this.auditLogs = response.data || [];
        this.auditLogDialogVisible = true;
      }).catch(error => {
        console.error('获取操作日志失败：', error);
        this.$modal.msgError("获取操作日志失败");
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (!row && (!this.ids || this.ids.length === 0)) {
        this.$modal.msgError("请选择要删除的数据");
        return;
      }

      const ids = row ? row.id : this.ids;
      if (!ids) {
        this.$modal.msgError("无法获取要删除的数据ID");
        return;
      }

      this.$modal.confirm('是否确认删除会诊申请编号为"' + (row ? row.requestNo : ids) + '"的数据项？').then(function() {
        return delConsultationRequest(ids);
      }).then(() => {
        this.getList();
        this.getStatistics();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 处理过期申请 */
    handleExpiredConsultations() {
      this.$modal.confirm('是否确认处理过期的会诊申请？').then(function() {
        return handleExpiredConsultations();
      }).then((response) => {
        this.$modal.msgSuccess(response.msg);
        this.getList();
        this.getStatistics();
      }).catch(() => {});
    },
    /** 发送提醒 */
    handleSendReminders() {
      this.$modal.confirm('是否确认发送会诊提醒？此操作将向所有待处理和即将到期的会诊申请发送提醒消息。').then(function() {
        return sendConsultationReminders();
      }).then((response) => {
        this.$modal.msgSuccess(response.msg);
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('consultation/request/export', {
        ...this.queryParams
      }, `consultation_request_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 0;
  margin-bottom: 16px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-actions .el-button {
  color: #606266;
  font-size: 13px;
}

.header-actions .el-button:hover {
  color: #409eff;
}

/* 统计卡片样式 */
.statistics-section {
  margin-bottom: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  transition: all 0.2s ease;
  cursor: pointer;
}

.stat-card:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.stat-card.pending:hover {
  border-color: #e6a23c;
}

.stat-card.accepted:hover {
  border-color: #409eff;
}

.stat-card.completed:hover {
  border-color: #67c23a;
}

.stat-card.total:hover {
  border-color: #909399;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 4px;
  color: #303133;
}

.stat-label {
  font-size: 13px;
  color: #909399;
}

.stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.stat-card.pending .stat-icon {
  background: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
}

.stat-card.accepted .stat-icon {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.stat-card.completed .stat-icon {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.stat-card.total .stat-icon {
  background: rgba(144, 147, 153, 0.1);
  color: #909399;
}

/* 紧凑搜索栏样式 */
.compact-search-bar {
  background: #fafafa;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.compact-form {
  margin: 0;
}

.compact-form :deep(.el-form-item) {
  margin-right: 16px;
  margin-bottom: 0;
}

.compact-form :deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
  font-size: 14px;
}

.compact-form :deep(.el-button) {
  margin-left: 8px;
}

.compact-form :deep(.el-button--text) {
  color: #409eff;
  padding: 8px 12px;
}

/* 详情弹窗样式 */
.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions :deep(.el-descriptions__label) {
  font-weight: 500;
  color: #606266;
}

.el-descriptions :deep(.el-descriptions__content) {
  color: #303133;
}

.el-timeline :deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}

.el-timeline :deep(.el-card) {
  margin-bottom: 0;
}

.el-timeline :deep(.el-card__body) {
  padding: 12px;
}

/* 操作日志样式 */
.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.log-header h4 {
  margin: 0;
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.log-time {
  color: #909399;
  font-size: 12px;
}

.log-content {
  margin: 0;
}

.log-content p {
  margin: 4px 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
}

.log-content strong {
  color: #303133;
  font-weight: 500;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 20px;
}

.search-card {
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.search-title .el-icon {
  color: #409eff;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409eff;
  font-size: 14px;
}

.search-content {
  padding-top: 16px;
  border-top: 1px solid #f0f2f5;
}

.search-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-buttons .el-button {
  margin-left: 0;
  margin-right: 12px;
}

/* 表格样式 */
.consultation-table {
  border-radius: 8px;
  overflow: hidden;
}

.info-cell {
  padding: 4px 0;
}

.info-row {
  line-height: 1.4;
  margin-bottom: 2px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row.primary {
  font-weight: 500;
  color: #303133;
}

.info-row.secondary {
  font-size: 12px;
  color: #909399;
}

.request-no {
  font-weight: 600;
  margin-right: 8px;
}

.patient-info,
.doctor-name,
.consultant-name {
  font-size: 13px;
}

.time-info {
  font-size: 12px;
}

.reason-cell {
  padding: 4px 0;
}

.reason-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  font-size: 13px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
}

.action-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn .el-icon {
  font-size: 16px;
}

/* 下拉菜单样式 */
.el-dropdown-menu__item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.el-dropdown-menu__item .el-icon {
  font-size: 14px;
}

.statistics-card {
  margin-bottom: 20px;
}

.statistics-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.statistics-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.statistics-icon.pending {
  background: #e6a23c;
}

.statistics-icon.accepted {
  background: #409eff;
}

.statistics-icon.completed {
  background: #67c23a;
}

.statistics-icon.total {
  background: #909399;
}

.statistics-info {
  flex: 1;
}

.statistics-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.statistics-label {
  font-size: 14px;
  color: #606266;
  margin-top: 8px;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
