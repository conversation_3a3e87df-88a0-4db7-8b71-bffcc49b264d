<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <div class="statistics-section">
      <el-row :gutter="12">
        <el-col :span="6">
          <div class="stat-card total" @click="filterByDiagnosisStatus('')">
            <div class="stat-info">
              <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
              <div class="stat-label">总检查数</div>
            </div>
            <div class="stat-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card pending" @click="filterByDiagnosisStatus('-1')">
            <div class="stat-info">
              <div class="stat-number">{{ statistics.pendingCount || 0 }}</div>
              <div class="stat-label">待诊断</div>
            </div>
            <div class="stat-icon">
              <el-icon><Clock /></el-icon>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card diagnosed" @click="filterByDiagnosisStatus('1')">
            <div class="stat-info">
              <div class="stat-number">{{ statistics.diagnosedCount || 0 }}</div>
              <div class="stat-label">已诊断</div>
            </div>
            <div class="stat-icon">
              <el-icon><Edit /></el-icon>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card audited" @click="filterByDiagnosisStatus('2')">
            <div class="stat-info">
              <div class="stat-number">{{ statistics.auditedCount || 0 }}</div>
              <div class="stat-label">已审核</div>
            </div>
            <div class="stat-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 紧凑搜索栏 -->
    <div v-show="showSearch" class="compact-search-bar">
      <el-form :model="queryParams" ref="queryRef" :inline="true" size="small" class="compact-form">
        <el-form-item label="患者ID" prop="originalPatientId">
          <el-input
            v-model="queryParams.originalPatientId"
            placeholder="请输入患者ID"
            clearable
            style="width: 160px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="患者姓名" prop="patientName">
          <el-input
            v-model="queryParams.patientName"
            placeholder="请输入患者姓名"
            clearable
            style="width: 160px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="检查编码" prop="examCode">
          <el-input
            v-model="queryParams.examCode"
            placeholder="请输入检查编码"
            clearable
            style="width: 160px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button type="text" icon="More" @click="showAdvancedSearchDialog = true">更多筛选</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 高级搜索弹窗 -->
    <el-dialog
      v-model="showAdvancedSearchDialog"
      title="高级搜索"
      width="800px"
      :before-close="handleAdvancedClose"
    >
      <el-form :model="advancedParams" label-width="100px">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="原始患者ID">
              <el-input
                v-model="advancedParams.originalPatientId"
                placeholder="请输入原始患者ID"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="患者姓名">
              <el-input
                v-model="advancedParams.patientName"
                placeholder="请输入患者姓名"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查编码">
              <el-input
                v-model="advancedParams.examCode"
                placeholder="请输入检查编码"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="检查类型">
              <el-input
                v-model="advancedParams.modality"
                placeholder="请输入检查类型"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查部位">
              <el-input
                v-model="advancedParams.organ"
                placeholder="请输入检查部位"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查科室">
              <el-input
                v-model="advancedParams.examDepartment"
                placeholder="请输入检查科室"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="检查医生">
              <el-input
                v-model="advancedParams.examDoctorName"
                placeholder="请输入检查医生"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="登记时间">
              <el-date-picker
                v-model="advancedParams.registerTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择登记时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查完成时间">
              <el-date-picker
                v-model="advancedParams.checkFinishTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择检查完成时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleAdvancedClose">取消</el-button>
          <el-button @click="resetAdvancedSearch">重置</el-button>
          <el-button type="primary" @click="handleAdvancedSearch">搜索</el-button>
        </div>
      </template>
    </el-dialog>

    <el-row :gutter="10" class="mb8">

      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['pacs:study:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['pacs:study:export']"
        >导出</el-button>
      </el-col>-->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="studyList" @selection-change="handleSelectionChange" class="study-table">
      <el-table-column type="selection" width="55" align="center" />

      <!-- 主要信息列 - 两行布局 -->
      <el-table-column label="检查记录信息" min-width="600" align="left">
        <template #default="scope">
          <div class="study-record-container">
            <!-- 第一行：主要信息 -->
            <div class="study-main-row">
              <div class="patient-section">
                <el-avatar :size="32" class="patient-avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <div class="patient-basic">
                  <span class="patient-name">{{ scope.row.patientName }}</span>
                  <span class="patient-meta">{{ scope.row.patientSex }} ID: {{ scope.row.originalPatientId }}</span>
                </div>
              </div>

              <div class="exam-section">
                <div class="exam-codes">
                  <span class="exam-code">{{ scope.row.examCode }}</span>
                  <el-tag size="small" :type="getModalityType(scope.row.modality)" class="modality-tag">
                    {{ scope.row.modality }}
                  </el-tag>
                </div>
              </div>

              <div class="time-section">
                <span class="finish-time">{{ formatExamTime(scope.row.checkFinishTime) }}</span>
              </div>
            </div>

            <!-- 第二行：次要信息 -->
            <div class="study-sub-row">
              <div class="contact-section">
                <span class="mobile" v-if="scope.row.mobile">
                  <el-icon class="phone-icon"><Phone /></el-icon>
                  {{ formatPhone(scope.row.mobile) }}
                </span>
              </div>

              <div class="department-section">
                <span class="department">{{ scope.row.examDepartment }}</span>
                <span class="doctor" v-if="scope.row.examDoctorName">{{ scope.row.examDoctorName }}</span>
              </div>

              <div class="register-time-section">
                <span class="register-time">登记: {{ formatTime(scope.row.registerTime) }}</span>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- DICOM状态列 -->
      <el-table-column label="DICOM状态" align="center" width="120">
        <template #default="scope">
          <div class="sync-status-container">
            <el-tag
              :type="getSyncStatusType(scope.row)"
              :icon="getSyncStatusIcon(scope.row)"
              effect="light"
              size="small"
            >
              {{ getSyncStatusText(scope.row) }}
            </el-tag>
            <el-tooltip
              v-if="scope.row.syncErrorMessage"
              :content="scope.row.syncErrorMessage"
              placement="top"
              effect="dark"
            >
              <el-icon class="error-icon" color="#f56c6c">
                <Warning />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>

      <!-- 同步信息列 -->
      <el-table-column label="同步信息" align="center" width="200">
        <template #default="scope">
          <div v-if="scope.row.syncErrorCode && scope.row.syncErrorCode !== 'SUCCESS'" class="sync-error-info">
            <div class="error-code">{{ getErrorCodeText(scope.row.syncErrorCode) }}</div>
            <div class="error-message" v-if="scope.row.syncErrorMessage">
              {{ scope.row.syncErrorMessage }}
            </div>
            <div class="sync-time" v-if="scope.row.lastSyncAttemptTime">
              最后尝试: {{ formatTime(scope.row.lastSyncAttemptTime) }}
            </div>
            <div class="retry-count" v-if="scope.row.syncRetryCount > 0">
              重试次数: {{ scope.row.syncRetryCount }}
            </div>
          </div>
          <div v-else-if="scope.row.dicomSyncFlag === '1'" class="sync-success-info">
            <div class="success-text">同步成功</div>
            <div class="sync-time" v-if="scope.row.syncTime">
              {{ formatTime(scope.row.syncTime) }}
            </div>
          </div>
          <div v-else class="sync-pending-info">
            <span class="pending-text">待同步</span>
          </div>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="180">
        <template #default="scope">
          <div class="action-buttons">
            <el-button link type="primary" size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button
              link
              type="primary"
              size="small"
              @click="handleSync(scope.row)"
              :loading="scope.row.syncing"
            >
              {{ getSyncButtonText(scope.row) }}
            </el-button>
            <el-button
              v-if="needShowRetryButton(scope.row)"
              link
              type="warning"
              size="small"
              @click="handleRetrySync(scope.row)"
              :loading="scope.row.syncing"
            >
              重试
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看详情对话框 -->
    <el-dialog title="检查记录详情" v-model="viewOpen" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="原始患者ID">{{ form.originalPatientId }}</el-descriptions-item>
        <el-descriptions-item label="患者姓名">{{ form.patientName }}</el-descriptions-item>
        <el-descriptions-item label="检查编码">{{ form.examCode }}</el-descriptions-item>
        <el-descriptions-item label="检查类型">{{ form.modality }}</el-descriptions-item>
        <el-descriptions-item label="检查部位">{{ form.organ }}</el-descriptions-item>
        <el-descriptions-item label="检查科室">{{ form.examDepartment }}</el-descriptions-item>
        <el-descriptions-item label="检查医生">{{ form.examDoctorName }}</el-descriptions-item>
        <el-descriptions-item label="登记时间">{{ parseTime(form.registerTime) }}</el-descriptions-item>
        <el-descriptions-item label="检查完成时间">{{ parseTime(form.checkFinishTime) }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ form.mobile }}</el-descriptions-item>
        <el-descriptions-item label="患者来源">{{ form.patientFrom }}</el-descriptions-item>
        <el-descriptions-item label="床号">{{ form.bedNo }}</el-descriptions-item>
        <el-descriptions-item label="检查项目">{{ form.examItem }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ form.idNo }}</el-descriptions-item>
        <el-descriptions-item label="社保卡号">{{ form.socialSecurityCardNo }}</el-descriptions-item>
        <el-descriptions-item label="就诊卡号">{{ form.medicalCardNo }}</el-descriptions-item>
        <el-descriptions-item label="设备名称">{{ form.device }}</el-descriptions-item>
        <el-descriptions-item label="同步时间">{{ parseTime(form.syncTime) }}</el-descriptions-item>
        <el-descriptions-item label="DICOM状态">
          <el-tag
            :type="getSyncStatusType(form)"
            effect="light"
          >
            {{ getSyncStatusText(form) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="同步错误代码" v-if="form.syncErrorCode && form.syncErrorCode !== 'SUCCESS'">
          {{ getErrorCodeText(form.syncErrorCode) }}
        </el-descriptions-item>
        <el-descriptions-item label="同步错误信息" v-if="form.syncErrorMessage" :span="2">
          {{ form.syncErrorMessage }}
        </el-descriptions-item>
        <el-descriptions-item label="最后同步尝试" v-if="form.lastSyncAttemptTime">
          {{ parseTime(form.lastSyncAttemptTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="重试次数" v-if="form.syncRetryCount > 0">
          {{ form.syncRetryCount }}
        </el-descriptions-item>
        <el-descriptions-item label="病史" :span="2">{{ form.medicalHistory }}</el-descriptions-item>
        <el-descriptions-item label="临床症状" :span="2">{{ form.clinicalSymptom }}</el-descriptions-item>
        <el-descriptions-item label="临床诊断" :span="2">{{ form.clinicalDiagnosis }}</el-descriptions-item>
        <el-descriptions-item label="院内影像诊断" :span="2">{{ form.reportDiagnose }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 添加或修改检查记录对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="studyRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="医院ID" prop="hospitalId">
          <el-input v-model="form.hospitalId" placeholder="请输入医院ID" />
        </el-form-item>
        <el-form-item label="医院名称" prop="hospitalName">
          <el-input v-model="form.hospitalName" placeholder="请输入医院名称" />
        </el-form-item>
        <el-form-item label="原始患者ID" prop="originalPatientId">
          <el-input v-model="form.originalPatientId" placeholder="请输入原始患者ID" />
        </el-form-item>
        <el-form-item label="患者姓名" prop="patientName">
          <el-input v-model="form.patientName" placeholder="请输入患者姓名" />
        </el-form-item>
        <el-form-item label="原始检查编码" prop="originalExamCode">
          <el-input v-model="form.originalExamCode" placeholder="请输入原始检查编码" />
        </el-form-item>
        <el-form-item label="检查编码" prop="examCode">
          <el-input v-model="form.examCode" placeholder="请输入检查编码" />
        </el-form-item>
        <el-form-item label="检查类型" prop="modality">
          <el-input v-model="form.modality" placeholder="请输入检查类型" />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="form.mobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="HIS患者ID" prop="hisPatientId">
          <el-input v-model="form.hisPatientId" placeholder="请输入HIS患者ID" />
        </el-form-item>
        <el-form-item label="门诊ID" prop="outPatientId">
          <el-input v-model="form.outPatientId" placeholder="请输入门诊ID" />
        </el-form-item>
        <el-form-item label="住院ID" prop="inPatientId">
          <el-input v-model="form.inPatientId" placeholder="请输入住院ID" />
        </el-form-item>
        <el-form-item label="检查患者ID" prop="examPatientId">
          <el-input v-model="form.examPatientId" placeholder="请输入检查患者ID" />
        </el-form-item>
        <el-form-item label="患者来源" prop="patientFrom">
          <el-input v-model="form.patientFrom" placeholder="请输入患者来源" />
        </el-form-item>
        <el-form-item label="床号" prop="bedNo">
          <el-input v-model="form.bedNo" placeholder="请输入床号" />
        </el-form-item>
        <el-form-item label="患者出生日期" prop="patientBirthday">
          <el-date-picker clearable
            v-model="form.patientBirthday"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择患者出生日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检查部位" prop="organ">
          <el-input v-model="form.organ" placeholder="请输入检查部位" />
        </el-form-item>
        <el-form-item label="检查项目" prop="examItem">
          <el-input v-model="form.examItem" placeholder="请输入检查项目" />
        </el-form-item>
        <el-form-item label="检查科室" prop="examDepartment">
          <el-input v-model="form.examDepartment" placeholder="请输入检查科室" />
        </el-form-item>
        <el-form-item label="检查医生" prop="examDoctorName">
          <el-input v-model="form.examDoctorName" placeholder="请输入检查医生" />
        </el-form-item>
        <el-form-item label="登记时间" prop="registerTime">
          <el-date-picker clearable
            v-model="form.registerTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择登记时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="预约时间" prop="reserveTime">
          <el-date-picker clearable
            v-model="form.reserveTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择预约时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="预约到达时间" prop="reserveArrivalTime">
          <el-date-picker clearable
            v-model="form.reserveArrivalTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择预约到达时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检查完成时间" prop="checkFinishTime">
          <el-date-picker clearable
            v-model="form.checkFinishTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择检查完成时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="身份证号" prop="idNo">
          <el-input v-model="form.idNo" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="社保卡号" prop="socialSecurityCardNo">
          <el-input v-model="form.socialSecurityCardNo" placeholder="请输入社保卡号" />
        </el-form-item>
        <el-form-item label="就诊卡号" prop="medicalCardNo">
          <el-input v-model="form.medicalCardNo" placeholder="请输入就诊卡号" />
        </el-form-item>
        <el-form-item label="病史" prop="medicalHistory">
          <el-input v-model="form.medicalHistory" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="临床症状" prop="clinicalSymptom">
          <el-input v-model="form.clinicalSymptom" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="临床诊断" prop="clinicalDiagnosis">
          <el-input v-model="form.clinicalDiagnosis" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="设备名称" prop="device">
          <el-input v-model="form.device" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="同步时间" prop="syncTime">
          <el-date-picker clearable
            v-model="form.syncTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择同步时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="删除标志" prop="isDeleted">
          <el-input v-model="form.isDeleted" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="dicom数据同步状态" prop="dicomSyncFlag">
          <el-input v-model="form.dicomSyncFlag" placeholder="请输入dicom数据同步状态" />
        </el-form-item>
        <el-form-item label="${comment}" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入${comment}" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Study">
import {listStudy, getStudy, delStudy, addStudy, updateStudy, syncDicomData} from "@/api/pacs/study";
import {getDiagnosisStatusCount} from "@/api/dashboard/index";
import {User, Phone, Warning, Clock, CircleCheck, DataAnalysis, Edit} from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

const studyList = ref([]);
const open = ref(false);
const viewOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const showAdvancedSearch = ref(false);
const showAdvancedSearchDialog = ref(false); // 控制高级搜索弹窗
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 统计数据
const statistics = ref({
  totalCount: 0,
  pendingCount: 0,    // 待诊断
  diagnosedCount: 0,  // 已诊断
  auditedCount: 0     // 已审核
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    hospitalId: null,
    hospitalName: null,
    originalPatientId: null,
    patientName: null,
    originalExamCode: null,
    examCode: null,
    modality: null,
    mobile: null,
    patientSex: null,
    hisPatientId: null,
    outPatientId: null,
    inPatientId: null,
    examPatientId: null,
    patientFrom: null,
    bedNo: null,
    patientBirthday: null,
    organ: null,
    examItem: null,
    examDepartment: null,
    examDoctorName: null,
    registerTime: null,
    reserveTime: null,
    reserveArrivalTime: null,
    checkFinishTime: null,
    idNo: null,
    socialSecurityCardNo: null,
    medicalCardNo: null,
    medicalHistory: null,
    clinicalSymptom: null,
    clinicalDiagnosis: null,
    device: null,
    syncTime: null,
    isDeleted: null,
    dicomSyncFlag: null,
    diagnosisStatus: null,
  },
  rules: {
    hospitalId: [
      { required: true, message: "医院ID不能为空", trigger: "blur" }
    ],
    hospitalName: [
      { required: true, message: "医院名称不能为空", trigger: "blur" }
    ],
    originalPatientId: [
      { required: true, message: "原始患者ID不能为空", trigger: "blur" }
    ],
    originalExamCode: [
      { required: true, message: "原始检查编码不能为空", trigger: "blur" }
    ],
    examCode: [
      { required: true, message: "检查编码不能为空", trigger: "blur" }
    ],
    syncTime: [
      { required: true, message: "同步时间不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

// 高级搜索参数
const advancedParams = reactive({
  originalPatientId: '',
  patientName: '',
  examCode: '',
  modality: '',
  organ: '',
  examDepartment: '',
  examDoctorName: '',
  registerTime: '',
  checkFinishTime: ''
});

/** 查询检查记录列表 */
function getList() {
  loading.value = true;
  listStudy(queryParams.value).then(response => {
    studyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

/** 同步按钮操作 */
function handleSync(record) {
  if (record.originalPatientId == null) {
    proxy.$modal.msgError("记录缺少患者ID");
    return;
  }

  // 设置同步状态
  record.syncing = true;

  syncDicomData(record.originalPatientId).then(response => {
    proxy.$modal.msgSuccess("同步成功");
    getList();
  }).catch(error => {
    proxy.$modal.msgError("同步失败: " + (error.msg || error.message || "未知错误"));
    getList(); // 刷新列表以显示最新的错误信息
  }).finally(() => {
    record.syncing = false;
  });
}

/** 重试同步操作 */
function handleRetrySync(record) {
  proxy.$modal.confirm('确认要重新同步该患者的影像数据吗？').then(() => {
    handleSync(record);
  }).catch(() => {});
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    hospitalId: null,
    hospitalName: null,
    originalPatientId: null,
    patientName: null,
    originalExamCode: null,
    examCode: null,
    modality: null,
    mobile: null,
    patientSex: null,
    hisPatientId: null,
    outPatientId: null,
    inPatientId: null,
    examPatientId: null,
    patientFrom: null,
    bedNo: null,
    patientBirthday: null,
    organ: null,
    examItem: null,
    examDepartment: null,
    examDoctorName: null,
    registerTime: null,
    reserveTime: null,
    reserveArrivalTime: null,
    checkFinishTime: null,
    idNo: null,
    socialSecurityCardNo: null,
    medicalCardNo: null,
    medicalHistory: null,
    clinicalSymptom: null,
    clinicalDiagnosis: null,
    device: null,
    syncTime: null,
    updateTime: null,
    isDeleted: null,
    dicomSyncFlag: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    remark: null
  };
  proxy.resetForm("studyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
  getStatistics(); // 搜索后更新统计数据
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 高级搜索弹窗关闭处理 */
function handleAdvancedClose() {
  showAdvancedSearchDialog.value = false;
}

/** 重置高级搜索 */
function resetAdvancedSearch() {
  advancedParams.originalPatientId = '';
  advancedParams.patientName = '';
  advancedParams.examCode = '';
  advancedParams.modality = '';
  advancedParams.organ = '';
  advancedParams.examDepartment = '';
  advancedParams.examDoctorName = '';
  advancedParams.registerTime = '';
  advancedParams.checkFinishTime = '';
}

/** 高级搜索 */
function handleAdvancedSearch() {
  // 将高级搜索参数同步到查询参数
  queryParams.value.originalPatientId = advancedParams.originalPatientId;
  queryParams.value.patientName = advancedParams.patientName;
  queryParams.value.examCode = advancedParams.examCode;
  queryParams.value.modality = advancedParams.modality;
  queryParams.value.organ = advancedParams.organ;
  queryParams.value.examDepartment = advancedParams.examDepartment;
  queryParams.value.examDoctorName = advancedParams.examDoctorName;
  queryParams.value.registerTime = advancedParams.registerTime;
  queryParams.value.checkFinishTime = advancedParams.checkFinishTime;

  // 关闭弹窗并执行搜索
  showAdvancedSearchDialog.value = false;
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加检查记录";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getStudy(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改检查记录";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["studyRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateStudy(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addStudy(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除检查记录编号为"' + _ids + '"的数据项？').then(function() {
    return delStudy(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('pacs/study/export', {
    ...queryParams.value
  }, `study_${new Date().getTime()}.xlsx`)
}

/** 查看按钮操作 */
function handleView(row) {
  reset();
  const _id = row.id || ids.value
  getStudy(_id).then(response => {
    form.value = response.data;
    viewOpen.value = true;
  });
}

/** 切换高级搜索显示状态 */
function toggleAdvancedSearch() {
  showAdvancedSearch.value = !showAdvancedSearch.value;
}

/** 获取同步状态类型 */
function getSyncStatusType(row) {
  if (row.dicomSyncFlag === '1') {
    return 'success'; // 完全成功
  } else if (row.dicomSyncFlag === '2') {
    return 'warning'; // 部分成功
  } else if (row.dicomSyncFlag === '0') {
    return 'danger'; // 失败
  } else {
    return 'info'; // 待同步
  }
}

/** 获取同步状态图标 */
function getSyncStatusIcon(row) {
  if (row.dicomSyncFlag === '1') {
    return 'CircleCheck';
  } else if (row.dicomSyncFlag === '2') {
    return 'Warning';
  } else if (row.dicomSyncFlag === '0') {
    return 'CircleClose';
  } else {
    return 'Clock';
  }
}

/** 获取同步状态文本 */
function getSyncStatusText(row) {
  if (row.dicomSyncFlag === '1') {
    return '已同步';
  } else if (row.dicomSyncFlag === '2') {
    return '部分成功';
  } else if (row.dicomSyncFlag === '0') {
    return '同步失败';
  } else {
    return '待同步';
  }
}

/** 获取错误代码文本 */
function getErrorCodeText(errorCode) {
  const errorCodeMap = {
    'SUCCESS': '成功',
    'NO_STUDIES_FOUND': '未找到研究',
    'CONNECTIVITY_ERROR': '连接错误',
    'PERMISSION_DENIED': '权限不足',
    'TIMEOUT': '超时',
    'PARTIAL_FAILURE': '部分失败',
    'SYNC_FAILURE': '同步失败',
    'SYSTEM_ERROR': '系统错误',
    'INTERRUPTED': '中断',
    'PENDING': '待同步'
  };
  return errorCodeMap[errorCode] || errorCode;
}

/** 获取同步按钮文本 */
function getSyncButtonText(row) {
  if (row.syncing) {
    return '同步中...';
  } else if (row.dicomSyncFlag === '1') {
    return '重新同步';
  } else {
    return '同步影像';
  }
}

/** 判断是否需要显示重试按钮 */
function needShowRetryButton(row) {
  return row.dicomSyncFlag === '0' || row.dicomSyncFlag === '2';
}

/** 获取检查方式类型 */
function getModalityType(modality) {
  const modalityTypes = {
    'CT': 'primary',
    'MR': 'success',
    'MG': 'warning',
    'DX': 'info',
    'CR': 'info'
  };
  return modalityTypes[modality] || 'info';
}

/** 格式化手机号 */
function formatPhone(phone) {
  if (!phone) return '';
  // 格式化为 138****1234 的形式
  if (phone.length === 11) {
    return phone.substring(0, 3) + '****' + phone.substring(7);
  }
  return phone;
}

/** 格式化检查时间 */
function formatExamTime(time) {
  if (!time) return '未完成';
  const date = new Date(time);
  const now = new Date();
  const diffTime = now - date;
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays === 1) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays < 7) {
    return diffDays + '天前';
  } else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
  }
}

/** 格式化时间（简短格式） */
function formatTime(time) {
  if (!time) return '';
  const date = new Date(time);
  return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }) + ' ' +
         date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
}

/** 获取统计数据 */
function getStatistics() {
  // 构建查询参数，基于当前的筛选条件获取统计
  const params = {};

  // 只传递有值的筛选条件
  if (queryParams.value.originalPatientId) {
    params.patientId = queryParams.value.originalPatientId;
  }
  if (queryParams.value.patientName) {
    params.patientName = queryParams.value.patientName;
  }
  if (queryParams.value.examCode) {
    params.examCode = queryParams.value.examCode;
  }
  if (queryParams.value.examItem) {
    params.examItem = queryParams.value.examItem;
  }
  if (queryParams.value.hospitalId) {
    params.hospitalId = queryParams.value.hospitalId;
  }
  if (queryParams.value.hospitalName) {
    params.hospitalName = queryParams.value.hospitalName;
  }

  // 不传递scenario参数，获取全部状态的统计

  getDiagnosisStatusCount(params).then(response => {
    if (response.code === 200 && response.data) {
      const data = response.data;
      statistics.value = {
        pendingCount: data.pending || 0,     // 待诊断
        diagnosedCount: data.diagnosed || 0,    // 已诊断
        auditedCount: data.audited || 0,      // 已审核
        totalCount: (data.pending || 0) + (data.diagnosed || 0) + (data.audited || 0) + (data.rejected || 0)
      };
    }
  }).catch(error => {
    console.error('获取统计数据失败：', error);
    statistics.value = {
      totalCount: 0,
      pendingCount: 0,
      diagnosedCount: 0,
      auditedCount: 0
    };
  });
}

/** 根据诊断状态筛选 */
function filterByDiagnosisStatus(status) {
  // 重置查询参数
  queryParams.value.pageNum = 1;

  if (status === '') {
    // 显示全部
    queryParams.value.diagnosisStatus = null;
  } else {
    // 设置诊断状态筛选
    queryParams.value.diagnosisStatus = status;
  }

  getList();
  // 筛选后重新获取统计数据
  getStatistics();
}

getList();
getStatistics();
</script>

<style scoped>
.app-container {
  padding: 20px;
}

/* 统计卡片样式 */
.statistics-section {
  margin-bottom: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  transition: all 0.2s ease;
  cursor: pointer;
}

.stat-card:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.stat-card.total:hover {
  border-color: #909399;
}

.stat-card.pending:hover {
  border-color: #e6a23c;
}

.stat-card.diagnosed:hover {
  border-color: #409eff;
}

.stat-card.audited:hover {
  border-color: #67c23a;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 13px;
  color: #909399;
  line-height: 1;
}

.stat-icon {
  font-size: 20px;
  color: #c0c4cc;
  opacity: 0.8;
}

.stat-card.total .stat-icon {
  color: #909399;
}

.stat-card.pending .stat-icon {
  color: #e6a23c;
}

.stat-card.diagnosed .stat-icon {
  color: #409eff;
}

.stat-card.audited .stat-icon {
  color: #67c23a;
}

/* 紧凑搜索栏样式 */
.compact-search-bar {
  background: #fafafa;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.compact-form {
  margin: 0;
}

.compact-form :deep(.el-form-item) {
  margin-right: 16px;
  margin-bottom: 0;
}

.compact-form :deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
  font-size: 14px;
}

.compact-form :deep(.el-button) {
  margin-left: 8px;
}

.compact-form :deep(.el-button--text) {
  color: #409eff;
  padding: 8px 12px;
}

/* 高级搜索容器 */
.advanced-search-container {
  display: contents; /* 让容器不影响表单的inline布局 */
}

/* 高级搜索区域的过渡动画 */
.advanced-search-enter-active,
.advanced-search-leave-active {
  transition: all 0.3s ease;
}

.advanced-search-enter-from,
.advanced-search-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 展开/收起按钮样式 */
.el-button--text {
  color: #409eff;
  font-size: 14px;
}

.el-button--text:hover {
  color: #66b1ff;
}

/* 搜索表单样式优化 */
.el-form--inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

.el-form--inline .el-form-item:last-child {
  margin-right: 0;
}

/* 两行布局样式 */
.study-table :deep(.el-table__row) {
  height: auto !important;
  min-height: 70px;
}

.study-table :deep(.el-table__row td) {
  padding: 0 !important;
  vertical-align: middle;
  border-bottom: 1px solid #f0f0f0;
}

.study-record-container {
  padding: 12px 0;
  line-height: 1.4;
}

/* 第一行：主要信息 */
.study-main-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
  min-height: 36px;
}

/* 患者基本信息区域 */
.patient-section {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 140px;
  flex-shrink: 0;
}

.patient-avatar {
  border: 2px solid #f0f8ff;
  box-shadow: 0 2px 4px rgba(0, 102, 204, 0.1);
}

.patient-basic {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.patient-name {
  font-size: 15px;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
}

.patient-meta {
  font-size: 12px;
  color: #8c8c8c;
}

/* 检查编号和方式区域 */
.exam-section {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  flex-shrink: 0;
}

.exam-codes {
  display: flex;
  align-items: center;
  gap: 8px;
}

.exam-code {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  font-family: 'Courier New', monospace;
}

.modality-tag {
  flex-shrink: 0;
}

/* 时间区域 */
.time-section {
  min-width: 100px;
  flex-shrink: 0;
  text-align: right;
}

.finish-time {
  font-size: 13px;
  color: #0066cc;
  font-weight: 500;
}

/* 第二行：次要信息 */
.study-sub-row {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #8c8c8c;
  min-height: 20px;
}

/* 联系方式区域 */
.contact-section {
  min-width: 140px;
  flex-shrink: 0;
  margin-left: 40px; /* 对齐头像右侧 */
}

.mobile {
  display: flex;
  align-items: center;
  gap: 4px;
}

.phone-icon {
  font-size: 12px;
}

/* 科室医生区域 */
.department-section {
  flex: 1;
  min-width: 0;
  display: flex;
  gap: 12px;
}

.department,
.doctor {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 登记时间区域 */
.register-time-section {
  min-width: 100px;
  flex-shrink: 0;
  text-align: right;
  font-size: 11px;
}

/* 同步状态相关样式 */
.sync-status-container {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.error-icon {
  cursor: pointer;
}

/* 同步信息样式 */
.sync-error-info,
.sync-success-info,
.sync-pending-info {
  font-size: 12px;
  text-align: center;
}

.sync-error-info {
  color: #f56c6c;
}

.sync-success-info {
  color: #67c23a;
}

.sync-pending-info {
  color: #e6a23c;
}

.error-code {
  font-weight: 600;
  margin-bottom: 4px;
}

.error-message {
  margin-bottom: 4px;
  font-size: 11px;
  line-height: 1.2;
}

.sync-time,
.retry-count {
  font-size: 11px;
  color: #8c8c8c;
}

.success-text,
.pending-text {
  font-weight: 500;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

/* 表格行悬停效果 */
.study-table :deep(.el-table__row:hover) {
  background-color: #f8fbff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 102, 204, 0.08);
  transition: all 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .study-main-row {
    gap: 12px;
  }

  .study-sub-row {
    gap: 12px;
  }

  .patient-section {
    min-width: 120px;
  }

  .exam-section {
    min-width: 100px;
  }
}

@media (max-width: 1200px) {
  .patient-name {
    font-size: 14px;
  }

  .exam-code {
    font-size: 13px;
  }

  .study-main-row {
    gap: 8px;
  }

  .study-sub-row {
    gap: 8px;
  }
}
</style>
