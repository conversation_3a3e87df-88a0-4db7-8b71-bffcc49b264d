<template>
  <div class="app-container">
    <el-card header="系统配置图片上传测试">
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="配置键名">
          <el-input v-model="testForm.configKey" placeholder="请输入配置键名" />
        </el-form-item>
        
        <el-form-item label="参数类型">
          <el-select v-model="testForm.valueType" placeholder="请选择参数类型">
            <el-option label="文本" value="text" />
            <el-option label="图片" value="image" />
            <el-option label="文件" value="file" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="参数值">
          <!-- 文本类型 -->
          <el-input 
            v-if="testForm.valueType === 'text'"
            v-model="testForm.configValue" 
            type="textarea" 
            placeholder="请输入参数值" 
          />
          
          <!-- 图片类型 -->
          <div v-else-if="testForm.valueType === 'image'">
            <config-image-upload 
              v-model="testForm.configValue"
              :config-key="testForm.configKey"
            />
            <p class="upload-tip">当前图片URL: {{ testForm.configValue || '未设置' }}</p>
          </div>
          
          <!-- 文件类型 -->
          <file-upload 
            v-else-if="testForm.valueType === 'file'"
            v-model="testForm.configValue"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testUpload">测试上传</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 调试信息 -->
      <el-card header="调试信息" style="margin-top: 20px;">
        <pre>{{ JSON.stringify(testForm, null, 2) }}</pre>
      </el-card>
    </el-card>
  </div>
</template>

<script setup name="ConfigImageUploadTest">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import ConfigImageUpload from "@/components/ConfigImageUpload";
import FileUpload from "@/components/FileUpload";

// 测试表单数据
const testForm = reactive({
  configKey: 'hospital.logo.url',
  valueType: 'image',
  configValue: ''
});

// 测试上传功能
function testUpload() {
  if (!testForm.configKey) {
    ElMessage.error('请输入配置键名');
    return;
  }
  
  if (testForm.valueType === 'image' && !testForm.configValue) {
    ElMessage.error('请先上传图片');
    return;
  }
  
  ElMessage.success('测试成功！配置值：' + testForm.configValue);
}

// 重置表单
function resetForm() {
  testForm.configKey = 'hospital.logo.url';
  testForm.valueType = 'image';
  testForm.configValue = '';
}
</script>

<style scoped>
.upload-tip {
  margin-top: 10px;
  color: #666;
  font-size: 12px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
}
</style>
