<template>
  <div class="report-test-container">
    <el-card header="ReportViewer 调试测试">
      <el-space direction="vertical" style="width: 100%">
        <el-button type="primary" @click="testBasicReport">测试基础报告</el-button>
        <el-button type="success" @click="testWithData">测试带数据报告</el-button>
        <el-button type="warning" @click="testFontConfig">测试字体配置</el-button>
        <el-button type="info" @click="testDynamicTemplate">测试动态模板</el-button>
        
        <el-divider>调试信息</el-divider>
        <el-input
          v-model="debugInfo"
          type="textarea"
          :rows="10"
          readonly
          placeholder="调试信息将显示在这里..."
        />
      </el-space>
    </el-card>
    
    <!-- ReportViewer 组件 -->
    <ReportViewer ref="reportViewerRef" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ReportViewer from '@/components/Report/ReportViewer.vue';
import { getFontConfigResourceUrl, updateFontConfigPaths } from '@/utils/resource';

const reportViewerRef = ref();
const debugInfo = ref('');

// 添加调试信息
function addDebugInfo(message) {
  const timestamp = new Date().toLocaleTimeString();
  debugInfo.value += `[${timestamp}] ${message}\n`;
}

// 检查环境
function checkEnvironment() {
  addDebugInfo('=== 环境检查 ===');
  addDebugInfo(`BASE_URL: ${import.meta.env.BASE_URL}`);
  addDebugInfo(`MODE: ${import.meta.env.MODE}`);
  addDebugInfo(`字体配置URL: ${getFontConfigResourceUrl()}`);
}

// 测试字体配置
async function testFontConfig() {
  addDebugInfo('=== 测试字体配置 ===');
  try {
    const fontConfigUrl = getFontConfigResourceUrl();
    addDebugInfo(`字体配置URL: ${fontConfigUrl}`);

    const response = await fetch(fontConfigUrl);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const fontConfig = await response.json();
    const updatedConfig = updateFontConfigPaths(fontConfig);

    // 测试字体文件是否可访问
    for (const descriptor of updatedConfig.descriptors) {
      try {
        const fontResponse = await fetch(descriptor.source, { method: 'HEAD' });
        addDebugInfo(`字体文件 ${descriptor.name}: ${fontResponse.status}`);
      } catch (fontError) {
        addDebugInfo(`字体文件 ${descriptor.name} 加载失败: ${fontError.message}`);
      }
    }

  } catch (error) {
    addDebugInfo(`字体配置测试失败: ${error.message}`);
  }
}

// 测试基础报告
function testBasicReport() {
  addDebugInfo('=== 测试基础报告 ===');

  const basicTemplate = {
    "$schema": "https://www.grapecity.com/activereportsjs/schemas/report-15.json",
    "Type": "report",
    "Version": "15.1.0",
    "Name": "BasicTest",
    "Body": {
      "Type": "bandedReportItem",
      "Name": "body",
      "Height": "11in",
      "Sections": [
        {
          "Type": "detail",
          "Name": "detail",
          "Height": "2in",
          "Items": [
            {
              "Type": "textBox",
              "Name": "testText",
              "Value": "这是一个测试报告",
              "Style": {
                "fontSize": "16pt",
                "textAlign": "center",
                "fontFamily": "Arial"
              },
              "Location": "1in, 0.5in",
              "Size": "6in, 1in"
            }
          ]
        }
      ]
    }
  };

  reportViewerRef.value.open({
    filename: '基础测试报告',
    template: basicTemplate
  });
}

// 测试带数据的报告
function testWithData() {
  addDebugInfo('=== 测试带数据报告 ===');

  const testData = {
    hospitalName: "测试医院",
    reportTitle: "测试报告",
    patientName: "张三",
    patientSex: "男",
    diagnosis: {
      diagnose: "测试诊断内容",
      doctor: "测试医生",
      recommendation: "测试建议"
    },
    study: {
      patientName: "张三",
      patientSex: "男",
      age: 30,
      examItem: "胸部CT",
      examDepartment: "放射科"
    }
  };

  reportViewerRef.value.open({
    filename: '带数据测试报告',
    data: testData
  });
}

// 测试静态模板（不传入数据，使用内嵌数据）
function testStaticTemplate() {
  addDebugInfo('=== 测试静态模板（使用内嵌数据） ===');

  addDebugInfo('直接打开静态模板，不传入任何数据，使用模板内嵌的测试数据');

  reportViewerRef.value.open({
    filename: '静态模板测试',
    // 不传入data，让模板使用内嵌的测试数据
  });
}

// 测试动态模板和数据
function testDynamicTemplate() {
  addDebugInfo('=== 测试动态模板和数据 ===');

  // 简化的动态模板
  const dynamicTemplate = {
    "Name": "DynamicTestReport",
    "DataSources": [
      {
        "Name": "JsonDataSource",
        "ConnectionProperties": {
          "DataProvider": "JSON",
          "ConnectString": "jsondata="
        }
      }
    ],
    "ReportSections": [
      {
        "Type": "Detail",
        "Name": "detail",
        "Height": "2in",
        "Items": [
          {
            "Type": "TextBox",
            "Name": "patientName",
            "Value": "患者姓名：=Fields!study.patientName.Value",
            "Style": { "fontSize": "12pt" },
            "Location": "0.5in, 0.2in",
            "Size": "3in, 0.3in"
          }
        ]
      }
    ]
  };

  // 测试数据
  const testData = {
    study: {
      patientName: "张三"
    }
  };

  reportViewerRef.value.open({
    filename: '动态模板测试报告',
    templateJson: JSON.stringify(dynamicTemplate),
    data: testData
  });
}

// 测试模板修复功能
function testTemplateHelper() {
  addDebugInfo('=== 测试模板修复功能 ===');

  // 创建一个包含问题的模板
  const problematicTemplate = {
    "Name": "ProblematicTemplate",
    "DataSources": [
      {
        "Name": "JsonDataSource",
        "ConnectionProperties": {
          "DataProvider": "JSON",
          "ConnectString": "jsondata="
        }
      }
    ],
    "Body": {
      "Type": "bandedReportItem",
      "Name": "body",
      "Height": "11in",
      "ReportItems": [
        {
          "Type": "textBox",
          "Name": "checkNumber",
          "Value": "检查号：",
          "Style": {
            "fontSize": "12pt"
          },
          "Location": "1in, 1in",
          "Size": "2in, 0.5in"
        },
        {
          "Type": "textBox",
          "Name": "patientName",
          "Value": "患者姓名：",
          "Style": {
            "fontSize": "12pt"
          },
          "Location": "1in, 1.5in",
          "Size": "2in, 0.5in"
        }
      ]
    }
  };

  addDebugInfo('原始问题模板:');
  addDebugInfo(JSON.stringify(problematicTemplate, null, 2));

  // 验证模板
  const validation = validateTemplate(problematicTemplate);
  addDebugInfo(`模板验证结果: ${JSON.stringify(validation, null, 2)}`);

  // 修复模板
  const fixedTemplate = fixReportTemplate(problematicTemplate);
  addDebugInfo('修复后的模板:');
  addDebugInfo(JSON.stringify(fixedTemplate, null, 2));

  // 测试修复后的模板
  addDebugInfo('使用修复后的模板打开报告...');
  reportViewerRef.value.open({
    filename: '修复测试报告',
    template: fixedTemplate,
    data: {
      checkNumber: 'TEST001',
      patientName: '测试患者'
    }
  });
}
</script>

<style scoped>
.report-test-container {
  padding: 20px;
}
</style>
