<template>
  <div class="app-container">
    <h2>医疗主题样式测试页面</h2>
    
    <!-- 消息提示测试 -->
    <div class="test-section">
      <h3>消息提示测试</h3>
      <el-space>
        <el-button @click="testMessage('success')" type="success">成功消息</el-button>
        <el-button @click="testMessage('warning')" type="warning">警告消息</el-button>
        <el-button @click="testMessage('error')" type="danger">错误消息</el-button>
        <el-button @click="testMessage('info')" type="info">信息消息</el-button>
      </el-space>
    </div>

    <!-- 通知测试 -->
    <div class="test-section">
      <h3>通知测试</h3>
      <el-space>
        <el-button @click="testNotification('success')" type="success">成功通知</el-button>
        <el-button @click="testNotification('warning')" type="warning">警告通知</el-button>
        <el-button @click="testNotification('error')" type="danger">错误通知</el-button>
        <el-button @click="testNotification('info')" type="info">信息通知</el-button>
      </el-space>
    </div>

    <!-- 医疗主题卡片测试 -->
    <div class="test-section">
      <h3>医疗主题卡片测试</h3>
      <el-row :gutter="20">
        <el-col :span="6">
          <MedicalCard title="普通卡片" icon="Document">
            <p>这是一个普通的医疗主题卡片</p>
          </MedicalCard>
        </el-col>
        <el-col :span="6">
          <MedicalCard title="信息卡片" icon="InfoFilled" type="info">
            <p>这是一个信息类型的医疗主题卡片</p>
          </MedicalCard>
        </el-col>
        <el-col :span="6">
          <MedicalCard title="警告卡片" icon="Warning" type="warning">
            <p>这是一个警告类型的医疗主题卡片</p>
          </MedicalCard>
        </el-col>
        <el-col :span="6">
          <MedicalCard title="危险卡片" icon="CircleCloseFilled" type="danger">
            <p>这是一个危险类型的医疗主题卡片</p>
          </MedicalCard>
        </el-col>
      </el-row>
    </div>

    <!-- 统计卡片测试 -->
    <div class="test-section">
      <h3>统计卡片测试</h3>
      <el-row :gutter="20">
        <el-col :span="6">
          <MedicalCard type="stats">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-title">待诊断</div>
            <div class="stats-value">25</div>
          </MedicalCard>
        </el-col>
        <el-col :span="6">
          <MedicalCard type="stats">
            <div class="stats-icon diagnosed">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stats-title">已诊断</div>
            <div class="stats-value">120</div>
          </MedicalCard>
        </el-col>
        <el-col :span="6">
          <MedicalCard type="stats">
            <div class="stats-icon audited">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-title">已审核</div>
            <div class="stats-value">95</div>
          </MedicalCard>
        </el-col>
        <el-col :span="6">
          <MedicalCard type="stats">
            <div class="stats-icon archived">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="stats-title">院内诊断</div>
            <div class="stats-value">8</div>
          </MedicalCard>
        </el-col>
      </el-row>
    </div>

    <!-- 医疗主题表格测试 -->
    <div class="test-section">
      <h3>医疗主题表格测试</h3>
      <MedicalCard title="患者检查记录" icon="Document">
        <MedicalTable :data="testTableData" stripe>
          <el-table-column prop="patientId" label="患者ID" width="120" />
          <el-table-column prop="patientName" label="患者姓名" width="120" />
          <el-table-column prop="examItem" label="检查项目" />
          <el-table-column prop="dicomStatus" label="DICOM状态" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.dicomStatusType" :class="scope.row.dicomStatusClass">
                {{ scope.row.dicomStatusText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="diagnosisStatus" label="诊断状态" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.diagnosisStatusType" :class="scope.row.diagnosisStatusClass">
                {{ scope.row.diagnosisStatusText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="primary" class="medical-button" size="small">
                诊断
              </el-button>
            </template>
          </el-table-column>
        </MedicalTable>
      </MedicalCard>
    </div>

    <!-- 医疗主题按钮测试 -->
    <div class="test-section">
      <h3>医疗主题按钮测试</h3>
      <el-space>
        <el-button type="primary" class="medical-button">主要按钮</el-button>
        <el-button type="success" class="medical-button">成功按钮</el-button>
        <el-button type="warning" class="medical-button">警告按钮</el-button>
        <el-button type="danger" class="medical-button">危险按钮</el-button>
        <el-button type="info" class="medical-button">信息按钮</el-button>
      </el-space>
    </div>

    <!-- 状态标签测试 -->
    <div class="test-section">
      <h3>状态标签测试</h3>
      <el-space>
        <el-tag class="medical-status-tag pending">待诊断</el-tag>
        <el-tag class="medical-status-tag diagnosed">已诊断</el-tag>
        <el-tag class="medical-status-tag audited">已审核</el-tag>
        <el-tag class="medical-status-tag archived">院内诊断</el-tag>
        <el-tag class="medical-status-tag synced">已同步</el-tag>
        <el-tag class="medical-status-tag sync-pending">待同步</el-tag>
      </el-space>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { Clock, CircleCheck, Document, FolderOpened, InfoFilled, Warning, CircleCloseFilled } from '@element-plus/icons-vue'
import { getDiagnosisStatusStyle, getDicomSyncStatusStyle } from '@/utils/medical-theme'

// 测试表格数据
const testTableData = ref([
  {
    patientId: 'P001',
    patientName: '张三',
    examItem: 'CT胸部平扫',
    dicomStatus: '1',
    dicomStatusType: 'success',
    dicomStatusClass: 'medical-status-tag synced',
    dicomStatusText: '已同步',
    diagnosisStatus: '-1',
    diagnosisStatusType: 'warning',
    diagnosisStatusClass: 'medical-status-tag pending',
    diagnosisStatusText: '待诊断'
  },
  {
    patientId: 'P002',
    patientName: '李四',
    examItem: 'MRI头部增强',
    dicomStatus: '1',
    dicomStatusType: 'success',
    dicomStatusClass: 'medical-status-tag synced',
    dicomStatusText: '已同步',
    diagnosisStatus: '1',
    diagnosisStatusType: 'success',
    diagnosisStatusClass: 'medical-status-tag diagnosed',
    diagnosisStatusText: '已诊断'
  },
  {
    patientId: 'P003',
    patientName: '王五',
    examItem: 'X线胸片',
    dicomStatus: '0',
    dicomStatusType: 'warning',
    dicomStatusClass: 'medical-status-tag sync-pending',
    dicomStatusText: '待同步',
    diagnosisStatus: '2',
    diagnosisStatusType: 'primary',
    diagnosisStatusClass: 'medical-status-tag audited',
    diagnosisStatusText: '已审核'
  }
])

// 测试消息提示
const testMessage = (type) => {
  const messages = {
    success: '操作成功！患者检查记录已保存',
    warning: '注意：该患者存在过敏史，请谨慎用药',
    error: '错误：DICOM数据同步失败，请检查网络连接',
    info: '提示：新的检查报告已生成，请及时查看'
  }
  
  ElMessage({
    type,
    message: messages[type],
    duration: 3000,
    showClose: true
  })
}

// 测试通知
const testNotification = (type) => {
  const notifications = {
    success: {
      title: '诊断完成',
      message: '患者张三的CT检查诊断已完成，请进行审核'
    },
    warning: {
      title: '系统警告',
      message: '检测到异常数据，建议立即处理'
    },
    error: {
      title: '系统错误',
      message: 'PACS服务器连接失败，请联系系统管理员'
    },
    info: {
      title: '系统通知',
      message: '系统将在今晚23:00进行维护，预计持续2小时'
    }
  }
  
  ElNotification({
    type,
    title: notifications[type].title,
    message: notifications[type].message,
    duration: 4500,
    position: 'top-right'
  })
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  color: var(--medical-primary, #0066cc);
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-bottom: 12px;
}

.stats-icon.pending {
  background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
}

.stats-icon.diagnosed {
  background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
}

.stats-icon.audited {
  background: linear-gradient(135deg, #0066cc 0%, #4d94ff 100%);
}

.stats-icon.archived {
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
}

.stats-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.stats-value {
  font-size: 28px;
  font-weight: 700;
  color: #262626;
  line-height: 1;
}
</style>
