<template>
  <div class="review-report-list">
    <!-- 列表头部 -->
    <div class="list-header">
      <div class="header-title">
        <el-icon><DocumentChecked /></el-icon>
        <span>待审核报告</span>
        <el-badge :value="total" class="item-count" />
      </div>
      <div class="header-actions">
        <el-tooltip content="刷新列表" placement="top">
          <el-button
            type="primary"
            link
            icon="Refresh"
            @click="$emit('refresh')"
            :loading="loading"
          />
        </el-tooltip>
      </div>
    </div>

    <!-- 报告列表 -->
    <div class="list-content" v-loading="loading">
      <div class="report-list">
        <div
          v-for="report in data"
          :key="report.id"
          :class="[
            'report-item',
            { 'active': currentReport?.id === report.id }
          ]"
          @click="handleReportSelect(report)"
        >
          <!-- 报告基本信息 -->
          <div class="report-header">
            <div class="patient-info">
              <span class="patient-name">{{ report.patientName }}</span>
              <span class="patient-gender">{{ formatGender(report.sex) }}</span>
              <span class="patient-age">{{ report.age }}岁</span>
            </div>
            <div class="report-status">
              <el-tag
                :type="getStatusType(report.diagnosis?.status)"
                size="small"
              >
                {{ getStatusText(report.diagnosis?.status) }}
              </el-tag>
            </div>
          </div>

          <!-- 检查信息 -->
          <div class="exam-info">
            <div class="exam-row">
              <span class="exam-label">检查号:</span>
              <span class="exam-value">{{ report.originalPatientId || report.examCode }}</span>
            </div>
            <div class="exam-row">
              <span class="exam-label">检查类型:</span>
              <span class="exam-value">{{ report.modality }}</span>
              <span class="exam-label">检查部位:</span>
              <span class="exam-value">{{ report.organ }}</span>
            </div>
            <div class="exam-row">
              <span class="exam-label">申请科室:</span>
              <span class="exam-value">{{ report.examDepartment }}</span>
            </div>
          </div>

          <!-- 诊断信息 -->
          <div class="diagnosis-info" v-if="report.diagnosis">
            <div class="diagnosis-row">
              <span class="diagnosis-label">报告医生:</span>
              <span class="diagnosis-value">{{ report.diagnosis.doctor }}</span>
            </div>
            <div class="diagnosis-row">
              <span class="diagnosis-label">报告时间:</span>
              <span class="diagnosis-value">{{ formatDateTime(report.diagnosis.createTime) }}</span>
            </div>
            <div class="diagnosis-preview" v-if="report.diagnosis.diagnose">
              <div class="preview-title">影像所见:</div>
              <div class="preview-content">{{ truncateText(report.diagnosis.diagnose, 80) }}</div>
            </div>
          </div>

          <!-- 紧急程度标识 -->
          <div class="urgency-indicator" v-if="report.isUrgent">
            <el-icon class="urgent-icon"><Warning /></el-icon>
            <span>紧急</span>
          </div>

          <!-- 操作按钮 -->
          <div class="report-actions">
            <el-button-group size="small">
              <el-button
                type="primary"
                plain
                @click.stop="handleViewImages(report)"
                :disabled="!report.studyInstanceUid"
              >
                <el-icon><Picture /></el-icon>
                影像
              </el-button>
              <el-button
                type="success"
                plain
                @click.stop="handleQuickApprove(report)"
                v-if="report.diagnosis?.status === DIAGNOSIS_STATUS.DIAGNOSED"
              >
                <el-icon><Check /></el-icon>
                通过
              </el-button>
              <el-button
                type="danger"
                plain
                @click.stop="handleQuickReject(report)"
                v-if="report.diagnosis?.status === DIAGNOSIS_STATUS.DIAGNOSED"
              >
                <el-icon><Close /></el-icon>
                驳回
              </el-button>
            </el-button-group>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && data.length === 0" class="empty-state">
        <el-empty description="暂无待审核报告" />
      </div>
    </div>

    <!-- 分页 -->
    <div class="list-footer" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        layout="prev, pager, next, jumper, total"
        @current-change="handlePageChange"
        small
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DocumentChecked,
  Warning,
  Picture,
  Check,
  Close,
  Refresh
} from '@element-plus/icons-vue'
import {
  DIAGNOSIS_STATUS,
  getDiagnosisStatusText,
  getDiagnosisStatusTagType
} from '@/constants/diagnosisStatus'

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Array,
    default: () => []
  },
  total: {
    type: Number,
    default: 0
  },
  currentReport: {
    type: Object,
    default: null
  },
  pageSize: {
    type: Number,
    default: 20
  }
})

// Emits
const emit = defineEmits([
  'report-select',
  'page-change',
  'view-images',
  'quick-approve',
  'quick-reject',
  'refresh'
])

// 响应式数据
const currentPage = ref(1)

// 计算属性
const pendingCount = computed(() => {
  return props.data.filter(report =>
    report.diagnosis?.status === '1'
  ).length
})

// 处理报告选择
const handleReportSelect = (report) => {
  emit('report-select', report)
}

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page
  emit('page-change', page)
}

// 处理查看影像
const handleViewImages = (report) => {
  if (!report.studyInstanceUid) {
    ElMessage.warning('该检查没有影像数据')
    return
  }
  emit('view-images', report.studyInstanceUid)
}

// 处理快速通过
const handleQuickApprove = (report) => {
  ElMessageBox.confirm(
    `确认通过患者 ${report.patientName} 的检查报告吗？`,
    '快速审核',
    {
      confirmButtonText: '确认通过',
      cancelButtonText: '取消',
      type: 'success'
    }
  ).then(() => {
    emit('quick-approve', report)
  }).catch(() => {
    // 用户取消
  })
}

// 处理快速驳回
const handleQuickReject = (report) => {
  ElMessageBox.prompt(
    `请输入驳回 ${report.patientName} 检查报告的原因：`,
    '快速驳回',
    {
      confirmButtonText: '确认驳回',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '请输入驳回原因...',
      inputValidator: (value) => {
        if (!value || !value.trim()) {
          return '请输入驳回原因'
        }
        return true
      }
    }
  ).then(({ value }) => {
    emit('quick-reject', report, value)
  }).catch(() => {
    // 用户取消
  })
}

// 获取状态类型
const getStatusType = (status) => {
  return getDiagnosisStatusTagType(status)
}

// 获取状态文本
const getStatusText = (status) => {
  return getDiagnosisStatusText(status)
}

// 格式化性别
const formatGender = (sex) => {
  const genderMap = {
    'M': '男',
    'F': '女',
    '1': '男',
    '0': '女'
  }
  return genderMap[sex] || '未知'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  try {
    const date = new Date(dateTime)
    if (isNaN(date.getTime())) return ''

    const now = new Date()
    const diffMs = now - date
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffDays === 0) {
      if (diffHours === 0) {
        const diffMinutes = Math.floor(diffMs / (1000 * 60))
        return diffMinutes <= 0 ? '刚刚' : `${diffMinutes}分钟前`
      }
      return `${diffHours}小时前`
    } else if (diffDays === 1) {
      return '昨天'
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  } catch (error) {
    return ''
  }
}

// 截断文本
const truncateText = (text, maxLength) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}
</script>

<style scoped>
.review-report-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.item-count {
  :deep(.el-badge__content) {
    background-color: #409eff;
  }
}

.list-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.report-item {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.report-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.report-item.active {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.patient-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.patient-gender,
.patient-age {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.exam-info {
  margin-bottom: 12px;
}

.exam-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
}

.exam-label {
  color: #909399;
  min-width: 60px;
}

.exam-value {
  color: #606266;
  font-weight: 500;
}

.diagnosis-info {
  margin-bottom: 12px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.diagnosis-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
}

.diagnosis-label {
  color: #909399;
  min-width: 60px;
}

.diagnosis-value {
  color: #606266;
}

.diagnosis-preview {
  margin-top: 8px;
}

.preview-title {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.preview-content {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.urgency-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  color: #e6a23c;
  font-size: 12px;
  background: #fdf6ec;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #f5dab1;
}

.urgent-icon {
  font-size: 14px;
}

.report-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.list-footer {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  background: #fafafa;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 滚动条样式 */
.list-content::-webkit-scrollbar {
  width: 6px;
}

.list-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.list-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.list-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
