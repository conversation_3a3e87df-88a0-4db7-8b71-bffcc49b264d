<template>
  <div class="report-review-detail">
    <!-- 详情头部 -->
    <div class="detail-header">
      <div class="header-left">
        <h3 class="report-title">
          <el-icon><DocumentChecked /></el-icon>
          报告审核详情
        </h3>
        <div class="patient-summary">
          <span class="patient-name">{{ reportData.patientName }}</span>
          <span class="patient-info">{{ formatGender(reportData.sex) }} {{ reportData.age }}岁</span>
          <el-tag :type="getStatusType(diagnosisData?.status)" size="small">
            {{ getStatusText(diagnosisData?.status) }}
          </el-tag>
        </div>
      </div>
      <div class="header-actions">
        <el-button
          type="primary"
          icon="Picture"
          @click="handleViewImages"
          :disabled="!reportData.studyInstanceUid"
        >
          查看影像
        </el-button>
        <el-button
          type="info"
          icon="Printer"
          @click="handlePrintReport"
        >
          打印报告
        </el-button>
      </div>
    </div>

    <!-- 详情内容 -->
    <div class="detail-content">
      <el-scrollbar height="100%">
        <!-- 基本信息卡片 -->
        <el-card class="info-card" header="基本信息" shadow="never">
          <div class="info-grid">
            <div class="info-row">
              <div class="info-item">
                <span class="info-label">患者ID:</span>
                <span class="info-value">{{ reportData.patientId }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">检查号:</span>
                <span class="info-value">{{ reportData.originalPatientId || reportData.examCode }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">检查类型:</span>
                <span class="info-value">{{ reportData.modality }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="info-label">检查部位:</span>
                <span class="info-value">{{ reportData.organ }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">申请科室:</span>
                <span class="info-value">{{ reportData.examDepartment }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">申请医生:</span>
                <span class="info-value">{{ reportData.examDoctorName }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="info-label">检查时间:</span>
                <span class="info-value">{{ formatDateTime(reportData.studyDate) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">医院名称:</span>
                <span class="info-value">{{ reportData.hospitalName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">检查描述:</span>
                <span class="info-value">{{ reportData.studyDescription }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 诊断报告卡片 -->
        <el-card class="report-card" header="诊断报告" shadow="never" v-if="diagnosisData">
          <div class="report-content">
            <!-- 影像所见 -->
            <div class="report-section">
              <div class="section-header">
                <el-icon class="section-icon"><View /></el-icon>
                <span class="section-title">影像所见</span>
              </div>
              <div class="section-content">
                <div class="text-content" v-if="diagnosisData.diagnose">
                  {{ diagnosisData.diagnose }}
                </div>
                <div class="empty-content" v-else>
                  <el-text type="info">暂无影像所见内容</el-text>
                </div>
              </div>
            </div>

            <!-- 影像意见 -->
            <div class="report-section">
              <div class="section-header">
                <el-icon class="section-icon"><EditPen /></el-icon>
                <span class="section-title">影像意见</span>
              </div>
              <div class="section-content">
                <div class="text-content" v-if="diagnosisData.recommendation">
                  {{ diagnosisData.recommendation }}
                </div>
                <div class="empty-content" v-else>
                  <el-text type="info">暂无影像意见内容</el-text>
                </div>
              </div>
            </div>

            <!-- 阴阳性结果 -->
            <div class="report-section" v-if="diagnosisData.positiveNegative">
              <div class="section-header">
                <el-icon class="section-icon"><Select /></el-icon>
                <span class="section-title">阴阳性结果</span>
              </div>
              <div class="section-content">
                <el-tag
                  :type="diagnosisData.positiveNegative === 'positive' ? 'danger' : 'success'"
                  size="large"
                >
                  {{ diagnosisData.positiveNegative === 'positive' ? '阳性' : '阴性' }}
                </el-tag>
              </div>
            </div>

            <!-- 医生信息 -->
            <div class="doctor-info">
              <div class="doctor-item">
                <span class="doctor-label">报告医生:</span>
                <span class="doctor-name">{{ diagnosisData.doctor }}</span>
                <span class="doctor-time" v-if="diagnosisData.createTime">
                  {{ formatDateTime(diagnosisData.createTime) }}
                </span>
              </div>
              <div class="doctor-item" v-if="diagnosisData.auditBy">
                <span class="doctor-label">审核医生:</span>
                <span class="doctor-name">{{ diagnosisData.auditBy }}</span>
                <span class="doctor-time" v-if="diagnosisData.auditTime">
                  {{ formatDateTime(diagnosisData.auditTime) }}
                </span>
              </div>
              <div class="doctor-item" v-if="diagnosisData.auditComment">
                <span class="doctor-label">审核意见:</span>
                <span class="audit-comment">{{ diagnosisData.auditComment }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 历史审核记录 -->
        <el-card class="history-card" header="审核历史" shadow="never" v-if="auditHistory.length > 0">
          <div class="history-list">
            <div
              v-for="(history, index) in auditHistory"
              :key="index"
              class="history-item"
            >
              <div class="history-header">
                <el-tag
                  :type="history.action === 'approve' ? 'success' : 'danger'"
                  size="small"
                >
                  {{ history.action === 'approve' ? '审核通过' : '审核驳回' }}
                </el-tag>
                <span class="history-time">{{ formatDateTime(history.time) }}</span>
              </div>
              <div class="history-content">
                <div class="history-operator">操作人: {{ history.operator }}</div>
                <div class="history-comment" v-if="history.comment">
                  审核意见: {{ history.comment }}
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-scrollbar>
    </div>

    <!-- 审核操作区域 -->
    <div class="audit-actions" v-if="canAudit">
      <div class="action-buttons">
        <el-button
          type="danger"
          size="large"
          @click="handleReject"
          :loading="rejectLoading"
        >
          <el-icon><Close /></el-icon>
          驳回报告
        </el-button>
        <el-button
          type="success"
          size="large"
          @click="handleApprove"
          :loading="approveLoading"
        >
          <el-icon><Check /></el-icon>
          审核通过
        </el-button>
      </div>
    </div>

    <!-- 已审核状态提示 -->
    <div class="audit-status" v-else-if="diagnosisData?.status === '2'">
      <el-alert
        title="该报告已完成审核"
        type="success"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="audit-info">
            <div>审核医生: {{ diagnosisData.auditBy }}</div>
            <div>审核时间: {{ formatDateTime(diagnosisData.auditTime) }}</div>
            <div v-if="diagnosisData.auditComment">审核意见: {{ diagnosisData.auditComment }}</div>
          </div>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  DocumentChecked,
  View,
  EditPen,
  Select,
  Close,
  Check,
  Picture,
  Printer
} from '@element-plus/icons-vue'
import {
  DIAGNOSIS_STATUS,
  getDiagnosisStatusText,
  getDiagnosisStatusTagType
} from '@/constants/diagnosisStatus'
import useUserStore from '@/store/modules/user'

// Props
const props = defineProps({
  reportData: {
    type: Object,
    required: true
  },
  diagnosisData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits([
  'audit-approve',
  'audit-reject',
  'view-images'
])

// Store
const userStore = useUserStore()

// 响应式数据
const approveLoading = ref(false)
const rejectLoading = ref(false)
const auditHistory = ref([]) // 审核历史记录

// 计算属性
const canAudit = computed(() => {
  return props.diagnosisData?.status === DIAGNOSIS_STATUS.DIAGNOSED &&
         props.diagnosisData?.diagnose?.trim() &&
         props.diagnosisData?.doctor !== userStore.name // 不能审核自己的报告
})

// 处理审核通过
const handleApprove = () => {
  emit('audit-approve')
}

// 处理审核驳回
const handleReject = () => {
  emit('audit-reject')
}

// 处理查看影像
const handleViewImages = () => {
  if (!props.reportData.studyInstanceUid) {
    ElMessage.warning('该检查没有影像数据')
    return
  }
  emit('view-images', props.reportData.studyInstanceUid)
}

// 处理打印报告
const handlePrintReport = () => {
  // 构建打印页面URL
  const printUrl = `/diagnosis/print/${props.reportData.id}`
  window.open(printUrl, '_blank')
}

// 获取状态类型
const getStatusType = (status) => {
  return getDiagnosisStatusTagType(status)
}

// 获取状态文本
const getStatusText = (status) => {
  return getDiagnosisStatusText(status)
}

// 格式化性别
const formatGender = (sex) => {
  const genderMap = {
    'M': '男',
    'F': '女',
    '1': '男',
    '0': '女'
  }
  return genderMap[sex] || '未知'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  try {
    const date = new Date(dateTime)
    if (isNaN(date.getTime())) return ''
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return ''
  }
}

// 加载审核历史
const loadAuditHistory = () => {
  // 这里可以调用API获取审核历史
  // 暂时使用模拟数据
  auditHistory.value = []
}
</script>

<style scoped>
.report-review-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.header-left {
  flex: 1;
}

.report-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.patient-summary {
  display: flex;
  align-items: center;
  gap: 12px;
}

.patient-name {
  font-size: 16px;
  font-weight: 500;
  color: #409eff;
}

.patient-info {
  font-size: 14px;
  color: #909399;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.detail-content {
  flex: 1;
  overflow: hidden;
  padding: 16px;
}

.info-card,
.report-card,
.history-card {
  margin-bottom: 16px;
}

.info-card :deep(.el-card__header),
.report-card :deep(.el-card__header),
.history-card :deep(.el-card__header) {
  background: #f8f9fa;
  font-weight: 600;
  color: #303133;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  gap: 24px;
}

.info-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  font-size: 14px;
  color: #909399;
  min-width: 80px;
}

.info-value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.report-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.section-icon {
  color: #409eff;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.section-content {
  padding-left: 24px;
}

.text-content {
  font-size: 14px;
  line-height: 1.6;
  color: #303133;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  white-space: pre-wrap;
}

.empty-content {
  padding: 16px;
  text-align: center;
}

.doctor-info {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.doctor-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 14px;
}

.doctor-label {
  color: #909399;
  min-width: 80px;
}

.doctor-name {
  color: #303133;
  font-weight: 500;
}

.doctor-time {
  color: #909399;
  font-size: 12px;
}

.audit-comment {
  color: #e6a23c;
  font-style: italic;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.history-time {
  font-size: 12px;
  color: #909399;
}

.history-content {
  font-size: 14px;
}

.history-operator {
  color: #303133;
  margin-bottom: 4px;
}

.history-comment {
  color: #606266;
  font-style: italic;
}

.audit-actions {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
  background: #fafafa;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.action-buttons .el-button {
  min-width: 140px;
}

.audit-status {
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

.audit-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
  color: #303133;
}
</style>
