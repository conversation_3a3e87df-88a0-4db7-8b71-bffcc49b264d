<template>
  <div class="review-search-bar">
    <el-form
      ref="searchFormRef"
      :model="formData"
      inline
      label-width="80px"
      class="search-form"
    >
      <el-row :gutter="16">
        <el-col :span="6">
          <el-form-item label="患者姓名" prop="patientName">
            <el-input
              v-model="formData.patientName"
              placeholder="请输入患者姓名"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="检查号" prop="examCode">
            <el-input
              v-model="formData.examCode"
              placeholder="请输入检查号"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="检查类型" prop="modality">
            <el-select
              v-model="formData.modality"
              placeholder="请选择检查类型"
              clearable
            >
              <el-option label="CT" value="CT" />
              <el-option label="MRI" value="MRI" />
              <el-option label="DR" value="DR" />
              <el-option label="US" value="US" />
              <el-option label="CR" value="CR" />
              <el-option label="DX" value="DX" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="审核状态" prop="diagnosisStatus">
            <el-select
              v-model="formData.diagnosisStatus"
              placeholder="请选择审核状态"
              clearable
            >
              <el-option
                v-for="status in diagnosisStatusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="6">
          <el-form-item label="申请科室" prop="examDepartment">
            <el-input
              v-model="formData.examDepartment"
              placeholder="请输入申请科室"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="申请医生" prop="examDoctorName">
            <el-input
              v-model="formData.examDoctorName"
              placeholder="请输入申请医生"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="检查部位" prop="organ">
            <el-input
              v-model="formData.organ"
              placeholder="请输入检查部位"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="医院名称" prop="hospitalName">
            <el-input
              v-model="formData.hospitalName"
              placeholder="请输入医院名称"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="检查时间" prop="examTime">
            <el-date-picker
              v-model="examTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              @change="handleTimeRangeChange"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <div class="search-buttons">
            <el-button type="primary" icon="Search" @click="handleSearch">
              查询
            </el-button>
            <el-button icon="Refresh" @click="handleReset">
              重置
            </el-button>
            <el-button type="success" icon="Download" @click="handleExport">
              导出
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <!-- 统计信息 -->
    <div class="statistics-bar">
      <div class="stat-item">
        <span class="stat-label">待审核:</span>
        <span class="stat-value pending">{{ statistics.pending }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已驳回:</span>
        <span class="stat-value rejected">{{ statistics.rejected }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已审核:</span>
        <span class="stat-value approved">{{ statistics.approved }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">今日审核:</span>
        <span class="stat-value today">{{ statistics.todayAudited }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  DIAGNOSIS_STATUS,
  getDiagnosisStatusOptions
} from '@/constants/diagnosisStatus'

// Props
const props = defineProps({
  queryParams: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits([
  'update:queryParams',
  'search',
  'reset'
])

// 响应式数据
const searchFormRef = ref()
const examTimeRange = ref([])

// 表单数据
const formData = reactive({
  patientName: '',
  examCode: '',
  modality: '',
  diagnosisStatus: '1', // 默认查询待审核
  examDepartment: '',
  examDoctorName: '',
  organ: '',
  hospitalName: '',
  startTime: '',
  endTime: ''
})

// 统计数据
const statistics = reactive({
  pending: 0,
  rejected: 0,
  approved: 0,
  todayAudited: 0
})

// 审核状态选项
const diagnosisStatusOptions = getDiagnosisStatusOptions()

// 监听props变化，同步到formData
watch(() => props.queryParams, (newVal) => {
  Object.assign(formData, newVal)

  // 处理时间范围
  if (newVal.startTime && newVal.endTime) {
    examTimeRange.value = [newVal.startTime, newVal.endTime]
  } else {
    examTimeRange.value = []
  }
}, { immediate: true, deep: true })

// 监听formData变化，触发更新
watch(formData, (newVal) => {
  emit('update:queryParams', { ...newVal })
}, { deep: true })

// 处理时间范围变化
const handleTimeRangeChange = (value) => {
  if (value && value.length === 2) {
    formData.startTime = value[0]
    formData.endTime = value[1]
  } else {
    formData.startTime = ''
    formData.endTime = ''
  }
}

// 处理搜索
const handleSearch = () => {
  emit('search')
}

// 处理重置
const handleReset = () => {
  searchFormRef.value?.resetFields()
  examTimeRange.value = []

  // 重置表单数据，但保留默认的审核状态
  Object.keys(formData).forEach(key => {
    if (key === 'diagnosisStatus') {
      formData[key] = '1'
    } else {
      formData[key] = ''
    }
  })

  emit('reset')
}

// 处理导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 更新统计数据
const updateStatistics = (data) => {
  statistics.pending = data.pending || 0
  statistics.rejected = data.rejected || 0
  statistics.approved = data.approved || 0
  statistics.todayAudited = data.todayAudited || 0
}

// 暴露方法
defineExpose({
  updateStatistics
})
</script>

<style scoped>
.review-search-bar {
  padding: 16px;
}

.search-form {
  margin-bottom: 16px;
}

.search-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.statistics-bar {
  display: flex;
  gap: 24px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
}

.stat-value.pending {
  color: #e6a23c;
}

.stat-value.rejected {
  color: #f56c6c;
}

.stat-value.approved {
  color: #67c23a;
}

.stat-value.today {
  color: #409eff;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}
</style>
