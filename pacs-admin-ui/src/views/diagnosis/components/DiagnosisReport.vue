<template>
  <div class="diagnosis-report">
    <div class="report-container">
      <!-- 报告编辑区域 -->
      <div class="report-editor">
        <el-form
            ref="diagnosisFormRef"
            :model="diagnosisForm"
            :rules="rules"
            label-width="0"
            class="diagnosis-form"
        >
          <!-- 影像所见 -->
          <div class="report-section">
            <div class="section-header">
              <div class="section-title">
                <el-icon class="title-icon">
                  <View/>
                </el-icon>
                <span>影像所见</span>
              </div>
              <div class="section-actions">
                <el-button
                    v-if="hasEditPermission"
                    type="primary"
                    link
                    size="small"
                    @click="clearFindings"
                    :disabled="!canEdit"
                >
                  清空
                </el-button>
              </div>
            </div>
            <el-form-item prop="diagnose" class="form-item">
              <el-input
                  v-model="diagnosisForm.diagnose"
                  type="textarea"
                  :rows="6"
                  placeholder="请输入影像所见内容..."
                  :disabled="!canEdit"
                  @input="handleContentChange"
                  class="report-textarea"
              />
            </el-form-item>
          </div>

          <!-- 影像意见 -->
          <div class="report-section">
            <div class="section-header">
              <div class="section-title">
                <el-icon class="title-icon">
                  <EditPen/>
                </el-icon>
                <span>影像意见</span>
              </div>
              <div class="section-actions">
                <el-button
                    v-if="hasEditPermission"
                    type="primary"
                    link
                    size="small"
                    @click="clearOpinion"
                    :disabled="!canEdit"
                >
                  清空
                </el-button>
              </div>
            </div>
            <el-form-item prop="recommendation" class="form-item">
              <el-input
                  v-model="diagnosisForm.recommendation"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入影像意见..."
                  :disabled="!canEdit"
                  @input="handleContentChange"
                  class="report-textarea"
              />
            </el-form-item>
          </div>

          <!-- 阴阳性结果 -->
          <div class="report-section">
            <div class="section-header">
              <div class="section-title">
                <el-icon class="title-icon"><Select/></el-icon>
                <span>阴阳性结果</span>
              </div>
            </div>
            <el-form-item prop="positiveNegative" class="form-item">
              <el-radio-group
                  v-model="diagnosisForm.positiveNegative"
                  :disabled="!canEdit"
                  @change="handleContentChange"
                  class="result-radio-group"
              >
                <el-radio value="positive" class="result-radio positive">
                  <span>阳性</span>
                </el-radio>
                <el-radio value="negative" class="result-radio negative">
                  <span>阴性</span>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- 诊断医生信息 -->
          <div class="report-section">
            <div class="section-header">
              <div class="section-title">
                <el-icon class="title-icon">
                  <User/>
                </el-icon>
                <span>诊断医生</span>
              </div>
            </div>
            <div class="doctor-info">
              <div class="doctor-item">
                <label class="doctor-label">报告医生：</label>
                <span class="doctor-name">{{ diagnosisForm.doctor || userStore.name }}</span>
                <span class="doctor-time" v-if="diagnosisForm.createTime">
                  {{ formatDateTime(diagnosisForm.createTime) }}
                </span>
              </div>
              <div class="doctor-item" v-if="diagnosisForm.auditBy">
                <label class="doctor-label">审核医生：</label>
                <span class="doctor-name">{{ diagnosisForm.auditBy }}</span>
                <span class="doctor-time" v-if="diagnosisForm.auditTime">
                  {{ formatDateTime(diagnosisForm.auditTime) }}
                </span>
              </div>
            </div>
          </div>
        </el-form>
      </div>

      <!-- 自动保存状态 -->
      <div class="auto-save-status" v-if="autoSaveStatus">
        <el-tag size="small" :type="autoSaveType">
          {{ autoSaveStatus }}
        </el-tag>
      </div>
    </div>

    <!-- 底部操作按钮区域 -->
    <div class="action-buttons">
      <div class="button-group">
        <el-button
            v-if="canEdit && (hasAddPermission || hasEditPermission)"
            @click="handleSave"
            :loading="saveLoading"
            icon="Document"
        >
          暂存
        </el-button>

        <el-button
            v-if="canEdit && (hasAddPermission || hasEditPermission)"
            type="primary"
            @click="handleSubmit"
            :loading="submitLoading"
            icon="Upload"
        >
          提交
        </el-button>

        <el-button
            v-if="canAudit && hasAuditPermission"
            type="warning"
            @click="handleAudit"
            :loading="auditLoading"
            icon="CircleCheck"
        >
          审核
        </el-button>

        <el-button
            v-if="canUnaudit && hasAuditPermission"
            type="warning"
            @click="handleUnaudit"
            :loading="unauditLoading"
            icon="CircleClose"
        >
          反审核
        </el-button>
      </div>

      <div class="button-group">
        <el-button
            v-if="hasQueryPermission"
            @click="handlePreview"
            icon="View"
        >
          报告预览
        </el-button>

        <!--        <el-button
                  v-if="hasQueryPermission"
                  @click="handlePrint"
                  icon="Printer"
                >
                  打印
                </el-button>-->

        <el-button
            v-if="hasQueryPermission"
            @click="handleViewImages"
            :disabled="!hasImages"
            :loading="isLoadingStudyUid"
            icon="Picture"
        >
          查看影像
        </el-button>

        <el-button
            v-if="hasQueryPermission"
            @click="handleViewRecords"
            icon="Document"
        >
        </el-button>

        <!-- 多个检查选择器 -->
        <el-dropdown
            v-if="allStudyInstanceUids.length > 1"
            @command="switchStudy"
            trigger="click"
            class="ml-2"
        >
          <el-button size="small" type="primary" plain>
            选择检查
            <el-icon class="el-icon--right">
              <arrow-down/>
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                  v-for="(study, index) in allStudyInstanceUids"
                  :key="study.studyInstanceUid"
                  :command="study.studyInstanceUid"
                  :class="{ 'is-active': study.studyInstanceUid === studyInstanceUid }"
              >
                <div class="study-item">
                  <div class="study-title">
                    {{ study.studyDescription || `检查 ${index + 1}` }}
                  </div>
                  <div class="study-info">
                    <span v-if="study.studyDate">{{ formatStudyDate(study.studyDate) }}</span>
                    <span v-if="study.modality" class="ml-2">{{ study.modality }}</span>
                    <span v-if="study.accessionNumber" class="ml-2">{{ study.accessionNumber }}</span>
                  </div>
                </div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!--        <el-button
                  v-if="canEdit"
                  @click="handleSaveAsTemplate"
                  type="info"
                  icon="Document"
                >
                  存为模板
                </el-button>-->

        <el-button
            v-if="canRequestConsultation && hasEditPermission"
            @click="handleRequestConsultation"
            type="warning"
            plain
            :icon="ChatLineRound"
            :loading="consultationLoading"
        >
          申请协助
        </el-button>
      </div>
    </div>

    <!-- 报告预览组件 -->
    <ReportViewer ref="reportRef"/>

    <!-- 检查选择对话框 -->
    <el-dialog
        v-model="studySelectionVisible"
        title="选择要查看的检查"
        width="800px"
        :close-on-click-modal="false"
        append-to-body
    >
      <div class="study-selection-content">
        <p class="selection-tip">该患者有多个检查，请选择要查看的检查：</p>

        <el-table
            :data="allStudyInstanceUids"
            @current-change="handleStudySelectionChange"
            highlight-current-row
            style="width: 100%"
            ref="studyTableRef"
        >
          <el-table-column label="序号" width="60" align="center">
            <template #default="{ $index }">
              {{ $index + 1 }}
            </template>
          </el-table-column>

          <el-table-column label="检查描述" min-width="150">
            <template #default="{ row }">
              {{ row.studyDescription || '未知检查' }}
            </template>
          </el-table-column>

          <el-table-column label="检查日期" width="120">
            <template #default="{ row }">
              {{ formatStudyDate(row.studyDate) }}
            </template>
          </el-table-column>

          <el-table-column label="检查时间" width="100">
            <template #default="{ row }">
              {{ formatStudyTime(row.studyTime) }}
            </template>
          </el-table-column>

          <el-table-column label="检查类型" width="80">
            <template #default="{ row }">
              {{ row.modality || '-' }}
            </template>
          </el-table-column>

          <el-table-column label="检查号" width="120">
            <template #default="{ row }">
              {{ row.accessionNumber || '-' }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100" align="center">
            <template #default="{ row }">
              <el-button
                  type="primary"
                  size="small"
                  @click="selectStudyAndView(row.studyInstanceUid)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="studySelectionVisible = false">取消</el-button>
          <el-button
              type="primary"
              @click="confirmSelectedStudy"
              :disabled="!selectedStudyForViewing"
          >
            确定查看
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 申请会诊对话框 -->
    <el-dialog
        v-model="consultationDialogVisible"
        title="申请会诊"
        width="600px"
        :close-on-click-modal="false"
        append-to-body
    >
      <el-form
          ref="consultationFormRef"
          :model="consultationForm"
          :rules="consultationRules"
          label-width="100px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="患者信息">
              <div class="patient-info-display">
                <span><strong>姓名：</strong>{{ props.patientData?.patientName || '未知' }}</span>
                <span><strong>性别：</strong>{{ props.patientData?.patientSex || '未知' }}</span>
                <span><strong>年龄：</strong>{{ calculatePatientAge() }}岁</span>
              </div>
              <div class="patient-info-display mt-2">
                <span><strong>检查类型：</strong>{{ props.patientData?.modality || '未知' }}</span>
                <span><strong>检查部位：</strong>{{
                    props.patientData?.bodyPartExamined || props.patientData?.studyDescription || '未知'
                  }}</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="会诊专家" prop="consultantId">
              <el-select
                  v-model="consultationForm.consultantId"
                  placeholder="请选择会诊专家"
                  style="width: 100%"
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="searchConsultants"
                  :loading="consultantLoading"
                  @focus="loadAllConsultants"
                  clearable
              >
                <el-option
                    v-for="consultant in consultantOptions"
                    :key="consultant.userId"
                    :label="`${consultant.nickName} (${consultant.dept?.deptName || ''})`"
                    :value="consultant.userId"
                >
                  <div class="consultant-option">
                    <div class="consultant-info">
                      <span class="consultant-name">{{ consultant.nickName }}</span>
                      <span class="consultant-dept">- {{ consultant.dept?.deptName || '未分配部门' }}</span>
                    </div>
                    <div class="consultant-meta">
                      <el-tag v-if="consultant.phonenumber" size="small" type="info">
                        {{ consultant.phonenumber }}
                      </el-tag>
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="紧急程度" prop="urgency">
              <el-radio-group v-model="consultationForm.urgency">
                <el-radio label="URGENT">紧急</el-radio>
                <el-radio label="NORMAL">普通</el-radio>
                <el-radio label="LOW">低优先级</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="会诊原因" prop="reason">
              <el-input
                  v-model="consultationForm.reason"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入会诊原因和临床疑问..."
                  maxlength="500"
                  show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="临床描述">
              <el-input
                  v-model="consultationForm.clinicalDescription"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入相关临床信息（可选）..."
                  maxlength="300"
                  show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="consultationDialogVisible = false">取 消</el-button>
          <el-button
              type="primary"
              @click="submitConsultationRequest"
              :loading="submitConsultationLoading"
          >
            提 交
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 全屏影像阅读器弹窗 -->
    <el-dialog
        v-model="fullscreenViewerVisible"
        title="影像阅读器"
        fullscreen
        :destroy-on-close="false"
        :before-close="handleCloseViewer"
        :show-close="true"
    >
      <div class="fullscreen-viewer-container">
        <div class="fullscreen-viewer-content">
          <i-frame v-if="fullscreenViewerUrl" :src="fullscreenViewerUrl"></i-frame>
        </div>
      </div>
    </el-dialog>

    <!-- 诊断记录查看器 -->
    <DiagnosisRecordViewer
        ref="diagnosisRecordViewerRef"
        :diagnosis-id="diagnosisForm.id"
        @close="handleRecordViewerClose"
    />
  </div>
</template>

<script setup>
import {ref, reactive, computed, watch, onMounted, onBeforeUnmount, nextTick} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {
  View,
  EditPen,
  Select,
  CircleCheck,
  CircleClose,
  User,
  Document,
  Upload,
  Printer,
  Picture,
  ChatLineRound
} from '@element-plus/icons-vue'
import useUserStore from '@/store/modules/user'
import {addDiagnosis, updateDiagnosis, formatReportData as apiFormatReportData} from '@/api/diagnosis/diagnosis'
import {getApplicableTemplate} from '@/api/diagnosis/reportTemplate'
import {listUser} from '@/api/system/user'
import {createConsultationRequest} from '@/api/consultation/request'
import {getStudyInstanceUid} from '@/api/pacs/study'
import {getConfigKey} from '@/api/system/config'
import ReportViewer from '@/components/Report/ReportViewer.vue'
import iFrame from '@/components/iFrame/index.vue'
import DiagnosisRecordViewer from './DiagnosisRecordViewer.vue'
import {checkPermi} from '@/utils/permission'

// Props
const props = defineProps({
  patientData: {
    type: Object,
    required: true
  },
  diagnosisData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits([
  'diagnosis-save',
  'diagnosis-submit',
  'diagnosis-audit',
  'template-apply'
])

// 响应式数据
const diagnosisFormRef = ref()
const autoSaveTimer = ref(null)
const autoSaveStatus = ref('')
const autoSaveType = ref('info')
const lastSavedContent = ref('')

// 按钮loading状态
const saveLoading = ref(false)
const submitLoading = ref(false)
const auditLoading = ref(false)
const unauditLoading = ref(false)

// 报告和查看器相关
const reportRef = ref()
const fullscreenViewerVisible = ref(false)
const diagnosisRecordViewerRef = ref()

// Store
const userStore = useUserStore()

// 诊断表单
const diagnosisForm = reactive({
  id: undefined,
  studyId: undefined,
  checkId: undefined,
  diagnose: '',
  recommendation: '',
  positiveNegative: '',
  status: '0',
  doctor: userStore.name || '',
  auditBy: undefined,
  auditTime: undefined,
  createTime: undefined,
  updateTime: undefined
})

// 表单验证规则
const rules = {
  diagnose: [
    {required: true, message: '请输入影像所见', trigger: 'blur'}
  ]
}

// 计算属性
const isAudited = computed(() => {
  return diagnosisForm.status === '2'
})

const isCreator = computed(() => {
  if (!diagnosisForm.id) return true

  if (diagnosisForm.createBy === 'admin') return true
  if (diagnosisForm.createBy === userStore.id || diagnosisForm.createBy === userStore.name) return true
  if (diagnosisForm.doctor === userStore.name) return true

  return false
})

const canEdit = computed(() => {
  return !isAudited.value && isCreator.value && checkPermi(['diagnosis:diagnosis:edit'])
})

const canAudit = computed(() => {
  return !isAudited.value && isCreator.value && diagnosisForm.diagnose?.trim() && checkPermi(['diagnosis:diagnosis:audit'])
})

const canUnaudit = computed(() => {
  if (!isAudited.value) return false
  return (diagnosisForm.auditBy === userStore.name || userStore.roles?.includes('admin')) && checkPermi(['diagnosis:diagnosis:audit'])
})

// 权限检查计算属性
const hasAddPermission = computed(() => {
  return checkPermi(['diagnosis:diagnosis:add'])
})

const hasEditPermission = computed(() => {
  return checkPermi(['diagnosis:diagnosis:edit'])
})

const hasQueryPermission = computed(() => {
  return checkPermi(['diagnosis:diagnosis:query'])
})

const hasAuditPermission = computed(() => {
  return checkPermi(['diagnosis:diagnosis:audit'])
})

const hasRemovePermission = computed(() => {
  return checkPermi(['diagnosis:diagnosis:remove'])
})

const hasImages = computed(() => {
  // 如果明确标记为没有影像，返回false
  if (props.patientData?.hasImages === false) {
    return false
  }

  // 如果有studyInstanceUid或者患者数据中有相关字段，认为有影像
  return !!(studyInstanceUid.value || props.patientData?.studyInstanceUid || props.patientData?.hasImages !== false)
})

// 影像数据相关
const studyInstanceUid = ref('')
const allStudyInstanceUids = ref([])
const isLoadingStudyUid = ref(false)
const studySelectionVisible = ref(false)
const selectedStudyForViewing = ref('')

// 全屏查看器URL
const fullscreenViewerUrl = computed(() => {
  if (!props.patientData?.id) return ''

  // 优先使用获取到的studyInstanceUid，如果没有则使用患者数据中的字段
  const studyUid = studyInstanceUid.value || props.patientData.studyInstanceUid

  // 如果有多个StudyInstanceUID，构建包含所有UID的URL
  const options = {isFullscreen: true}
  if (allStudyInstanceUids.value.length > 1) {
    options.allStudyUids = allStudyInstanceUids.value.map(study => study.studyInstanceUid)
  }

  return buildViewerUrl(studyUid, options)
})

// 监听诊断数据变化
watch(() => props.diagnosisData, (newVal) => {
  if (newVal) {
    Object.assign(diagnosisForm, {
      id: newVal.id,
      studyId: newVal.studyId || props.patientData.id,
      checkId: newVal.checkId || props.patientData.id,
      diagnose: newVal.diagnose || '',
      recommendation: newVal.recommendation || '',
      positiveNegative: newVal.positiveNegative || '',
      status: newVal.status || '0',
      doctor: newVal.doctor || userStore.name,
      auditBy: newVal.auditBy,
      auditTime: newVal.auditTime,
      createTime: newVal.createTime,
      updateTime: newVal.updateTime
    })

    // 更新最后保存的内容
    lastSavedContent.value = JSON.stringify(diagnosisForm)
  }
}, {immediate: true, deep: true})

// 监听患者数据变化
watch(() => props.patientData, (newVal) => {
  if (newVal && !diagnosisForm.id) {
    diagnosisForm.studyId = newVal.originPatientId
    diagnosisForm.checkId = newVal.id
  }
}, {immediate: true})

// 处理内容变化
const handleContentChange = () => {
  if (!canEdit.value) return
  triggerAutoSave()
}

// 触发自动保存
const triggerAutoSave = () => {
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value)
  }

  autoSaveStatus.value = '编辑中...'
  autoSaveType.value = 'warning'

  autoSaveTimer.value = setTimeout(async () => {
    await autoSave()
  }, 3000) // 3秒后自动保存
}

// 自动保存
const autoSave = async () => {
  // 检查内容是否有变化
  const currentContent = JSON.stringify(diagnosisForm)
  if (currentContent === lastSavedContent.value) {
    autoSaveStatus.value = ''
    return
  }

  // 只有在有基本内容时才自动保存
  if (!diagnosisForm.diagnose.trim()) {
    autoSaveStatus.value = ''
    return
  }

  try {
    autoSaveStatus.value = '正在保存...'
    autoSaveType.value = 'primary'

    // 保留原状态，除非是新诊断
    if (!diagnosisForm.id) {
      diagnosisForm.status = '0' // 草稿状态
    }

    const api = diagnosisForm.id ? updateDiagnosis : addDiagnosis
    const res = await api(diagnosisForm)

    if (res.code === 200) {
      // 更新表单数据
      Object.assign(diagnosisForm, res.data)

      // 更新最后保存的内容
      lastSavedContent.value = JSON.stringify(diagnosisForm)

      autoSaveStatus.value = '自动保存成功'
      autoSaveType.value = 'success'

      // 3秒后清除状态
      setTimeout(() => {
        autoSaveStatus.value = ''
      }, 3000)

      // 通知父组件
      emit('diagnosis-save', diagnosisForm)
    } else {
      autoSaveStatus.value = '自动保存失败'
      autoSaveType.value = 'danger'
    }
  } catch (error) {
    console.error('自动保存失败:', error)
    autoSaveStatus.value = '自动保存失败'
    autoSaveType.value = 'danger'
  }
}

// 清空影像所见
const clearFindings = () => {
  diagnosisForm.diagnose = ''
  handleContentChange()
}

// 清空影像意见
const clearOpinion = () => {
  diagnosisForm.recommendation = ''
  handleContentChange()
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  try {
    const date = new Date(dateTime)
    if (isNaN(date.getTime())) return ''

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}`
  } catch (error) {
    return ''
  }
}

// DCM4CHEE服务地址配置
const dcm4cheeServerUrl = ref('')
const dcm4cheeConfigError = ref(false)

// 构建查看器URL
const buildViewerUrl = (studyId, options = {}) => {
  const {isFullscreen = false, allStudyUids = []} = options

  // 从当前URL分析获取baseUrl
  const currentUrl = window.location
  const protocol = currentUrl.protocol
  const host = currentUrl.host
  const baseUrl = `${protocol}//${host}`

  // 构建参数对象
  const params = {}

  if (isFullscreen) {
    // 全屏模式使用桌面端查看器
    // 根据当前URL分析构建完整的查看器URL
    const basePath = import.meta.env.BASE_URL.replace(/\/$/, '') // 移除末尾的斜杠
    const fullUrl = baseUrl + '/desktop/index.html'

    params.studyInstanceUid = studyId

    // 添加全屏参数
    params.fullscreen = 'true'
    params.mode = 'advanced'
    //alert(`${fullUrl}?${new URLSearchParams(params).toString()}`)

    // 构建完整URL
    return `${fullUrl}?${new URLSearchParams(params).toString()}`
  } else {
    // 嵌入模式使用移动端查看器
    // 根据当前URL分析构建完整的查看器URL
    const basePath = import.meta.env.BASE_URL.replace(/\/$/, '') // 移除末尾的斜杠
    const fullUrl = baseUrl  + '/mobile/dicomViewer.html'

    params.studyInstanceUid = studyId

    return `${fullUrl}?${new URLSearchParams(params).toString()}`
  }
}

// 应用模板
const applyTemplate = (template) => {
  if (!canEdit.value) {
    ElMessage.warning('当前状态不允许编辑')
    return
  }

  console.log('应用模板数据:', template)

  // 应用影像所见
  if (template.findings) {
    diagnosisForm.diagnose = template.findings
  }

  // 应用影像意见
  if (template.opinion) {
    diagnosisForm.recommendation = template.opinion
  }

  // 应用诊断标题（如果有）
  if (template.title) {
    diagnosisForm.title = template.title
  }

  handleContentChange()
  ElMessage.success(`已应用模板：${template.name}`)
}

// 获取诊断数据
const getDiagnosisData = () => {
  return {...diagnosisForm}
}

// 验证表单
const validateForm = async () => {
  try {
    await diagnosisFormRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 预览报告
const previewReport = async () => {
  if (!props.patientData) {
    ElMessage.error('检查数据不存在')
    return
  }

  if (!diagnosisForm.diagnose) {
    ElMessage.error('请先填写影像所见内容')
    return
  }

  try {
    // 调用后台接口格式化报告数据
    const reportData = await formatReportData()

    // 获取适用的报告模版
    const reportTemplate = await getApplicableReportTemplate()

    // 调用Report组件的open方法
    const options = {
      filename: `${props.patientData.patientName || '未知患者'}_${props.patientData.modality || ''}检查报告_${formatDateTime(new Date())}`,
      rptPath: reportTemplate ? null : '/diagnosis-templates/report.rdlx-json', // 有动态模版时不使用静态路径
      templateJson: reportTemplate ? reportTemplate.templateJson : null, // 使用动态模版JSON
      data: reportData
    }

    reportRef.value.open(options)
  } catch (error) {
    console.error('预览报告失败:', error)
    ElMessage.error('预览报告失败，请稍后重试')
  }
}

// 格式化报告数据
const formatReportData = async () => {
  if (!diagnosisForm.id) {
    // 如果没有诊断ID，使用前端格式化
    return formatReportDataLocal()
  }

  try {
    // 调用后台接口格式化报告数据
    const res = await apiFormatReportData(diagnosisForm.id)
    if (res.code === 200) {
      return res.data
    } else {
      console.warn('后台格式化报告数据失败，使用前端格式化:', res.msg)
      return formatReportDataLocal()
    }
  } catch (error) {
    console.error('调用后台格式化报告数据接口失败，使用前端格式化:', error)
    return formatReportDataLocal()
  }
}

// 获取适用的报告模版
const getApplicableReportTemplate = async () => {
  if (!props.patientData) {
    console.warn('患者数据不存在，无法获取报告模版')
    return null
  }

  try {
    const modalityType = props.patientData.modality || 'CT'
    const bodyPart = props.patientData.organ || ''

    console.log(`正在获取报告模版: 检查类型=${modalityType}, 检查部位=${bodyPart}`)

    const response = await getApplicableTemplate(modalityType, bodyPart)

    if (response.code === 200 && response.data) {
      console.log('成功获取报告模版:', response.data.templateName)
      console.log('模版JSON长度:', response.data.templateJson ? response.data.templateJson.length : 0)
      console.log('模版JSON前100字符:', response.data.templateJson ? response.data.templateJson.substring(0, 100) : 'null')
      return response.data
    } else {
      console.warn('未找到适用的报告模版，将使用默认模版')
      return null
    }
  } catch (error) {
    console.error('获取报告模版失败:', error)
    ElMessage.warning('获取报告模版失败，将使用默认模版')
    return null
  }
}

// 前端格式化报告数据（备用方案）
const formatReportDataLocal = () => {
  if (!props.patientData) return {}

  // 计算年龄（与diagnose-editor.vue保持一致）
  const calculateAge = (birthDate) => {
    if (!birthDate) return 0

    try {
      const birth = new Date(birthDate.replace(/-/g, '/'))
      if (isNaN(birth.getTime())) return 0

      const today = new Date()
      let age = today.getFullYear() - birth.getFullYear()
      const monthDiff = today.getMonth() - birth.getMonth()

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--
      }

      return age
    } catch (error) {
      return 0
    }
  }

  // 构建诊断对象（模拟后台的 diagnosis 对象）
  const diagnosis = {
    id: diagnosisForm.id,
    diagnose: diagnosisForm.diagnose || '',
    recommendation: diagnosisForm.recommendation || '',
    positiveNegative: diagnosisForm.positiveNegative || '',
    doctor: diagnosisForm.doctor || userStore.name || '',
    auditBy: diagnosisForm.auditBy || '',
    createTime: diagnosisForm.createTime,
    auditTime: diagnosisForm.auditTime,
    status: diagnosisForm.status
  }

  // 构建检查信息对象（模拟后台的 study 对象）
  const studyData = {
    ...props.patientData,
    // 确保年龄字段存在并且是整数
    age: props.patientData.age || parseInt(calculateAge(props.patientData.patientBirthday)) || 0,
    // 映射诊断内容到模版期望的字段
    see: diagnosisForm.diagnose || '', // 影像所见
    reportDiagnose: diagnosisForm.recommendation || '', // 影像意见/建议
    // 添加检查相关字段
    examCode: props.patientData.accessionNumber || props.patientData.studyInstanceUID || '',
    registerTime: props.patientData.studyDate || formatDateTime(new Date()),
    organ: props.patientData.bodyPartExamined || props.patientData.studyDescription || '',
    examItem: props.patientData.studyDescription || '',
    modality: props.patientData.modality || 'CT'
  }

  // 按照模版期望的数据结构返回
  return {
    // 基本信息
    hospitalName: '鄂托克旗人民医院',
    reportTitle: `${props.patientData.modality || ''}检查报告单`,
    // 患者基本信息（根级别）
    patientName: props.patientData.patientName || '',
    patientSex: props.patientData.patientSex || '',
    // 诊断对象
    diagnosis: diagnosis,
    // 检查信息对象（包含映射后的字段）
    study: studyData,
    qrCode: props.patientData.originalPatientId ? `https://xgyyx.etkqrmyy.com/report/${props.patientData.originalPatientId}` : '',
    generateTime: formatDateTime(new Date())
  }
}

// 创建可打印的报告内容
const createPrintableContent = () => {
  if (!props.patientData || !diagnosisForm.diagnose) {
    return null
  }

  const formatGender = (sex) => {
    if (sex === 'Male' || sex === 'M') return '男'
    if (sex === 'Female' || sex === 'F') return '女'
    return '未知'
  }

  const calculateAge = (birthDate) => {
    if (!birthDate) return '0'
    try {
      const birth = new Date(birthDate.replace(/-/g, '/'))
      if (isNaN(birth.getTime())) return '0'
      const today = new Date()
      let age = today.getFullYear() - birth.getFullYear()
      const monthDiff = today.getMonth() - birth.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--
      }
      return age.toString()
    } catch (error) {
      return '0'
    }
  }

  return `
    <div class="report-container">
      <div class="report-header">
        <div class="hospital-name">鄂托克旗人民医院</div>
        <div class="report-title">${props.patientData.modality || 'CT'}检查报告单</div>
      </div>
      
      <div class="patient-info-grid">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">姓名：</span>
            <span class="info-value">${props.patientData.patientName || ''}</span>
          </div>
          <div class="info-item">
            <span class="info-label">性别：</span>
            <span class="info-value">${formatGender(props.patientData.patientSex)}</span>
          </div>
          <div class="info-item">
            <span class="info-label">年龄：</span>
            <span class="info-value">${calculateAge(props.patientData.patientBirthday)}岁</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">检查时间：</span>
            <span class="info-value">${formatDateTime(props.patientData.checkFinishTime) || ''}</span>
          </div>
          <div class="info-item">
            <span class="info-label">检查部位：</span>
            <span class="info-value">${props.patientData.organ || ''}</span>
          </div>
        </div>
      </div>
      
      <div class="diagnosis-content">
        <div class="section">
          <div class="section-title">影像所见：</div>
          <div class="section-content">${diagnosisForm.diagnose}</div>
        </div>
        <div class="section">
          <div class="section-title">影像意见：</div>
          <div class="section-content">${diagnosisForm.recommendation || ''}</div>
        </div>
      </div>
      
      <div class="signature-section">
        <div class="signature-item">
          <span>报告医生：${diagnosisForm.doctor || userStore.name}</span>
        </div>
        ${diagnosisForm.auditBy ? `<div class="signature-item"><span>审核医生：${diagnosisForm.auditBy}</span></div>` : ''}
        <div class="signature-item">
          <span>报告时间：${formatDateTime(new Date())}</span>
        </div>
      </div>
    </div>
  `
}

// 获取studyInstanceUid
const fetchStudyInstanceUid = async () => {
  if (!props.patientData) {
    console.warn('patientData is undefined in fetchStudyInstanceUid')
    return false
  }

  try {
    isLoadingStudyUid.value = true

    // 使用患者ID获取studyInstanceUid
    const patientId = props.patientData.originalPatientId
    if (!patientId) {
      console.warn('没有患者ID，无法获取影像数据')
      return false
    }

    console.log('正在获取患者影像数据，患者ID:', patientId)
    const dicomRes = await getStudyInstanceUid(patientId)

    // 检查代理响应
    if (dicomRes && dicomRes.code === 200 && dicomRes.data) {
      // 代理返回的数据在data属性中
      const dicomData = dicomRes.data

      // 检查是否是数组并且有数据
      if (Array.isArray(dicomData) && dicomData.length > 0) {
        // 提取所有有效的StudyInstanceUID
        const validStudyUids = []

        dicomData.forEach((studyData, index) => {
          // 检查是否包含StudyInstanceUID (0020,000D)
          if (studyData && studyData['0020000D'] && studyData['0020000D'].Value && studyData['0020000D'].Value.length > 0) {
            const studyUid = studyData['0020000D'].Value[0]
            validStudyUids.push({
              studyInstanceUid: studyUid,
              studyDate: studyData['00080020']?.Value?.[0] || '', // Study Date
              studyTime: studyData['00080030']?.Value?.[0] || '', // Study Time
              studyDescription: studyData['00081030']?.Value?.[0] || '', // Study Description
              modality: studyData['00080060']?.Value?.[0] || '', // Modality
              accessionNumber: studyData['00080050']?.Value?.[0] || '', // Accession Number
              index: index
            })
          }
        })

        if (validStudyUids.length > 0) {
          // 保存所有StudyInstanceUID
          allStudyInstanceUids.value = validStudyUids

          if (validStudyUids.length === 1) {
            // 只有一个检查，直接使用
            studyInstanceUid.value = validStudyUids[0].studyInstanceUid
            console.log('成功获取1个StudyInstanceUID:', validStudyUids[0])
            return true
          } else {
            // 多个检查，不自动选择，等待用户选择
            console.log(`成功获取${validStudyUids.length}个StudyInstanceUID，等待用户选择`)
            return true
          }
        } else {
          // 没有找到有效的studyInstanceUid
          console.log('未找到有效的studyInstanceUid，DICOM数据结构:', JSON.stringify(dicomData))
          return false
        }
      } else {
        // 没有找到影像数据
        console.log('患者没有影像数据，没有返回数组数据')
        return false
      }
    } else {
      // 代理请求失败
      console.log('代理请求失败或返回的数据格式不正确:', dicomRes)
      return false
    }
  } catch (error) {
    console.error('获取studyInstanceUid失败:', error)
    return false
  } finally {
    isLoadingStudyUid.value = false
  }
}

// 查看影像
const viewImages = async () => {

  if (!props.patientData) {
    console.warn('patientData is undefined in viewImages')
    ElMessage.warning('未找到检查数据')
    return
  }


  // 如果还没有获取studyInstanceUid，先尝试获取
  if (allStudyInstanceUids.value.length === 0) {
    const success = await fetchStudyInstanceUid()
    if (!success) {
      ElMessage.warning('无法获取影像数据，请稍后重试')
      return
    }
  }

  // 检查是否有多个检查需要用户选择
  if (allStudyInstanceUids.value.length > 1 && !studyInstanceUid.value) {
    // 显示检查选择对话框
    studySelectionVisible.value = true
    return
  }

  // 如果只有一个检查或已经选择了检查，直接打开查看器
  if (studyInstanceUid.value) {
    fullscreenViewerVisible.value = true
  } else {
    ElMessage.warning('请先选择要查看的检查')
  }
}

// 处理检查选择表格行选择变化
const handleStudySelectionChange = (currentRow) => {
  if (currentRow) {
    selectedStudyForViewing.value = currentRow.studyInstanceUid
  }
}

// 选择检查并查看
const selectStudyAndView = (studyUid) => {
  if (!studyUid) {
    ElMessage.warning('请选择要查看的检查')
    return
  }

  const selectedStudy = allStudyInstanceUids.value.find(study => study.studyInstanceUid === studyUid)
  if (selectedStudy) {
    studyInstanceUid.value = studyUid
    studySelectionVisible.value = false

    console.log('选择查看检查:', selectedStudy)
    ElMessage.success(`已选择检查: ${selectedStudy.studyDescription || '检查 ' + (selectedStudy.index + 1)}`)

    // 打开影像查看器
    fullscreenViewerVisible.value = true
  }
}

// 确认选择的检查
const confirmSelectedStudy = () => {
  if (selectedStudyForViewing.value) {
    selectStudyAndView(selectedStudyForViewing.value)
  } else {
    ElMessage.warning('请先选择一个检查')
  }
}

// 切换检查（用于下拉菜单）
const switchStudy = (selectedStudyUid) => {
  const selectedStudy = allStudyInstanceUids.value.find(study => study.studyInstanceUid === selectedStudyUid)
  if (selectedStudy) {
    studyInstanceUid.value = selectedStudyUid
    console.log('切换到检查:', selectedStudy)
    ElMessage.success(`已切换到检查: ${selectedStudy.studyDescription || '检查 ' + (selectedStudy.index + 1)}`)
  }
}

// 格式化检查日期
const formatStudyDate = (studyDate) => {
  if (!studyDate || studyDate.length !== 8) return studyDate

  try {
    // DICOM日期格式: YYYYMMDD
    const year = studyDate.substring(0, 4)
    const month = studyDate.substring(4, 6)
    const day = studyDate.substring(6, 8)
    return `${year}-${month}-${day}`
  } catch (error) {
    return studyDate
  }
}

// 格式化检查时间
const formatStudyTime = (studyTime) => {
  if (!studyTime || studyTime.length < 6) return studyTime || '-'

  try {
    // DICOM时间格式: HHMMSS.FFFFFF
    const hour = studyTime.substring(0, 2)
    const minute = studyTime.substring(2, 4)
    const second = studyTime.substring(4, 6)
    return `${hour}:${minute}:${second}`
  } catch (error) {
    return studyTime
  }
}

// 关闭全屏影像阅读器
const handleCloseViewer = () => {
  fullscreenViewerVisible.value = false
}

// 处理保存
const handleSave = async () => {
  try {
    await diagnosisFormRef.value.validate()
    saveLoading.value = true

    diagnosisForm.status = '0' // 草稿状态
    const api = diagnosisForm.id ? updateDiagnosis : addDiagnosis
    const res = await api(diagnosisForm)

    if (res.code === 200) {
      Object.assign(diagnosisForm, res.data)
      lastSavedContent.value = JSON.stringify(diagnosisForm)
      emit('diagnosis-save', diagnosisForm)
      ElMessage.success('保存成功')
    } else {
      ElMessage.error(res.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saveLoading.value = false
  }
}

// 处理提交
const handleSubmit = async () => {
  try {
    await diagnosisFormRef.value.validate()
    submitLoading.value = true

    diagnosisForm.status = '1' // 待审核状态
    const api = diagnosisForm.id ? updateDiagnosis : addDiagnosis
    const res = await api(diagnosisForm)

    if (res.code === 200) {
      Object.assign(diagnosisForm, res.data)
      lastSavedContent.value = JSON.stringify(diagnosisForm)
      emit('diagnosis-submit', diagnosisForm)
      ElMessage.success('提交成功')
    } else {
      ElMessage.error(res.msg || '提交失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

// 处理审核
const handleAudit = async () => {
  try {
    await ElMessageBox.confirm(
        '确定要审核此诊断报告吗？审核后将不能修改。',
        '审核确认',
        {
          confirmButtonText: '确定审核',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    await diagnosisFormRef.value.validate()
    auditLoading.value = true

    diagnosisForm.status = '2' // 已审核状态
    diagnosisForm.auditBy = userStore.name
    diagnosisForm.auditTime = new Date()

    const api = diagnosisForm.id ? updateDiagnosis : addDiagnosis
    const res = await api(diagnosisForm)

    if (res.code === 200) {
      Object.assign(diagnosisForm, res.data)
      lastSavedContent.value = JSON.stringify(diagnosisForm)
      emit('diagnosis-audit', diagnosisForm)
      ElMessage.success('审核成功')
    } else {
      ElMessage.error(res.msg || '审核失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核失败:', error)
      ElMessage.error('审核失败，请重试')
    }
  } finally {
    auditLoading.value = false
  }
}

// 处理反审核
const handleUnaudit = async () => {
  try {
    await ElMessageBox.confirm(
        '确定要反审核此诊断报告吗？反审核后可以重新编辑。',
        '反审核确认',
        {
          confirmButtonText: '确定反审核',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    unauditLoading.value = true

    diagnosisForm.status = '1' // 回到待审核状态
    diagnosisForm.auditBy = null
    diagnosisForm.auditTime = null

    const res = await updateDiagnosis(diagnosisForm)

    if (res.code === 200) {
      Object.assign(diagnosisForm, res.data)
      lastSavedContent.value = JSON.stringify(diagnosisForm)
      emit('diagnosis-audit', diagnosisForm)
      ElMessage.success('反审核成功')
    } else {
      ElMessage.error(res.msg || '反审核失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('反审核失败:', error)
      ElMessage.error('反审核失败，请重试')
    }
  } finally {
    unauditLoading.value = false
  }
}

// 处理预览
const handlePreview = () => {
  previewReport()
}

// 处理查看影像
const handleViewImages = () => {
  viewImages()
}

// 处理查看操作记录
const handleViewRecords = () => {
  if (!diagnosisForm.id) {
    ElMessage.warning('请先保存诊断后再查看操作记录')
    return
  }
  diagnosisRecordViewerRef.value?.open()
}

// 处理记录查看器关闭
const handleRecordViewerClose = () => {
  // 可以在这里添加关闭后的逻辑
}

// 会诊相关响应式数据
const consultationDialogVisible = ref(false)
const consultationLoading = ref(false)
const submitConsultationLoading = ref(false)
const consultantLoading = ref(false)
const consultantOptions = ref([])
const consultationFormRef = ref()

// 会诊表单数据
const consultationForm = reactive({
  consultantId: undefined,
  urgency: 'NORMAL',
  reason: '',
  clinicalDescription: ''
})

// 会诊表单验证规则
const consultationRules = {
  consultantId: [
    {required: true, message: '请选择会诊专家', trigger: 'change'}
  ],
  reason: [
    {required: true, message: '请输入会诊原因', trigger: 'blur'},
    {min: 10, message: '会诊原因至少需要10个字符', trigger: 'blur'}
  ],
  urgency: [
    {required: true, message: '请选择紧急程度', trigger: 'change'}
  ]
}

// 计算是否可以申请会诊
const canRequestConsultation = computed(() => {
  return diagnosisForm.id && diagnosisForm.diagnose?.trim() && !isAudited.value
})

// 计算患者年龄
const calculatePatientAge = () => {
  if (!props.patientData?.patientBirthday) return '未知'
  try {
    const birth = new Date(props.patientData.patientBirthday.replace(/-/g, '/'))
    if (isNaN(birth.getTime())) return '未知'
    const today = new Date()
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    return age.toString()
  } catch (error) {
    return '未知'
  }
}

// 加载所有可用的会诊专家
const loadAllConsultants = async () => {
  // 如果已经有数据，不重复加载
  if (consultantOptions.value.length > 0) {
    return
  }

  try {
    consultantLoading.value = true
    const params = {
      pageNum: 1,
      pageSize: 50, // 增加页面大小以获取更多专家
      status: '0' // 启用状态
    }

    const response = await listUser(params)
    if (response.code === 200 && response.rows) {
      // 过滤掉当前用户，只保留有效的专家用户
      consultantOptions.value = response.rows.filter(user =>
          user.userId !== userStore.id &&
          user.nickName !== userStore.name &&
          user.status === '0' // 确保用户状态为启用
      )
    } else {
      consultantOptions.value = []
    }
  } catch (error) {
    console.error('加载会诊专家失败:', error)
    consultantOptions.value = []
    ElMessage.error('加载会诊专家失败')
  } finally {
    consultantLoading.value = false
  }
}

// 搜索会诊专家
const searchConsultants = async (query) => {
  if (!query || query.length < 1) {
    // 如果没有搜索关键词，加载所有专家
    await loadAllConsultants()
    return
  }

  try {
    consultantLoading.value = true
    const params = {
      pageNum: 1,
      pageSize: 20,
      nickName: query,
      status: '0' // 启用状态
    }

    const response = await listUser(params)
    if (response.code === 200 && response.rows) {
      // 过滤掉当前用户
      consultantOptions.value = response.rows.filter(user =>
          user.userId !== userStore.id &&
          user.nickName !== userStore.name &&
          user.status === '0'
      )
    } else {
      consultantOptions.value = []
    }
  } catch (error) {
    console.error('搜索会诊专家失败:', error)
    consultantOptions.value = []
    ElMessage.error('搜索会诊专家失败')
  } finally {
    consultantLoading.value = false
  }
}

// 处理申请会诊
const handleRequestConsultation = () => {
  if (!canRequestConsultation.value) {
    ElMessage.warning('请先完善诊断内容才能申请会诊')
    return
  }

  // 重置表单
  Object.assign(consultationForm, {
    consultantId: undefined,
    urgency: 'NORMAL',
    reason: '',
    clinicalDescription: ''
  })
  consultantOptions.value = []

  consultationDialogVisible.value = true
}

// 提交会诊申请
const submitConsultationRequest = async () => {
  try {
    await consultationFormRef.value.validate()

    submitConsultationLoading.value = true

    // 查找选中的专家信息
    const selectedConsultant = consultantOptions.value.find(c => c.userId === consultationForm.consultantId)

    // 生成请求编号：HZ + 年月日 + 时分秒 + 随机数
    const now = new Date()
    const dateStr = now.getFullYear().toString() +
        (now.getMonth() + 1).toString().padStart(2, '0') +
        now.getDate().toString().padStart(2, '0')
    const timeStr = now.getHours().toString().padStart(2, '0') +
        now.getMinutes().toString().padStart(2, '0') +
        now.getSeconds().toString().padStart(2, '0')
    const randomStr = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    const requestNo = `HZ${dateStr}${timeStr}${randomStr}`

    const requestData = {
      // 请求编号
      requestNo: requestNo,

      // 基本信息
      patientId: props.patientData.patientId || props.patientData.id,
      studyId: props.patientData.studyInstanceUid || props.patientData.id,
      checkId: diagnosisForm.checkId || diagnosisForm.id,

      // 申请信息
      requesterId: userStore.id,
      requesterName: userStore.name,
      requestReason: consultationForm.reason,
      requestDescription: consultationForm.clinicalDescription,
      urgencyLevel: consultationForm.urgency,

      // 专家信息
      consultantId: consultationForm.consultantId,
      consultantName: selectedConsultant?.nickName || '',
      consultantPhone: selectedConsultant?.phonenumber || '',

      // 医院信息
      hospitalId: userStore.deptId || '1',
      hospitalName: userStore.deptName || '鄂托克旗人民医院',

      // 状态
      status: 'PENDING',
      requestTime: new Date()
    }

    const response = await createConsultationRequest(requestData)

    if (response.code === 200) {
      ElMessage.success('会诊申请提交成功')
      consultationDialogVisible.value = false

      // 可以在这里添加刷新逻辑或通知父组件
      // emit('consultation-requested', response.data)
    } else {
      ElMessage.error(response.msg || '提交会诊申请失败')
    }
  } catch (error) {
    console.error('提交会诊申请失败:', error)
    ElMessage.error('提交会诊申请失败')
  } finally {
    submitConsultationLoading.value = false
  }
}


// 处理保存为模板
const handleSaveAsTemplate = () => {
  // 触发保存为模板事件
  emit('template-apply', getDiagnosisData())
}

// 组件挂载
onMounted(async () => {
  // 初始化最后保存的内容
  lastSavedContent.value = JSON.stringify(diagnosisForm)

  // 预加载studyInstanceUid信息（但不自动选择）
  if (props.patientData && props.patientData.hasImages !== false) {
    await fetchStudyInstanceUid()
  }
})

// 组件卸载前清理
onBeforeUnmount(() => {
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value)
  }
})

// 暴露方法
defineExpose({
  applyTemplate,
  getDiagnosisData,
  validateForm,
  previewReport,
  viewImages
})
</script>

<style scoped>
.diagnosis-report {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.report-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  min-height: 0;
}

.report-editor {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
  padding-bottom: 12px;
  min-height: 0;
}

.diagnosis-form {
  height: 100%;
}

/* 检查选择器样式 */
.study-item {
  padding: 4px 0;
}

.study-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.study-info {
  font-size: 12px;
  color: #909399;
}

.study-info span {
  margin-right: 8px;
}

:deep(.el-dropdown-menu__item.is-active) {
  background-color: #ecf5ff;
  color: #409eff;
}

/* 检查选择对话框样式 */
.study-selection-content {
  padding: 0;
}

.selection-tip {
  margin: 0 0 16px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.study-selection-content .el-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.study-selection-content .el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

.study-selection-content .el-table .current-row {
  background-color: #ecf5ff;
}

.study-selection-content .el-table .current-row td {
  background-color: #ecf5ff !important;
}

.report-section {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.title-icon {
  color: #409eff;
  font-size: 16px;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.form-item {
  margin: 0;
}

.form-item :deep(.el-form-item__content) {
  margin: 0;
}

.report-textarea {
  border: none;
  border-radius: 0;
}

.report-textarea :deep(.el-textarea__inner) {
  border: none;
  border-radius: 0;
  box-shadow: none;
  padding: 10px;
  font-size: 14px;
  line-height: 1.6;
  resize: none;
}

.report-textarea :deep(.el-textarea__inner:focus) {
  box-shadow: none;
}

.result-radio-group {
  padding: 10px;
  display: flex;
  gap: 16px;
}

.result-radio {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.result-radio:hover {
  border-color: #409eff;
}

.result-radio.positive :deep(.el-radio__input.is-checked) + .el-radio__label {
  color: #67c23a;
}

.result-radio.negative :deep(.el-radio__input.is-checked) + .el-radio__label {
  color: #f56c6c;
}

.result-radio :deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: currentColor;
  border-color: currentColor;
}

.radio-icon {
  font-size: 18px;
}

.doctor-info {
  padding: 10px;
  background: #f8f9fa;
}

.doctor-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.doctor-item:last-child {
  margin-bottom: 0;
}

.doctor-label {
  font-size: 12px;
  color: #909399;
  min-width: 60px;
}

.doctor-name {
  font-weight: 500;
  color: #303133;
}

.doctor-time {
  font-size: 12px;
  color: #909399;
  margin-left: auto;
}

.auto-save-status {
  position: fixed;
  bottom: 80px;
  right: 20px;
  z-index: 1000;
}

.action-buttons {
  padding: 4px 5px;
  border-top: 1px solid #e4e7ed;
  background: #fafbfc;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
  position: sticky;
  bottom: 0;
  z-index: 10;
  flex-shrink: 0;
}

.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.button-group .el-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .report-section {
    margin-bottom: 12px;
  }

  .section-header {
    padding: 6px 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .section-actions {
    align-self: flex-end;
  }

  .report-editor {
    padding: 12px;
  }

  .report-textarea :deep(.el-textarea__inner) {
    padding: 12px;
  }

  .result-radio-group {
    padding: 12px;
    flex-direction: column;
    gap: 6px;
  }

  .doctor-info {
    padding: 12px;
  }

  .doctor-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .doctor-time {
    margin-left: 0;
  }

  .action-buttons {
    padding: 8px;
    flex-direction: column;
    align-items: stretch;
  }

  .button-group {
    justify-content: center;
    gap: 6px;
  }

  .button-group .el-button {
    flex: 1;
    min-width: 70px;
    padding: 6px 12px;
    font-size: 12px;
  }
}

/* 全屏影像阅读器样式 */
.fullscreen-viewer-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.fullscreen-viewer-content {
  height: 100%;
  width: 100%;
  background: #1a1a1a;
}

.fullscreen-viewer-content :deep(.iframe-container) {
  height: 100%;
  width: 100%;
}

.fullscreen-viewer-content :deep(iframe) {
  height: 100%;
  width: 100%;
  border: none;
  background: #1a1a1a;
}

/* 会诊对话框样式 */
.patient-info-display {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
}

.patient-info-display span {
  color: #606266;
  font-size: 14px;
}

.patient-info-display strong {
  color: #303133;
  font-weight: 600;
}

.mt-2 {
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 会诊专家选项样式 */
.consultant-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 0;
  min-height: 40px;
}

.consultant-info {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.consultant-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  white-space: nowrap;
}

.consultant-dept {
  color: #909399;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.consultant-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* 下拉框选项悬停效果 */
:deep(.el-select-dropdown__item) {
  padding: 8px 12px !important;
  line-height: 1.5 !important;
  min-height: 40px !important;
  display: flex !important;
  align-items: center !important;
}

:deep(.el-select-dropdown__item:hover) {
  background-color: #f5f7fa;
}

/* 确保下拉框选项内容完整显示 */
:deep(.el-select-dropdown) {
  .el-select-dropdown__item {
    height: auto !important;
    min-height: 40px !important;
  }
}
</style>
