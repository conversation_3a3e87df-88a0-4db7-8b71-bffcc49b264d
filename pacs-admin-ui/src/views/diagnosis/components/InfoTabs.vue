<template>
  <div class="info-tabs">
    <!-- Tab导航 -->
    <el-tabs v-model="activeTab" class="main-tabs">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <BasicInfo :patient-data="patientData" />
      </el-tab-pane>

      <!-- 附加信息 -->
      <el-tab-pane label="附加信息" name="additional">
        <AdditionalInfo :patient-data="patientData" />
      </el-tab-pane>

      <!-- 费用明细 -->
      <el-tab-pane label="费用明细" name="cost">
        <CostDetails :patient-data="patientData" />
      </el-tab-pane>

      <!-- 检查报告 -->
      <el-tab-pane label="检查报告" name="report">
        <DiagnosisReport
          ref="diagnosisReportRef"
          :patient-data="patientData"
          :diagnosis-data="diagnosisData"
          @diagnosis-save="handleDiagnosisSave"
          @diagnosis-submit="handleSubmit"
          @diagnosis-audit="handleAudit"
          @template-apply="handleTemplateApply"
        />
      </el-tab-pane>
    </el-tabs>


    <!-- 保存为模板对话框 -->
    <el-dialog
      v-model="saveTemplateDialogVisible"
      title="保存为模板"
      width="600px"
      :before-close="handleSaveTemplateDialogClose"
    >
      <el-form
        ref="templateFormRef"
        :model="templateForm"
        :rules="templateRules"
        label-width="100px"
      >
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="templateForm.name" placeholder="请输入模板名称" />
        </el-form-item>

        <el-form-item label="检查类型" prop="modalityType">
          <el-select v-model="templateForm.modalityType" placeholder="请选择检查类型">
            <el-option label="CT" value="CT" />
            <el-option label="MRI" value="MRI" />
            <el-option label="DR" value="DR" />
            <el-option label="US" value="US" />
            <el-option label="CR" value="CR" />
            <el-option label="DX" value="DX" />
          </el-select>
        </el-form-item>

        <el-form-item label="检查部位" prop="bodyPart">
          <el-input v-model="templateForm.bodyPart" placeholder="请输入检查部位" />
        </el-form-item>

        <el-form-item label="诊断标题">
          <el-input v-model="templateForm.title" placeholder="请输入诊断标题（可选）" />
        </el-form-item>

        <el-form-item label="关键词">
          <el-input v-model="templateForm.keywords" placeholder="请输入关键词，用逗号分隔" />
        </el-form-item>

        <el-form-item label="模板类型">
          <el-radio-group v-model="templateForm.isPublic">
            <el-radio label="0">私有模板</el-radio>
            <el-radio label="1">公共模板</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="设为默认">
          <el-switch v-model="templateForm.isDefault" active-value="1" inactive-value="0" />
        </el-form-item>

        <el-form-item label="影像所见">
          <el-input
            v-model="templateForm.findings"
            type="textarea"
            :rows="4"
            readonly
            placeholder="将自动填入当前报告的影像所见内容"
          />
        </el-form-item>

        <el-form-item label="影像意见">
          <el-input
            v-model="templateForm.opinion"
            type="textarea"
            :rows="3"
            readonly
            placeholder="将自动填入当前报告的影像意见内容"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleSaveTemplateDialogClose">取消</el-button>
          <el-button type="primary" @click="handleConfirmSaveTemplate" :loading="saveTemplateLoading">
            保存模板
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { addTemplate } from '@/api/diagnosis/template'
import BasicInfo from './BasicInfo.vue'
import AdditionalInfo from './AdditionalInfo.vue'
import CostDetails from './CostDetails.vue'
import DiagnosisReport from './DiagnosisReport.vue'
import useUserStore from '@/store/modules/user'

// Props
const props = defineProps({
  patientData: {
    type: Object,
    required: true
  },
  diagnosisData: {
    type: Object,
    default: () => ({})
  },
  currentRole: {
    type: String,
    default: 'diagnoser',
    validator: (value) => ['diagnoser', 'auditor'].includes(value)
  },
  userPermissions: {
    type: Object,
    default: () => ({
      canDiagnose: true,
      canAudit: true,
      canEdit: true,
      canDelete: false
    })
  }
})

// Emits
const emit = defineEmits([
  'diagnosis-save',
  'diagnosis-submit', 
  'diagnosis-audit',
  'diagnosis-unaudit'
])

// 响应式数据
const activeTab = ref('basic')
const diagnosisReportRef = ref()
const saveTemplateDialogVisible = ref(false)
const saveTemplateLoading = ref(false)
const templateFormRef = ref(null)

// Store
const userStore = useUserStore()

// 模板表单数据
const templateForm = reactive({
  name: '',
  modalityType: '',
  bodyPart: '',
  title: '',
  findings: '',
  opinion: '',
  keywords: '',
  isPublic: '0',
  isDefault: '0',
  sortOrder: 0
})

// 模板表单验证规则
const templateRules = reactive({
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  modalityType: [{ required: true, message: '请选择检查类型', trigger: 'change' }],
  bodyPart: [{ required: true, message: '请输入检查部位', trigger: 'blur' }]
})

// 计算属性
const isAudited = computed(() => {
  return props.diagnosisData?.status === '2'
})

const isCreator = computed(() => {
  if (!props.diagnosisData?.id) return true
  
  // 检查创建者权限
  if (props.diagnosisData.createBy === 'admin') return true
  if (props.diagnosisData.createBy === userStore.id || props.diagnosisData.createBy === userStore.name) return true
  if (props.diagnosisData.doctor === userStore.name) return true
  
  return false
})

// 新增计算属性：根据角色和权限控制功能可见性
const canShowDiagnosisTab = computed(() => {
  return props.currentRole === 'diagnoser' || props.userPermissions.canAudit
})

const shouldAutoFocusDiagnosis = computed(() => {
  return props.currentRole === 'diagnoser' && !props.diagnosisData?.id
})

const tabOrder = computed(() => {
  const tabs = []

  // 根据角色调整Tab顺序
  if (props.currentRole === 'diagnoser') {
    tabs.push(
      { name: 'report', label: '检查报告', priority: 1 },
      { name: 'basic', label: '基本信息', priority: 2 },
      { name: 'additional', label: '附加信息', priority: 3 },
      { name: 'cost', label: '费用明细', priority: 4 }
    )
  } else {
    tabs.push(
      { name: 'basic', label: '基本信息', priority: 1 },
      { name: 'report', label: '检查报告', priority: 2 },
      { name: 'additional', label: '附加信息', priority: 3 },
      { name: 'cost', label: '费用明细', priority: 4 }
    )
  }

  return tabs.sort((a, b) => a.priority - b.priority)
})

// 监听患者数据变化，自动切换到基本信息tab
watch(() => props.patientData, (newVal) => {
  if (newVal) {
    if (props.currentRole === 'diagnoser') {
      // 诊断医师优先显示报告页面
      activeTab.value = 'report'
    } else {
      // 审核医师优先显示基本信息
      activeTab.value = 'basic'
    }
  }
}, { immediate: true })

// 监听角色变化
watch(() => props.currentRole, (newRole) => {
  if (newRole === 'diagnoser' && props.patientData) {
    activeTab.value = 'report'
  } else if (newRole === 'auditor' && props.patientData) {
    activeTab.value = 'basic'
  }
})

// 处理模板应用
const handleTemplateApply = (template) => {
  if (diagnosisReportRef.value) {
    diagnosisReportRef.value.applyTemplate(template)
  }
}

// 处理诊断保存
const handleDiagnosisSave = (data) => {
  emit('diagnosis-save', data)
}

// 处理诊断提交
const handleSubmit = (data) => {
  emit('diagnosis-submit', data)
}

// 处理诊断审核
const handleAudit = (data) => {
  emit('diagnosis-audit', data)
}

// 保存为模板
const handleSaveAsTemplate = () => {
  if (!diagnosisReportRef.value) {
    ElMessage.warning('请先填写诊断报告')
    return
  }

  // 获取当前诊断报告数据
  const reportData = diagnosisReportRef.value.getDiagnosisData()

  if (!reportData.diagnose && !reportData.recommendation) {
    ElMessage.warning('请先填写影像所见或影像意见')
    return
  }

  // 重置表单
  Object.keys(templateForm).forEach(key => {
    if (key === 'isPublic' || key === 'isDefault') {
      templateForm[key] = '0'
    } else if (key === 'sortOrder') {
      templateForm[key] = 0
    } else {
      templateForm[key] = ''
    }
  })

  // 填入报告内容
  templateForm.findings = reportData.diagnose || ''
  templateForm.opinion = reportData.recommendation || ''
  templateForm.title = reportData.title || ''

  // 从患者数据中获取检查类型和部位
  if (props.patientData) {
    templateForm.modalityType = props.patientData.modality || ''
    templateForm.bodyPart = props.patientData.organ || ''
  }

  saveTemplateDialogVisible.value = true
}

// 确认保存模板
const handleConfirmSaveTemplate = async () => {
  try {
    await templateFormRef.value.validate()

    saveTemplateLoading.value = true

    const res = await addTemplate(templateForm)

    if (res.code === 200) {
      ElMessage.success('模板保存成功')
      saveTemplateDialogVisible.value = false
    } else {
      ElMessage.error(res.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存模板出错:', error)
    ElMessage.error('系统错误，请联系管理员')
  } finally {
    saveTemplateLoading.value = false
  }
}

// 关闭保存模板对话框
const handleSaveTemplateDialogClose = () => {
  saveTemplateDialogVisible.value = false
}


// 暴露方法
defineExpose({
  applyTemplate: handleTemplateApply,
  handleSaveAsTemplate,
  switchToReport: () => {
    activeTab.value = 'report'
  }
})
</script>

<style scoped>
.info-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.main-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.main-tabs :deep(.el-tabs__content) {
  flex: 1;
  padding: 0;
  overflow: hidden;
  min-height: 0;
}

.main-tabs :deep(.el-tab-pane) {
  height: 100%;
  padding: 12px;
  overflow-y: auto;
  box-sizing: border-box;
}


.main-tabs :deep(.el-tabs__header) {
  margin: 0;
  border-bottom: 2px solid #e4e7ed;
  background: #fafbfc;
}

.main-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 20px;
}

.main-tabs :deep(.el-tabs__item) {
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  font-weight: 500;
  color: #606266;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.main-tabs :deep(.el-tabs__item:hover) {
  color: #409eff;
}

.main-tabs :deep(.el-tabs__item.is-active) {
  color: #409eff;
  border-bottom-color: #409eff;
  background: white;
}

.main-tabs :deep(.el-tabs__active-bar) {
  display: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .main-tabs :deep(.el-tabs__nav-wrap) {
    padding: 0 12px;
  }
  
  .main-tabs :deep(.el-tabs__item) {
    padding: 0 12px;
    font-size: 14px;
  }
  
  .main-tabs :deep(.el-tabs__content) {
    padding: 0;
  }
  
  .main-tabs :deep(.el-tab-pane) {
    padding: 8px 6px;
  }
}

@media (max-width: 480px) {
  .main-tabs :deep(.el-tabs__item) {
    padding: 0 8px;
    font-size: 13px;
  }
}

/* 组件特定样式 - 移除重复的按钮修复 */
</style>
