<template>
  <div class="diagnosis-record-viewer">
    <!-- 诊断记录对话框 -->
    <el-dialog
      v-model="visible"
      title="诊断操作记录"
      width="70%"
      :before-close="handleClose"
      append-to-body
    >
      <!-- 搜索条件 -->
      <div class="search-bar">
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
          <el-form-item label="操作类型" prop="remark">
            <el-select
              v-model="queryParams.remark"
              placeholder="请选择操作类型"
              clearable
              style="width: 180px"
            >
              <el-option label="新增诊断" value="操作类型: 新增诊断" />
              <el-option label="修改诊断" value="操作类型: 修改诊断" />
              <el-option label="删除诊断" value="操作类型: 删除诊断" />
              <el-option label="审核诊断" value="操作类型: 审核诊断" />
              <el-option label="驳回诊断" value="操作类型: 驳回诊断" />
              <el-option label="反审核诊断" value="操作类型: 反审核诊断" />
              <el-option label="提交审核" value="操作类型: 提交审核" />
              <el-option label="撤销审核" value="操作类型: 撤销审核" />
            </el-select>
          </el-form-item>
          <el-form-item label="操作人" prop="createBy">
            <el-input
              v-model="queryParams.createBy"
              placeholder="请输入操作人"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择状态"
              clearable
              style="width: 140px"
            >
              <el-option label="草稿" value="0" />
              <el-option label="待审核" value="1" />
              <el-option label="已审核" value="2" />
              <el-option label="已驳回" value="3" />
              <el-option label="已撤销" value="4" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 记录列表 -->
      <el-table
        v-loading="loading"
        :data="recordList"
        border
        stripe
        size="small"

        :default-sort="{ prop: 'createTime', order: 'descending' }"
      >
        <el-table-column label="序号" type="index" width="100" align="center" />
        
        <el-table-column label="操作时间" prop="createTime" width="180" align="center">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作类型" prop="remark" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getOperationTagType(scope.row.remark)">
              {{ getOperationText(scope.row.remark) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作人" prop="createBy" width="100" align="center" />
        
        <el-table-column label="诊断医生" prop="doctor" width="100" align="center" />
        
        <el-table-column label="状态" prop="status" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="审核人" prop="auditBy" width="100" align="center" />
        
        <el-table-column label="审核时间" prop="auditTime" width="160" align="center">
          <template #default="scope">
            <span>{{ parseTime(scope.row.auditTime) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="阴阳性" prop="positiveNegative" width="80" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.positiveNegative" :type="getPositiveNegativeTagType(scope.row.positiveNegative)">
              {{ getPositiveNegativeText(scope.row.positiveNegative) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              icon="View"
              size="small"
              @click="handleViewDetail(scope.row)"
              link
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="诊断记录详情"
      width="70%"
      append-to-body
    >
      <div v-if="currentRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="操作时间">
            {{ parseTime(currentRecord.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getOperationTagType(currentRecord.remark)">
              {{ getOperationText(currentRecord.remark) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作人">
            {{ currentRecord.createBy }}
          </el-descriptions-item>
          <el-descriptions-item label="诊断医生">
            {{ currentRecord.doctor }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="审核人">
            {{ currentRecord.auditBy || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="审核时间">
            {{ parseTime(currentRecord.auditTime) || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="阴阳性结果">
            <el-tag v-if="currentRecord.positiveNegative" :type="getPositiveNegativeTagType(currentRecord.positiveNegative)">
              {{ getPositiveNegativeText(currentRecord.positiveNegative) }}
            </el-tag>
            <span v-else>无</span>
          </el-descriptions-item>
        </el-descriptions>

        <div class="content-section">
          <h4>影像所见</h4>
          <div class="content-box">
            {{ currentRecord.diagnose || '无' }}
          </div>
        </div>

        <div class="content-section">
          <h4>影像意见</h4>
          <div class="content-box">
            {{ currentRecord.recommendation || '无' }}
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { listDiagnosisRecordByDiagnosisId } from '@/api/diagnosis/diagnosisRecord'
import { parseTime } from '@/utils/ruoyi'

// 组件属性
const props = defineProps({
  diagnosisId: {
    type: Number,
    required: true
  }
})

// 组件事件
const emit = defineEmits(['close'])

// 响应式数据
const visible = ref(false)
const detailVisible = ref(false)
const loading = ref(false)
const recordList = ref([])
const currentRecord = ref(null)
const total = ref(0)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  diagnosisId: null,
  remark: null,
  createBy: null,
  status: null
})

// 查询表单引用
const queryRef = ref()

// 监听diagnosisId变化
watch(() => props.diagnosisId, (newVal) => {
  if (newVal) {
    queryParams.diagnosisId = newVal
  }
}, { immediate: true })

// 获取记录列表
const getList = async () => {
  loading.value = true
  try {
    const response = await listDiagnosisRecordByDiagnosisId(props.diagnosisId)
    if (response.code === 200) {
      recordList.value = response.rows || []
      total.value = response.total || 0
    }
  } catch (error) {
    console.error('获取诊断记录失败:', error)
    ElMessage.error('获取诊断记录失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  queryRef.value?.resetFields()
  queryParams.pageNum = 1
  getList()
}

// 查看详情
const handleViewDetail = (row) => {
  currentRecord.value = row
  detailVisible.value = true
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  emit('close')
}

// 打开对话框
const open = () => {
  visible.value = true
  getList()
}

// 获取操作类型标签样式
const getOperationTagType = (remark) => {
  if (!remark) return ''
  if (remark.includes('新增')) return 'success'
  if (remark.includes('修改')) return 'warning'
  if (remark.includes('删除')) return 'danger'
  if (remark.includes('审核')) return 'primary'
  if (remark.includes('驳回')) return 'danger'
  if (remark.includes('撤销')) return 'info'
  return ''
}

// 获取操作类型文本
const getOperationText = (remark) => {
  if (!remark) return '未知'
  return remark.replace('操作类型: ', '')
}

// 获取状态标签样式
const getStatusTagType = (status) => {
  const statusMap = {
    '0': 'info',     // 草稿
    '1': 'warning',  // 待审核
    '2': 'success',  // 已审核
    '3': 'danger',   // 已驳回
    '4': 'info'      // 已撤销
  }
  return statusMap[status] || ''
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '0': '草稿',
    '1': '待审核',
    '2': '已审核',
    '3': '已驳回',
    '4': '已撤销'
  }
  return statusMap[status] || '未知'
}

// 获取阴阳性标签样式
const getPositiveNegativeTagType = (value) => {
  const typeMap = {
    'positive': 'danger',
    'negative': 'success',
    'uncertain': 'warning'
  }
  return typeMap[value] || ''
}

// 获取阴阳性文本
const getPositiveNegativeText = (value) => {
  const textMap = {
    'positive': '阳性',
    'negative': '阴性',
    'uncertain': '待定'
  }
  return textMap[value] || '未知'
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.diagnosis-record-viewer {
  .search-bar {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  .record-detail {
    .content-section {
      margin-top: 20px;
      
      h4 {
        margin-bottom: 10px;
        color: #303133;
        font-weight: 600;
      }
      
      .content-box {
        padding: 15px;
        background-color: #f9f9f9;
        border-radius: 4px;
        border: 1px solid #e4e7ed;
        min-height: 80px;
        line-height: 1.6;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
}
</style>
