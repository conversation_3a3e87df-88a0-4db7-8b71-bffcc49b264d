<template>
  <div class="diagnosis-workspace">
    <!-- 顶部搜索栏 -->
    <div class="top-search-bar">
      <SearchBar
        :queryParams="queryParams"
        :current-role="currentRole"
        @update:queryParams="handleUpdateQueryParams"
        @search="handleSearch"
        @reset="handleReset"
        @template-manage="handleTemplateManage"
      />
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧病人列表 -->
      <div class="left-panel">
        <!-- 角色切换和快速筛选整合到左侧面板头部 -->
        <div class="panel-header">
          <div class="role-switch-compact">
            <el-radio-group v-model="currentRole" @change="handleRoleChange" size="small">
              <el-radio-button label="diagnoser">诊断</el-radio-button>
              <el-radio-button label="auditor">审核</el-radio-button>
            </el-radio-group>
          </div>

          <div class="quick-filters-compact">
            <el-dropdown @command="setQuickFilter" trigger="click">
              <el-button size="small" type="primary">
                {{ getFilterLabel() }}
                <el-badge :value="getTotalCount()" :max="99" class="filter-badge-compact" />
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <!-- 诊断医生场景 -->
                  <template v-if="currentRole === 'diagnoser'">
                    <el-dropdown-item command="diagnoser_pending">
                      <div class="scenario-menu-item">
                        <span>待诊断</span>
                        <el-badge :value="scenarioStatusCounts.diagnoser_pending" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="diagnoser_diagnosed">
                      <div class="scenario-menu-item">
                        <span>待审核</span>
                        <el-badge :value="scenarioStatusCounts.diagnoser_diagnosed" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="diagnoser_my_all">
                      <div class="scenario-menu-item">
                        <span>我的全部诊断</span>
                        <el-badge :value="scenarioStatusCounts.diagnoser_my_all" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="diagnoser_audited_pass">
                      <div class="scenario-menu-item">
                        <span>我的已审核</span>
                        <el-badge :value="scenarioStatusCounts.diagnoser_audited_pass" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="diagnoser_audited_reject">
                      <div class="scenario-menu-item">
                        <span>审核未通过</span>
                        <el-badge :value="scenarioStatusCounts.diagnoser_audited_reject" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                  </template>

                  <!-- 审核医生场景 -->
                  <template v-else-if="currentRole === 'auditor'">
                    <el-dropdown-item command="auditor_pending">
                      <div class="scenario-menu-item">
                        <span>待审核</span>
                        <el-badge :value="scenarioStatusCounts.auditor_pending" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="auditor_my_audited">
                      <div class="scenario-menu-item">
                        <span>已审核</span>
                        <el-badge :value="scenarioStatusCounts.auditor_my_audited" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="auditor_my_rejected">
                      <div class="scenario-menu-item">
                        <span>拒绝记录</span>
                        <el-badge :value="scenarioStatusCounts.auditor_my_rejected" :max="99" class="menu-badge" />
                      </div>
                    </el-dropdown-item>
                  </template>

                  <el-dropdown-item divided command="all">
                    <div class="scenario-menu-item">
                      <el-icon color="#909399"><List /></el-icon>
                      <span>全部记录</span>
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <PatientList
          :loading="loading"
          :data="patientList"
          :total="total"
          :current-patient="currentPatient"
          :current-role="currentRole"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          @patient-select="handlePatientSelect"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
          @refresh="getPatientList"
        />
      </div>

      <!-- 中间信息展示区域 -->
      <div class="center-panel">
        <InfoTabs
          ref="infoTabsRef"
          v-if="currentPatient"
          :patient-data="currentPatient"
          :diagnosis-data="currentDiagnosis"
          :current-role="currentRole"
          :user-permissions="userPermissions"
          @diagnosis-save="handleDiagnosisSave"
          @diagnosis-submit="handleDiagnosisSubmit"
          @diagnosis-audit="handleDiagnosisAudit"
        />
        <div v-else class="no-patient-selected">
          <el-empty :description="getEmptyDescription()" />
        </div>
      </div>

      <!-- 右侧模板选择区域 -->
      <div class="right-panel">
        <TemplatePanel
          v-if="currentPatient"
          :modality="currentPatient.modality"
          :body-part="currentPatient.organ"
          :current-role="currentRole"
          @template-apply="handleTemplateApply"
          @add-template="handleAddTemplate"
        />
      </div>
    </div>

    <!-- 模板管理对话框 -->
    <el-dialog
      v-model="templateDialogVisible"
      title="模板管理"
      width="90%"
      :before-close="handleTemplateDialogClose"
    >
      <TemplateManagement
        v-if="templateDialogVisible"
        @template-apply="handleTemplateApply"
        @close="templateDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Clock, Edit, Check, Close, User, List, ArrowDown } from '@element-plus/icons-vue'
import SearchBar from './components/SearchBar.vue'
import PatientList from './components/PatientList.vue'
import InfoTabs from './components/InfoTabs.vue'
import TemplatePanel from './components/TemplatePanel.vue'
import TemplateManagement from './template/index.vue'
import { listWithDiagnose, getDiagnosisStatusCount } from '@/api/pacs/study'
import { getDiagnosis } from '@/api/diagnosis/diagnosis'
import {
  DIAGNOSIS_STATUS
} from '@/constants/diagnosisStatus'
import {
  DIAGNOSIS_SCENARIO,
  getDiagnosisScenarioText,
  getScenarioOptionsByRole
} from '@/constants/diagnosisScenario'

// 响应式数据
const loading = ref(false)
const total = ref(0)
const patientList = ref([])
const currentPatient = ref(null)
const currentDiagnosis = ref(null)
const infoTabsRef = ref(null)
const templateDialogVisible = ref(false)

// 状态统计数据
const statusCounts = reactive({
  pending: 0,    // 待诊断
  diagnosed: 0,  // 已诊断
  audited: 0,    // 已审核
  archived: 0    // 院内诊断
})

// 查询参数 - 参考study-list.vue的实现
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  patientId: undefined,
  patientName: undefined,
  examCode: undefined,
  examItem: undefined, // 检查项目
  modality: undefined,
  hospitalId: undefined,
  hospitalName: undefined,
  diagnosisStatus: undefined, // 默认显示全部状态
  organ: undefined, // 检查部位
  examDepartment: undefined, // 申请科室
  examDoctorName: undefined, // 申请医生
  startTime: undefined,
  endTime: undefined
})

// 新增响应式数据
const currentRole = ref('diagnoser') // 当前角色：diagnoser | auditor
const quickFilter = ref('diagnoser_pending') // 快速筛选，使用场景值
const userPermissions = ref({
  canDiagnose: true,
  canAudit: true,
  canEdit: true,
  canDelete: false
})

// 角色状态统计（保留兼容性）
const roleStatusCounts = reactive({
  pending: 0,
  completed: 0
})

// 场景状态统计
const scenarioStatusCounts = reactive({
  // 诊断医生场景
  diagnoser_pending: 0,
  diagnoser_diagnosed: 0,
  diagnoser_my_all: 0,
  diagnoser_audited_pass: 0,
  diagnoser_audited_reject: 0,

  // 审核医生场景
  auditor_pending: 0,
  auditor_my_audited: 0,
  auditor_my_rejected: 0
})

// 获取患者列表 - 修复筛选逻辑
const getPatientList = async () => {
  loading.value = true
  try {
    // 构建请求参数，过滤undefined值
    const params = {}
    Object.keys(queryParams).forEach(key => {
      const value = queryParams[key]
      // 对于diagnosisStatus，空字符串也是有效值（表示查询全部）
      if (key === 'diagnosisStatus') {
        if (value !== undefined && value !== null) {
          params[key] = value
        }
      } else {
        // 其他参数过滤undefined、null和空字符串
        if (value !== undefined && value !== null && value !== '') {
          params[key] = value
        }
      }
    })

    // 添加当前角色参数
    params.currentRole = currentRole.value

    // 添加场景参数
    if (quickFilter.value && quickFilter.value !== 'all') {
      params.scenario = quickFilter.value
    }

    // 同时获取列表数据和统计数据
    const [listRes, statsRes] = await Promise.all([
      listWithDiagnose(params),
      getScenarioStatusCounts()
    ])

    if (listRes.code === 200) {
      patientList.value = listRes.rows || []
      total.value = listRes.total || 0

      // 调试信息 - 仅在全部数据场景下输出
      if (quickFilter.value === 'all') {
        console.log('全部数据加载:', {
          total: total.value,
          dataLength: patientList.value.length,
          params: params
        })
      }
    } else {
      ElMessage.error(listRes.msg || '获取患者列表失败')
      patientList.value = []
      total.value = 0
    }

    // 更新状态统计
    if (statsRes) {
      // 更新兼容性统计
      roleStatusCounts.pending = statsRes.pending || 0
      roleStatusCounts.completed = statsRes.completed || 0

      // 更新场景统计
      Object.keys(scenarioStatusCounts).forEach(key => {
        scenarioStatusCounts[key] = statsRes[key] || 0
      })
    }

  } catch (error) {
    console.error('获取患者列表出错', error)
    ElMessage.error('系统错误，请联系管理员')
    patientList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 新增：获取角色状态统计
const getRoleStatusCounts = async () => {
  try {
    // 构建统计查询参数（排除分页和诊断状态筛选）
    const statsParams = {}
    Object.keys(queryParams).forEach(key => {
      // 排除分页参数和诊断状态，保留其他筛选条件
      if (!['pageNum', 'pageSize', 'diagnosisStatus'].includes(key)) {
        const value = queryParams[key]
        if (value !== undefined && value !== null && value !== '') {
          statsParams[key] = value
        }
      }
    })

    // 添加当前角色参数
    statsParams.currentRole = currentRole.value

    const res = await getDiagnosisStatusCount(statsParams)
    if (res.code === 200) {
      const stats = res.data || {}

      if (currentRole.value === 'diagnoser') {
        return {
          pending: stats[DIAGNOSIS_STATUS.PENDING] || 0,
          completed: (stats[DIAGNOSIS_STATUS.DIAGNOSED] || 0) + (stats[DIAGNOSIS_STATUS.AUDITED] || 0)
        }
      } else {
        return {
          pending: stats[DIAGNOSIS_STATUS.DIAGNOSED] || 0,
          completed: stats[DIAGNOSIS_STATUS.AUDITED] || 0
        }
      }
    }
  } catch (error) {
    console.error('获取状态统计失败', error)
  }

  return { pending: 0, completed: 0 }
}

// 新增：获取场景状态统计
const getScenarioStatusCounts = async () => {
  try {
    // 构建统计查询参数（排除分页和诊断状态筛选）
    const statsParams = {}
    Object.keys(queryParams).forEach(key => {
      // 排除分页参数和诊断状态，保留其他筛选条件
      if (!['pageNum', 'pageSize', 'diagnosisStatus', 'scenario'].includes(key)) {
        const value = queryParams[key]
        if (value !== undefined && value !== null && value !== '') {
          statsParams[key] = value
        }
      }
    })

    // 添加当前角色参数
    statsParams.currentRole = currentRole.value

    // 获取各个场景的统计数据
    const scenarios = currentRole.value === 'diagnoser'
      ? ['diagnoser_pending', 'diagnoser_diagnosed', 'diagnoser_my_all', 'diagnoser_audited_pass', 'diagnoser_audited_reject']
      : ['auditor_pending', 'auditor_my_audited', 'auditor_my_rejected']

    const results = {}

    // 并行获取各场景统计
    const promises = scenarios.map(async scenario => {
      const params = { ...statsParams, scenario }
      const res = await getDiagnosisStatusCount(params)
      if (res.code === 200) {
        const stats = res.data || {}
        // 计算该场景的总数
        const total = Object.values(stats).reduce((sum, count) => sum + (count || 0), 0)
        results[scenario] = total
      } else {
        results[scenario] = 0
      }
    })

    await Promise.all(promises)

    // 计算兼容性统计
    if (currentRole.value === 'diagnoser') {
      results.pending = results.diagnoser_pending || 0
      results.completed = (results.diagnoser_diagnosed || 0) + (results.diagnoser_audited_pass || 0)
    } else {
      results.pending = results.auditor_pending || 0
      results.completed = (results.auditor_my_audited || 0)
    }

    return results
  } catch (error) {
    console.error('获取场景统计失败', error)
    return {}
  }
}

// 处理搜索 - 优化逻辑
const handleSearch = () => {
  queryParams.pageNum = 1
  // 搜索时不改变快速筛选的状态选择
  getPatientList()
}

// 处理重置 - 修复逻辑
const handleReset = () => {
  // 重置所有查询参数为undefined，但保留分页参数
  Object.keys(queryParams).forEach(key => {
    if (key !== 'pageNum' && key !== 'pageSize') {
      queryParams[key] = undefined
    }
  })
  queryParams.pageNum = 1

  // 清空当前选中的患者
  currentPatient.value = null
  currentDiagnosis.value = null

  // 重置后重新应用当前的快速筛选
  applyQuickFilter()
}

// 新增：应用快速筛选逻辑
const applyQuickFilter = () => {
  // 清除诊断状态筛选，使用场景筛选
  queryParams.diagnosisStatus = undefined

  // 场景筛选通过scenario参数传递，在getPatientList中处理
  getPatientList()
}

// 设置快速筛选 - 优化逻辑
const setQuickFilter = (filter) => {
  quickFilter.value = filter
  queryParams.pageNum = 1
  applyQuickFilter()
}

// 处理角色切换 - 优化逻辑
const handleRoleChange = (role) => {
  currentRole.value = role

  // 角色切换时重新计算统计数据
  queryParams.pageNum = 1

  // 根据角色调整默认筛选
  if (role === 'diagnoser') {
    setQuickFilter('diagnoser_pending')
  } else {
    setQuickFilter('auditor_pending')
  }
}

// 获取当前筛选标签
const getFilterLabel = () => {
  if (quickFilter.value === 'all') {
    return '全部记录'
  }
  return getDiagnosisScenarioText(quickFilter.value)
}

// 获取总数
const getTotalCount = () => {
  if (quickFilter.value === 'all') {
    return total.value
  }
  return scenarioStatusCounts[quickFilter.value] || 0
}

// 处理查询参数更新
const handleUpdateQueryParams = (newParams) => {
  Object.assign(queryParams, newParams)
}

// 处理患者选择
const handlePatientSelect = async (patient) => {
  currentPatient.value = patient

  // 加载诊断数据
  if (patient.diagnosis && patient.diagnosis.id) {
    try {
      const res = await getDiagnosis(patient.diagnosis.id)
      if (res.code === 200) {
        currentDiagnosis.value = res.data
      }
    } catch (error) {
      console.error('获取诊断数据失败', error)
    }
  } else {
    // 新建诊断
    currentDiagnosis.value = {
      studyId: patient.id,
      checkId: patient.id,
      diagnose: '',
      recommendation: '',
      status: '0'
    }
  }
}

// 处理分页变化
const handlePageChange = (page) => {
  queryParams.pageNum = page
  getPatientList()
}

// 处理页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.pageNum = 1 // 重置到第一页
  getPatientList()
}

// 处理诊断保存
const handleDiagnosisSave = (diagnosisData) => {
  currentDiagnosis.value = { ...currentDiagnosis.value, ...diagnosisData }
  // 更新患者列表中的诊断状态
  updatePatientDiagnosisStatus()
  // 重新获取统计数据
  getRoleStatusCounts().then(stats => {
    if (stats) {
      roleStatusCounts.pending = stats.pending
      roleStatusCounts.completed = stats.completed
    }
  })
}

// 处理诊断提交
const handleDiagnosisSubmit = (diagnosisData) => {
  currentDiagnosis.value = { ...currentDiagnosis.value, ...diagnosisData }
  updatePatientDiagnosisStatus()
  // 重新获取统计数据
  getRoleStatusCounts().then(stats => {
    if (stats) {
      roleStatusCounts.pending = stats.pending
      roleStatusCounts.completed = stats.completed
    }
  })
}

// 处理诊断审核
const handleDiagnosisAudit = (diagnosisData) => {
  currentDiagnosis.value = { ...currentDiagnosis.value, ...diagnosisData }
  updatePatientDiagnosisStatus()
  // 重新获取统计数据
  getRoleStatusCounts().then(stats => {
    if (stats) {
      roleStatusCounts.pending = stats.pending
      roleStatusCounts.completed = stats.completed
    }
  })
}

// 更新患者列表中的诊断状态
const updatePatientDiagnosisStatus = () => {
  if (currentPatient.value && currentDiagnosis.value) {
    const index = patientList.value.findIndex(p => p.id === currentPatient.value.id)
    if (index !== -1) {
      patientList.value[index].diagnosis = currentDiagnosis.value
    }
  }
}

// 处理模板应用
const handleTemplateApply = (template) => {
  console.log('应用模板:', template)

  // 通过ref调用InfoTabs组件的应用模板方法
  if (infoTabsRef.value) {
    infoTabsRef.value.applyTemplate(template)
    // 关闭模板对话框
    templateDialogVisible.value = false
  } else {
    ElMessage.warning('请先选择患者')
  }
}

// 打开模板管理对话框
const handleTemplateManage = () => {
  templateDialogVisible.value = true
}

// 关闭模板对话框
const handleTemplateDialogClose = () => {
  templateDialogVisible.value = false
}

// 处理添加模板
const handleAddTemplate = () => {
  if (!currentPatient.value) {
    ElMessage.warning('请先选择患者')
    return
  }

  if (!infoTabsRef.value) {
    ElMessage.warning('请先切换到诊断报告页面')
    return
  }

  // 调用InfoTabs的保存为模板功能
  infoTabsRef.value.handleSaveAsTemplate()
}

// 根据角色获取空状态描述
const getEmptyDescription = () => {
  if (currentRole.value === 'diagnoser') {
    return '请选择患者进行诊断'
  } else {
    return '请选择患者进行审核'
  }
}

// 监听角色变化更新权限
watch(currentRole, (newRole) => {
  if (newRole === 'diagnoser') {
    userPermissions.value = {
      canDiagnose: true,
      canAudit: false,
      canEdit: true,
      canDelete: false
    }
  } else {
    userPermissions.value = {
      canDiagnose: false,
      canAudit: true,
      canEdit: false,
      canDelete: false
    }
  }
})

// 组件挂载时初始化
onMounted(() => {
  // 从localStorage恢复角色选择
  const savedRole = localStorage.getItem('diagnosis-workspace-role')
  if (savedRole && ['diagnoser', 'auditor'].includes(savedRole)) {
    currentRole.value = savedRole
  }

  handleRoleChange(currentRole.value)
})

// 保存角色选择到localStorage
watch(currentRole, (newRole) => {
  localStorage.setItem('diagnosis-workspace-role', newRole)
})

// 暴露方法给父组件
defineExpose({
  refresh: getPatientList,
  selectPatient: handlePatientSelect
})
</script>

<style scoped>
.diagnosis-workspace {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.top-search-bar {
  background: white;
  padding: 8px 12px;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.left-panel {
  width: 350px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 85vh;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e4e7ed;
  min-height: 40px;
}

.role-switch-compact .el-radio-group {
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.role-switch-compact .el-radio-button__inner {
  padding: 6px 12px;
  font-size: 12px;
}

.quick-filters-compact {
  position: relative;
}

.filter-badge-compact {
  position: absolute;
  top: -8px;
  right: -8px;
}

.filter-badge-compact :deep(.el-badge__content) {
  background-color: #f56c6c;
  border: 1px solid #f56c6c;
  font-size: 11px;
  height: 16px;
  line-height: 14px;
  min-width: 16px;
  padding: 0 4px;
}

.menu-badge {
  float: right;
  margin-left: 8px;
}

.menu-badge :deep(.el-badge__content) {
  background-color: #909399;
  border: 1px solid #909399;
  font-size: 11px;
  height: 16px;
  line-height: 14px;
  min-width: 16px;
  padding: 0 4px;
  position: static;
  transform: none;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 6px;
  padding: 6px;
}

.center-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 85vh;
}

.right-panel {
  width: 280px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 85vh;
}

.no-patient-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计优化 */
@media (max-width: 1400px) {
  .right-panel {
    width: 250px;
  }

  .panel-header {
    flex-direction: column;
    gap: 8px;
    min-height: auto;
    padding: 10px 12px;
  }

  .role-switch-compact,
  .quick-filters-compact {
    width: 100%;
  }
}

@media (max-width: 1200px) {
  .left-panel {
    width: 300px;
  }
  .right-panel {
    width: 220px;
  }
}

@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
    gap: 8px;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
    height: 200px;
  }
  
  .center-panel {
    flex: 1;
    min-height: 400px;
  }

  .panel-header {
    flex-direction: row;
    justify-content: space-between;
    padding: 8px 12px;
    min-height: 50px;
  }
}

/* 场景菜单项样式 */
.scenario-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.scenario-menu-item span {
  flex: 1;
  text-align: left;
}

.menu-badge {
  margin-left: auto;
}
</style>
