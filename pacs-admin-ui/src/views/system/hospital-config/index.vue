<template>
  <div class="app-container">
    <el-card header="医院信息配置">
      <el-form ref="configFormRef" :model="configForm" :rules="configRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="医院名称" prop="hospitalName">
              <el-input v-model="configForm.hospitalName" placeholder="请输入医院名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报告标题" prop="reportTitle">
              <el-input v-model="configForm.reportTitle" placeholder="请输入报告标题" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告生成URL" prop="reportGenerateUrl">
              <el-input v-model="configForm.reportGenerateUrl" placeholder="请输入报告生成URL" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="医院Logo" prop="hospitalLogoUrl">
          <div class="logo-upload-container">
            <config-image-upload 
              v-model="configForm.hospitalLogoUrl"
              config-key="hospital.logo.url"
            />
            <div class="upload-tip">
              <p>建议上传尺寸：200x80像素</p>
              <p>支持格式：JPG、PNG</p>
              <p>文件大小：不超过5MB</p>
            </div>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="loading">保存配置</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="info" @click="previewReport">预览报告</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 报告预览对话框 -->
    <el-dialog v-model="previewVisible" title="报告预览" width="80%" append-to-body>
      <div class="preview-container">
        <div class="report-header">
          <img v-if="configForm.hospitalLogoUrl" :src="configForm.hospitalLogoUrl" class="hospital-logo" alt="医院Logo" />
          <h2>{{ configForm.reportTitle || '医学影像诊断报告' }}</h2>
          <p>{{ configForm.hospitalName || '医院名称' }}</p>
        </div>
        <div class="report-content">
          <p>这是报告预览示例，实际报告将使用配置的Logo和信息。</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="HospitalConfig">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { listConfig, updateConfig } from "@/api/system/config";
import ConfigImageUpload from "@/components/ConfigImageUpload";

const loading = ref(false);
const previewVisible = ref(false);

// 表单数据
const configForm = reactive({
  hospitalName: '',
  hospitalLogoUrl: '',
  reportTitle: '',
  reportGenerateUrl: ''
});

// 表单验证规则
const configRules = {
  hospitalName: [
    { required: true, message: '请输入医院名称', trigger: 'blur' }
  ],
  reportTitle: [
    { required: true, message: '请输入报告标题', trigger: 'blur' }
  ],
  reportGenerateUrl: [
    { required: true, message: '请输入报告生成URL', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ]
};

// 配置项映射
const configKeyMap = {
  hospitalName: 'hospital.name',
  hospitalLogoUrl: 'hospital.logo.url',
  reportTitle: 'report.title',
  reportGenerateUrl: 'report.generate.url'
};

/** 加载配置数据 */
async function loadConfig() {
  try {
    loading.value = true;
    const response = await listConfig({
      pageNum: 1,
      pageSize: 100,
      configKey: Object.values(configKeyMap).join(',')
    });
    
    if (response.rows) {
      response.rows.forEach(config => {
        const key = Object.keys(configKeyMap).find(k => configKeyMap[k] === config.configKey);
        if (key) {
          configForm[key] = config.configValue || '';
        }
      });
    }
  } catch (error) {
    ElMessage.error('加载配置失败：' + error.message);
  } finally {
    loading.value = false;
  }
}

/** 提交表单 */
async function submitForm() {
  try {
    loading.value = true;
    
    // 获取所有配置项
    const response = await listConfig({
      pageNum: 1,
      pageSize: 100,
      configKey: Object.values(configKeyMap).join(',')
    });
    
    const configs = response.rows || [];
    const updatePromises = [];
    
    // 更新每个配置项
    Object.keys(configForm).forEach(key => {
      const configKey = configKeyMap[key];
      const config = configs.find(c => c.configKey === configKey);
      
      if (config) {
        config.configValue = configForm[key];
        updatePromises.push(updateConfig(config));
      }
    });
    
    await Promise.all(updatePromises);
    ElMessage.success('配置保存成功');
    
  } catch (error) {
    ElMessage.error('配置保存失败：' + error.message);
  } finally {
    loading.value = false;
  }
}

/** 重置表单 */
function resetForm() {
  loadConfig();
}

/** 预览报告 */
function previewReport() {
  previewVisible.value = true;
}

onMounted(() => {
  loadConfig();
});
</script>

<style scoped>
.logo-upload-container {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.upload-tip {
  color: #666;
  font-size: 12px;
  line-height: 1.5;
}

.upload-tip p {
  margin: 0 0 4px 0;
}

.preview-container {
  text-align: center;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
}

.report-header {
  margin-bottom: 30px;
}

.hospital-logo {
  max-width: 200px;
  max-height: 80px;
  margin-bottom: 10px;
}

.report-header h2 {
  margin: 10px 0;
  color: #333;
}

.report-header p {
  margin: 5px 0;
  color: #666;
}

.report-content {
  text-align: left;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 4px;
}
</style>
