import request from '@/utils/request'

/**
 * 首页仪表盘相关API
 */

// 获取数据同步统计
export function getSyncStatistics(params) {
  return request({
    url: '/datasync/pacs/statistics',
    method: 'get',
    params: params
  })
}

// 获取数据同步状态
export function getSyncStatus() {
  return request({
    url: '/datasync/pacs/status',
    method: 'get'
  })
}

// 获取检查数据统计
export function getStudyStatistics(params) {
  return request({
    url: '/datasync/study/stat',
    method: 'get',
    params: params
  })
}

// 获取诊断状态统计
export function getDiagnosisStatusCount(params) {
  return request({
    url: '/datasync/study/diagnosisStatusCount',
    method: 'get',
    params: params
  })
}

// 获取会诊统计
export function getConsultationStatistics() {
  return request({
    url: '/consultation/request/statistics',
    method: 'get'
  })
}

// 获取PDF生成统计
export function getPdfStatistics() {
  return request({
    url: '/diagnosis/pdf/statistics',
    method: 'get'
  })
}

// 获取未读通知数量
export function getUnreadNotificationCount() {
  return request({
    url: '/consultation/notification/unread-count',
    method: 'get'
  })
}

// 获取最近通知列表
export function getRecentNotifications(params) {
  return request({
    url: '/consultation/notification/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 5,
      ...params
    }
  })
}

// 获取系统概览统计
export function getSystemOverview() {
  return request({
    url: '/datasync/diagnostic/mysql',
    method: 'get'
  })
}

// 获取待办事项列表
export function getTodoList(params) {
  return request({
    url: '/dashboard/todo',
    method: 'get',
    params: params
  })
}

// 获取请求列表
export function getRequestList(params) {
  return request({
    url: '/dashboard/request',
    method: 'get',
    params: params
  })
}

// 获取待办事项统计
export function getTodoCount() {
  return request({
    url: '/dashboard/todo/count',
    method: 'get'
  })
}

// 获取请求统计
export function getRequestCount() {
  return request({
    url: '/dashboard/request/count',
    method: 'get'
  })
}
