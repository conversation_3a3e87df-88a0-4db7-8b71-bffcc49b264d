package com.ruoyi.datasync.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.datasync.domain.PacsSyncStatus;
import com.ruoyi.datasync.mapper.PacsSyncStatusMapper;
import com.ruoyi.datasync.service.PacsDataSyncService;
import com.ruoyi.datasync.service.IPacsPatientStudyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * PACS数据同步Controller
 */
@RestController
@RequestMapping("/datasync/pacs")
public class PacsSyncController extends BaseController {

    @Autowired
    private PacsDataSyncService pacsDataSyncService;

    @Autowired
    private PacsSyncStatusMapper pacsSyncStatusMapper;

    @Autowired
    private IPacsPatientStudyService pacsPatientStudyService;

    /**
     * 同步检查数据
     */
    @PreAuthorize("@ss.hasPermi('datasync:pacs:sync')")
    @Log(title = "PACS检查数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/syncCloud")
    public AjaxResult syncPacsCloudData() {
        int count = pacsDataSyncService.syncPacsPatientStudy();
        return AjaxResult.success("同步成功，共同步 " + count + " 条记录");
    }

    /**
     * 同步报告数据
     */
    @PreAuthorize("@ss.hasPermi('datasync:pacs:sync')")
    @Log(title = "PACS报告数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/syncReport")
    public AjaxResult syncPacsReportData() {
        int count = pacsDataSyncService.syncPacsReportData();
        return AjaxResult.success("同步成功，共同步 " + count + " 条记录");
    }

    /**
     * 同步所有数据
     */
    @PreAuthorize("@ss.hasPermi('datasync:pacs:sync')")
    @Log(title = "PACS全量数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/syncAll")
    public AjaxResult syncAllPacsData() {
        boolean success = pacsDataSyncService.syncAllPacsData();
        if (success) {
            return AjaxResult.success("所有数据同步成功");
        } else {
            return AjaxResult.error("数据同步失败，请查看日志");
        }
    }

    /**
     * 获取同步状态
     */
    //@PreAuthorize("@ss.hasPermi('datasync:pacs:query')")
    @GetMapping("/status")
    public AjaxResult getSyncStatus() {
        String status = pacsDataSyncService.getSyncStatus();
        return AjaxResult.success("查询成功", status);
    }
    
    /**
     * 获取同步统计数据
     * 支持按日期范围统计pacs_patient_study表的真实数据
     */
    //@PreAuthorize("@ss.hasPermi('datasync:pacs:query')")
    @GetMapping("/statistics")
    public AjaxResult getSyncStatistics(
            @RequestParam(value = "start", required = false) String start,
            @RequestParam(value = "end", required = false) String end) {
        try {
            Map<String, Object> result = new HashMap<>();
            // 获取真实的检查数据统计（按日期范围）
            Map<String, Object> studyStats = pacsPatientStudyService.stat(start, end);
            result.put("studyData", studyStats);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取同步统计数据失败", e);
            return AjaxResult.error("获取统计数据失败: " + e.getMessage());
        }
    }
} 