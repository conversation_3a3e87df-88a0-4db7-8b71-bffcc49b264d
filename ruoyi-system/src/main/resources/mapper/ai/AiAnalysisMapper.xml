<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ai.mapper.AiAnalysisMapper">
    
    <resultMap type="com.ruoyi.ai.domain.AiAnalysis" id="AiAnalysisResult">
        <result property="id"    column="id"    />
        <result property="studyInstanceUid"    column="study_instance_uid"    />
        <result property="studyId"    column="study_id"    />
        <result property="status"    column="status"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="resultJson"    column="result_json"    />
        <result property="diagnosticSuggestions"    column="diagnostic_suggestions"    />
        <result property="processStartTime"    column="process_start_time"    />
        <result property="processEndTime"    column="process_end_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectAiAnalysisVo">
        select id, study_instance_uid, study_id, status, error_message, result_json, diagnostic_suggestions, process_start_time, process_end_time, create_by, create_time, update_by, update_time, remark, del_flag from ai_analysis
    </sql>

    <select id="selectAiAnalysisList" parameterType="com.ruoyi.ai.domain.AiAnalysis" resultMap="AiAnalysisResult">
        <include refid="selectAiAnalysisVo"/>
        <where>  
            <if test="studyInstanceUid != null  and studyInstanceUid != ''"> and study_instance_uid = #{studyInstanceUid}</if>
            <if test="studyId != null "> and study_id = #{studyId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="processStartTime != null "> and process_start_time = #{processStartTime}</if>
            <if test="processEndTime != null "> and process_end_time = #{processEndTime}</if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>
    
    <select id="selectAiAnalysisById" parameterType="Long" resultMap="AiAnalysisResult">
        <include refid="selectAiAnalysisVo"/>
        where id = #{id} and del_flag = '0'
    </select>
    
    <select id="selectAiAnalysisByStudyInstanceUid" parameterType="String" resultMap="AiAnalysisResult">
        <include refid="selectAiAnalysisVo"/>
        where study_instance_uid = #{studyInstanceUid} and del_flag = '0'
        order by create_time desc
    </select>
        
    <insert id="insertAiAnalysis" parameterType="com.ruoyi.ai.domain.AiAnalysis" useGeneratedKeys="true" keyProperty="id">
        insert into ai_analysis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studyInstanceUid != null and studyInstanceUid != ''">study_instance_uid,</if>
            <if test="studyId != null">study_id,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="resultJson != null">result_json,</if>
            <if test="diagnosticSuggestions != null">diagnostic_suggestions,</if>
            <if test="processStartTime != null">process_start_time,</if>
            <if test="processEndTime != null">process_end_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studyInstanceUid != null and studyInstanceUid != ''">#{studyInstanceUid},</if>
            <if test="studyId != null">#{studyId},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="resultJson != null">#{resultJson},</if>
            <if test="diagnosticSuggestions != null">#{diagnosticSuggestions},</if>
            <if test="processStartTime != null">#{processStartTime},</if>
            <if test="processEndTime != null">#{processEndTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateAiAnalysis" parameterType="com.ruoyi.ai.domain.AiAnalysis">
        update ai_analysis
        <trim prefix="SET" suffixOverrides=",">
            <if test="studyInstanceUid != null and studyInstanceUid != ''">study_instance_uid = #{studyInstanceUid},</if>
            <if test="studyId != null">study_id = #{studyId},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="resultJson != null">result_json = #{resultJson},</if>
            <if test="diagnosticSuggestions != null">diagnostic_suggestions = #{diagnosticSuggestions},</if>
            <if test="processStartTime != null">process_start_time = #{processStartTime},</if>
            <if test="processEndTime != null">process_end_time = #{processEndTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAiAnalysisById" parameterType="Long">
        update ai_analysis set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteAiAnalysisByIds" parameterType="String">
        update ai_analysis set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
