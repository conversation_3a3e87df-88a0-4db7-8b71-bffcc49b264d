<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ReportShareAccessMapper">
    
    <resultMap type="ReportShareAccess" id="ReportShareAccessResult">
        <result property="id"    column="id"    />
        <result property="shareCode"    column="share_code"    />
        <result property="accessIp"    column="access_ip"    />
        <result property="accessUserAgent"    column="access_user_agent"    />
        <result property="accessTime"    column="access_time"    />
        <result property="accessResult"    column="access_result"    />
    </resultMap>

    <sql id="selectReportShareAccessVo">
        select id, share_code, access_ip, access_user_agent, access_time, access_result from report_share_access
    </sql>

    <select id="selectReportShareAccessList" parameterType="ReportShareAccess" resultMap="ReportShareAccessResult">
        <include refid="selectReportShareAccessVo"/>
        <where>  
            <if test="shareCode != null  and shareCode != ''"> and share_code = #{shareCode}</if>
            <if test="accessIp != null  and accessIp != ''"> and access_ip = #{accessIp}</if>
            <if test="accessResult != null  and accessResult != ''"> and access_result = #{accessResult}</if>
            <if test="accessTime != null "> and access_time = #{accessTime}</if>
        </where>
        order by access_time desc
    </select>
    
    <select id="selectReportShareAccessById" parameterType="Long" resultMap="ReportShareAccessResult">
        <include refid="selectReportShareAccessVo"/>
        where id = #{id}
    </select>

    <select id="selectAccessByShareCode" parameterType="String" resultMap="ReportShareAccessResult">
        <include refid="selectReportShareAccessVo"/>
        where share_code = #{shareCode}
        order by access_time desc
    </select>
        
    <insert id="insertReportShareAccess" parameterType="ReportShareAccess" useGeneratedKeys="true" keyProperty="id">
        insert into report_share_access
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shareCode != null and shareCode != ''">share_code,</if>
            <if test="accessIp != null">access_ip,</if>
            <if test="accessUserAgent != null">access_user_agent,</if>
            <if test="accessTime != null">access_time,</if>
            <if test="accessResult != null">access_result,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shareCode != null and shareCode != ''">#{shareCode},</if>
            <if test="accessIp != null">#{accessIp},</if>
            <if test="accessUserAgent != null">#{accessUserAgent},</if>
            <if test="accessTime != null">#{accessTime},</if>
            <if test="accessResult != null">#{accessResult},</if>
         </trim>
    </insert>

    <update id="updateReportShareAccess" parameterType="ReportShareAccess">
        update report_share_access
        <trim prefix="SET" suffixOverrides=",">
            <if test="shareCode != null and shareCode != ''">share_code = #{shareCode},</if>
            <if test="accessIp != null">access_ip = #{accessIp},</if>
            <if test="accessUserAgent != null">access_user_agent = #{accessUserAgent},</if>
            <if test="accessTime != null">access_time = #{accessTime},</if>
            <if test="accessResult != null">access_result = #{accessResult},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteReportShareAccessById" parameterType="Long">
        delete from report_share_access where id = #{id}
    </delete>

    <delete id="deleteReportShareAccessByIds" parameterType="String">
        delete from report_share_access where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="cleanOldAccessRecords" parameterType="int">
        delete from report_share_access where access_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>
</mapper>
