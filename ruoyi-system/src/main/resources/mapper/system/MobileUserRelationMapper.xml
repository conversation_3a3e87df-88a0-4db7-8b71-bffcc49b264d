<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MobileUserRelationMapper">
    
    <resultMap type="MobileUserRelation" id="MobileUserRelationResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userIdNo"    column="user_id_no"    />
        <result property="friendIdNo"    column="friend_id_no"    />
        <result property="friendName"    column="friend_name"    />
        <result property="friendPhone"    column="friend_phone"    />
        <result property="relationType"    column="relation_type"    />
        <result property="status"    column="status"    />
        <result property="verifiedTime"    column="verified_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMobileUserRelationVo">
        select id, user_id, user_id_no, friend_id_no, friend_name, friend_phone, relation_type, status, verified_time, create_time, create_by, update_time, update_by, remark from mobile_user_relation
    </sql>

    <select id="selectMobileUserRelationList" parameterType="MobileUserRelation" resultMap="MobileUserRelationResult">
        <include refid="selectMobileUserRelationVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userIdNo != null  and userIdNo != ''"> and user_id_no = #{userIdNo}</if>
            <if test="friendIdNo != null  and friendIdNo != ''"> and friend_id_no = #{friendIdNo}</if>
            <if test="friendName != null  and friendName != ''"> and friend_name like concat('%', #{friendName}, '%')</if>
            <if test="friendPhone != null  and friendPhone != ''"> and friend_phone = #{friendPhone}</if>
            <if test="relationType != null  and relationType != ''"> and relation_type = #{relationType}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="verifiedTime != null "> and verified_time = #{verifiedTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectMobileUserRelationById" parameterType="Long" resultMap="MobileUserRelationResult">
        <include refid="selectMobileUserRelationVo"/>
        where id = #{id}
    </select>

    <select id="selectRelationsByUserId" parameterType="Long" resultMap="MobileUserRelationResult">
        <include refid="selectMobileUserRelationVo"/>
        where user_id = #{userId} and status = 1
        order by create_time desc
    </select>

    <select id="selectRelationsByUserIdNo" parameterType="String" resultMap="MobileUserRelationResult">
        <include refid="selectMobileUserRelationVo"/>
        where user_id_no = #{userIdNo} and status = 1
        order by create_time desc
    </select>

    <select id="selectRelationByUserAndFriend" resultMap="MobileUserRelationResult">
        <include refid="selectMobileUserRelationVo"/>
        where user_id = #{userId} and friend_id_no = #{friendIdNo}
        limit 1
    </select>

    <select id="selectRelationsByFriendIdNo" parameterType="String" resultMap="MobileUserRelationResult">
        <include refid="selectMobileUserRelationVo"/>
        where friend_id_no = #{friendIdNo} and status = 1
        order by create_time desc
    </select>
        
    <insert id="insertMobileUserRelation" parameterType="MobileUserRelation" useGeneratedKeys="true" keyProperty="id">
        insert into mobile_user_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userIdNo != null and userIdNo != ''">user_id_no,</if>
            <if test="friendIdNo != null and friendIdNo != ''">friend_id_no,</if>
            <if test="friendName != null">friend_name,</if>
            <if test="friendPhone != null">friend_phone,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="status != null">status,</if>
            <if test="verifiedTime != null">verified_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userIdNo != null and userIdNo != ''">#{userIdNo},</if>
            <if test="friendIdNo != null and friendIdNo != ''">#{friendIdNo},</if>
            <if test="friendName != null">#{friendName},</if>
            <if test="friendPhone != null">#{friendPhone},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="status != null">#{status},</if>
            <if test="verifiedTime != null">#{verifiedTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMobileUserRelation" parameterType="MobileUserRelation">
        update mobile_user_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userIdNo != null and userIdNo != ''">user_id_no = #{userIdNo},</if>
            <if test="friendIdNo != null and friendIdNo != ''">friend_id_no = #{friendIdNo},</if>
            <if test="friendName != null">friend_name = #{friendName},</if>
            <if test="friendPhone != null">friend_phone = #{friendPhone},</if>
            <if test="relationType != null">relation_type = #{relationType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="verifiedTime != null">verified_time = #{verifiedTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMobileUserRelationById" parameterType="Long">
        delete from mobile_user_relation where id = #{id}
    </delete>

    <delete id="deleteMobileUserRelationByIds" parameterType="String">
        delete from mobile_user_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteRelationByUserAndFriend">
        delete from mobile_user_relation where user_id = #{userId} and friend_id_no = #{friendIdNo}
    </delete>
</mapper>
