<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ReportShareMapper">
    
    <resultMap type="ReportShare" id="ReportShareResult">
        <result property="id"    column="id"    />
        <result property="shareCode"    column="share_code"    />
        <result property="reportId"    column="report_id"    />
        <result property="examCode"    column="exam_code"    />
        <result property="patientName"    column="patient_name"    />
        <result property="patientIdNo"    column="patient_id_no"    />
        <result property="sharerPhone"    column="sharer_phone"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="accessCount"    column="access_count"    />
        <result property="maxAccessCount"    column="max_access_count"    />
        <result property="password"    column="password"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectReportShareVo">
        select id, share_code, report_id, exam_code, patient_name, patient_id_no, sharer_phone, expire_time, access_count, max_access_count, password, status, create_time, create_by, update_time, update_by, remark from report_share
    </sql>

    <select id="selectReportShareList" parameterType="ReportShare" resultMap="ReportShareResult">
        <include refid="selectReportShareVo"/>
        <where>  
            <if test="shareCode != null  and shareCode != ''"> and share_code = #{shareCode}</if>
            <if test="reportId != null "> and report_id = #{reportId}</if>
            <if test="examCode != null  and examCode != ''"> and exam_code = #{examCode}</if>
            <if test="patientName != null  and patientName != ''"> and patient_name like concat('%', #{patientName}, '%')</if>
            <if test="patientIdNo != null  and patientIdNo != ''"> and patient_id_no = #{patientIdNo}</if>
            <if test="sharerPhone != null  and sharerPhone != ''"> and sharer_phone = #{sharerPhone}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectReportShareById" parameterType="Long" resultMap="ReportShareResult">
        <include refid="selectReportShareVo"/>
        where id = #{id}
    </select>

    <select id="selectReportShareByCode" parameterType="String" resultMap="ReportShareResult">
        <include refid="selectReportShareVo"/>
        where share_code = #{shareCode}
        limit 1
    </select>

    <select id="selectReportShareByReportId" parameterType="Long" resultMap="ReportShareResult">
        <include refid="selectReportShareVo"/>
        where report_id = #{reportId} and status = 1
        order by create_time desc
    </select>

    <select id="selectReportShareByExamCode" parameterType="String" resultMap="ReportShareResult">
        <include refid="selectReportShareVo"/>
        where exam_code = #{examCode} and status = 1
        order by create_time desc
    </select>
        
    <insert id="insertReportShare" parameterType="ReportShare" useGeneratedKeys="true" keyProperty="id">
        insert into report_share
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shareCode != null and shareCode != ''">share_code,</if>
            <if test="reportId != null">report_id,</if>
            <if test="examCode != null">exam_code,</if>
            <if test="patientName != null">patient_name,</if>
            <if test="patientIdNo != null">patient_id_no,</if>
            <if test="sharerPhone != null">sharer_phone,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="accessCount != null">access_count,</if>
            <if test="maxAccessCount != null">max_access_count,</if>
            <if test="password != null">password,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shareCode != null and shareCode != ''">#{shareCode},</if>
            <if test="reportId != null">#{reportId},</if>
            <if test="examCode != null">#{examCode},</if>
            <if test="patientName != null">#{patientName},</if>
            <if test="patientIdNo != null">#{patientIdNo},</if>
            <if test="sharerPhone != null">#{sharerPhone},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="accessCount != null">#{accessCount},</if>
            <if test="maxAccessCount != null">#{maxAccessCount},</if>
            <if test="password != null">#{password},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateReportShare" parameterType="ReportShare">
        update report_share
        <trim prefix="SET" suffixOverrides=",">
            <if test="shareCode != null and shareCode != ''">share_code = #{shareCode},</if>
            <if test="reportId != null">report_id = #{reportId},</if>
            <if test="examCode != null">exam_code = #{examCode},</if>
            <if test="patientName != null">patient_name = #{patientName},</if>
            <if test="patientIdNo != null">patient_id_no = #{patientIdNo},</if>
            <if test="sharerPhone != null">sharer_phone = #{sharerPhone},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="accessCount != null">access_count = #{accessCount},</if>
            <if test="maxAccessCount != null">max_access_count = #{maxAccessCount},</if>
            <if test="password != null">password = #{password},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteReportShareById" parameterType="Long">
        delete from report_share where id = #{id}
    </delete>

    <delete id="deleteReportShareByIds" parameterType="String">
        delete from report_share where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="incrementAccessCount" parameterType="String">
        update report_share set access_count = access_count + 1 where share_code = #{shareCode}
    </update>

    <delete id="cleanExpiredShares">
        delete from report_share where expire_time &lt; NOW() or status = 0
    </delete>
</mapper>
