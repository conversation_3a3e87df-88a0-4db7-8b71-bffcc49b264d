<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MobileUserMapper">

    <resultMap type="MobileUser" id="MobileUserResult">
        <result property="id"    column="id"    />
        <result property="username"    column="username"    />
        <result property="password"    column="password"    />
        <result property="email"    column="email"    />
        <result property="phone"    column="phone"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectMobileUserVo">
        select id, username, password, email, phone, status, create_time, create_by, update_time, update_by from mobile_user
    </sql>

    <select id="selectMobileUserList" parameterType="MobileUser" resultMap="MobileUserResult">
        <include refid="selectMobileUserVo"/>
        <where>
            <if test="username != null  and username != ''"> and username like concat('%', #{username}, '%')</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectMobileUserById" parameterType="Long" resultMap="MobileUserResult">
        <include refid="selectMobileUserVo"/>
        where id = #{id}
    </select>

    <select id="selectMobileUserByUsername" parameterType="String" resultMap="MobileUserResult">
        <include refid="selectMobileUserVo"/>
        where username = #{username}
    </select>
    <select id="selectMobileUserByPhone" parameterType="String" resultMap="MobileUserResult">
        <include refid="selectMobileUserVo"/>
        where username = #{phone}
    </select>

    <insert id="insertMobileUser" parameterType="MobileUser" useGeneratedKeys="true" keyProperty="id">
        insert into mobile_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="username != null and username != ''">username,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="email != null">email,</if>
            <if test="phone != null">phone,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="username != null and username != ''">#{username},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="email != null">#{email},</if>
            <if test="phone != null">#{phone},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateMobileUser" parameterType="MobileUser">
        update mobile_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="username != null and username != ''">username = #{username},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMobileUserById" parameterType="Long">
        delete from mobile_user where id = #{id}
    </delete>

    <delete id="deleteMobileUserByIds" parameterType="String">
        delete from mobile_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
