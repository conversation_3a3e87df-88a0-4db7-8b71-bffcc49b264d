package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.ReportShare;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检查报告分享Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface ReportShareMapper
{
    /**
     * 查询检查报告分享
     *
     * @param id 检查报告分享主键
     * @return 检查报告分享
     */
    public ReportShare selectReportShareById(Long id);

    /**
     * 查询检查报告分享列表
     *
     * @param reportShare 检查报告分享
     * @return 检查报告分享集合
     */
    public List<ReportShare> selectReportShareList(ReportShare reportShare);

    /**
     * 根据分享码查询分享记录
     *
     * @param shareCode 分享码
     * @return 分享记录
     */
    public ReportShare selectReportShareByCode(String shareCode);

    /**
     * 根据报告ID查询分享记录
     *
     * @param reportId 报告ID
     * @return 分享记录列表
     */
    public List<ReportShare> selectReportShareByReportId(Long reportId);

    /**
     * 根据检查号查询分享记录
     *
     * @param examCode 检查号
     * @return 分享记录列表
     */
    public List<ReportShare> selectReportShareByExamCode(String examCode);

    /**
     * 新增检查报告分享
     *
     * @param reportShare 检查报告分享
     * @return 结果
     */
    public int insertReportShare(ReportShare reportShare);

    /**
     * 修改检查报告分享
     *
     * @param reportShare 检查报告分享
     * @return 结果
     */
    public int updateReportShare(ReportShare reportShare);

    /**
     * 删除检查报告分享
     *
     * @param id 检查报告分享主键
     * @return 结果
     */
    public int deleteReportShareById(Long id);

    /**
     * 批量删除检查报告分享
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteReportShareByIds(Long[] ids);

    /**
     * 更新访问次数
     *
     * @param shareCode 分享码
     * @return 结果
     */
    public int incrementAccessCount(String shareCode);

    /**
     * 清理过期的分享记录
     *
     * @return 清理的记录数
     */
    public int cleanExpiredShares();
}
