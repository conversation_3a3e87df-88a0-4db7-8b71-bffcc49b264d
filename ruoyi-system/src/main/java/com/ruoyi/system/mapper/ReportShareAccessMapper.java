package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.ReportShareAccess;

import java.util.List;

/**
 * 报告分享访问记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface ReportShareAccessMapper
{
    /**
     * 查询报告分享访问记录
     *
     * @param id 报告分享访问记录主键
     * @return 报告分享访问记录
     */
    public ReportShareAccess selectReportShareAccessById(Long id);

    /**
     * 查询报告分享访问记录列表
     *
     * @param reportShareAccess 报告分享访问记录
     * @return 报告分享访问记录集合
     */
    public List<ReportShareAccess> selectReportShareAccessList(ReportShareAccess reportShareAccess);

    /**
     * 根据分享码查询访问记录
     *
     * @param shareCode 分享码
     * @return 访问记录列表
     */
    public List<ReportShareAccess> selectAccessByShareCode(String shareCode);

    /**
     * 新增报告分享访问记录
     *
     * @param reportShareAccess 报告分享访问记录
     * @return 结果
     */
    public int insertReportShareAccess(ReportShareAccess reportShareAccess);

    /**
     * 修改报告分享访问记录
     *
     * @param reportShareAccess 报告分享访问记录
     * @return 结果
     */
    public int updateReportShareAccess(ReportShareAccess reportShareAccess);

    /**
     * 删除报告分享访问记录
     *
     * @param id 报告分享访问记录主键
     * @return 结果
     */
    public int deleteReportShareAccessById(Long id);

    /**
     * 批量删除报告分享访问记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteReportShareAccessByIds(Long[] ids);

    /**
     * 清理旧的访问记录
     *
     * @param days 保留天数
     * @return 清理的记录数
     */
    public int cleanOldAccessRecords(int days);
}
