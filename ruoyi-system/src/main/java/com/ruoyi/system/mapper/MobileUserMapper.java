package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.MobileUser;

/**
 * 移动端用户Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
public interface MobileUserMapper
{
    /**
     * 查询移动端用户
     *
     * @param id 移动端用户主键
     * @return 移动端用户
     */
    public MobileUser selectMobileUserById(Long id);

    /**
     * 根据用户名查询移动端用户
     *
     * @param username 用户名
     * @return 移动端用户
     */
    public MobileUser selectMobileUserByUsername(String username);

    /**
     * 查询移动端用户列表
     *
     * @param mobileUser 移动端用户
     * @return 移动端用户集合
     */
    public List<MobileUser> selectMobileUserList(MobileUser mobileUser);

    /**
     * 根据手机号查询移动端用户
     *
     * @param phone 手机号
     * @return 移动端用户
     */
    public MobileUser selectMobileUserByPhone(String phone);

    /**
     * 新增移动端用户
     *
     * @param mobileUser 移动端用户
     * @return 结果
     */
    public int insertMobileUser(MobileUser mobileUser);

    /**
     * 修改移动端用户
     *
     * @param mobileUser 移动端用户
     * @return 结果
     */
    public int updateMobileUser(MobileUser mobileUser);

    /**
     * 删除移动端用户
     *
     * @param id 移动端用户主键
     * @return 结果
     */
    public int deleteMobileUserById(Long id);

    /**
     * 批量删除移动端用户
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMobileUserByIds(Long[] ids);
}
