package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 报告分享访问记录对象 report_share_access
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReportShareAccess extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 分享码 */
    @Excel(name = "分享码")
    private String shareCode;

    /** 访问IP */
    @Excel(name = "访问IP")
    private String accessIp;

    /** 访问用户代理 */
    @Excel(name = "访问用户代理")
    private String accessUserAgent;

    /** 访问时间 */
    @Excel(name = "访问时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date accessTime;

    /** 访问结果(success-成功,expired-过期,forbidden-禁止) */
    @Excel(name = "访问结果", readConverterExp = "success=成功,expired=过期,forbidden=禁止")
    private String accessResult;

    public ReportShareAccess() {
    }

    public ReportShareAccess(String shareCode, String accessIp, String accessUserAgent, String accessResult) {
        this.shareCode = shareCode;
        this.accessIp = accessIp;
        this.accessUserAgent = accessUserAgent;
        this.accessResult = accessResult;
        this.accessTime = new Date();
    }

    @Override
    public String toString() {
        return "ReportShareAccess{" +
                "id=" + id +
                ", shareCode='" + shareCode + '\'' +
                ", accessIp='" + accessIp + '\'' +
                ", accessUserAgent='" + accessUserAgent + '\'' +
                ", accessTime=" + accessTime +
                ", accessResult='" + accessResult + '\'' +
                '}';
    }
}
