package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 移动端用户亲友关系对象 mobile_user_relation
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MobileUserRelation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 当前用户ID */
    @Excel(name = "当前用户ID")
    private Long userId;

    /** 当前用户身份证号 */
    @Excel(name = "当前用户身份证号")
    private String userIdNo;

    /** 亲友身份证号 */
    @Excel(name = "亲友身份证号")
    private String friendIdNo;

    /** 亲友姓名 */
    @Excel(name = "亲友姓名")
    private String friendName;

    /** 亲友手机号 */
    @Excel(name = "亲友手机号")
    private String friendPhone;

    /** 关系类型(friend-亲友,family-家属) */
    @Excel(name = "关系类型", readConverterExp = "friend=亲友,family=家属")
    private String relationType;

    /** 状态(0-禁用,1-正常) */
    @Excel(name = "状态", readConverterExp = "0=禁用,1=正常")
    private Integer status;

    /** 验证通过时间 */
    @Excel(name = "验证通过时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date verifiedTime;

    public MobileUserRelation() {
    }

    public MobileUserRelation(Long userId, String userIdNo, String friendIdNo, String friendName, String friendPhone) {
        this.userId = userId;
        this.userIdNo = userIdNo;
        this.friendIdNo = friendIdNo;
        this.friendName = friendName;
        this.friendPhone = friendPhone;
        this.relationType = "friend";
        this.status = 1;
        this.verifiedTime = new Date();
    }

    @Override
    public String toString() {
        return "MobileUserRelation{" +
                "id=" + id +
                ", userId=" + userId +
                ", userIdNo='" + userIdNo + '\'' +
                ", friendIdNo='" + friendIdNo + '\'' +
                ", friendName='" + friendName + '\'' +
                ", friendPhone='" + friendPhone + '\'' +
                ", relationType='" + relationType + '\'' +
                ", status=" + status +
                ", verifiedTime=" + verifiedTime +
                '}';
    }
}
