package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 检查报告分享对象 report_share
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReportShare extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 分享码 */
    @Excel(name = "分享码")
    private String shareCode;

    /** 报告ID */
    @Excel(name = "报告ID")
    private Long reportId;

    /** 检查号 */
    @Excel(name = "检查号")
    private String examCode;

    /** 患者姓名 */
    @Excel(name = "患者姓名")
    private String patientName;

    /** 患者身份证号 */
    @Excel(name = "患者身份证号")
    private String patientIdNo;

    /** 分享者手机号 */
    @Excel(name = "分享者手机号")
    private String sharerPhone;

    /** 过期时间 */
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 访问次数 */
    @Excel(name = "访问次数")
    private Integer accessCount;

    /** 最大访问次数 */
    @Excel(name = "最大访问次数")
    private Integer maxAccessCount;

    /** 访问密码(可选) */
    @Excel(name = "访问密码")
    private String password;

    /** 状态(0-禁用,1-正常) */
    @Excel(name = "状态", readConverterExp = "0=禁用,1=正常")
    private Integer status;

    public ReportShare() {
    }

    public ReportShare(String shareCode, Long reportId, String examCode, String patientName, Date expireTime) {
        this.shareCode = shareCode;
        this.reportId = reportId;
        this.examCode = examCode;
        this.patientName = patientName;
        this.expireTime = expireTime;
        this.accessCount = 0;
        this.maxAccessCount = 100;
        this.status = 1;
    }

    /**
     * 检查分享是否有效
     */
    public boolean isValid() {
        if (status == null || status != 1) {
            return false;
        }
        
        if (expireTime == null) {
            return false;
        }
        
        Date now = new Date();
        if (now.after(expireTime)) {
            return false;
        }
        
        if (maxAccessCount != null && accessCount != null && accessCount >= maxAccessCount) {
            return false;
        }
        
        return true;
    }

    /**
     * 增加访问次数
     */
    public void incrementAccessCount() {
        if (accessCount == null) {
            accessCount = 0;
        }
        accessCount++;
    }

    @Override
    public String toString() {
        return "ReportShare{" +
                "id=" + id +
                ", shareCode='" + shareCode + '\'' +
                ", reportId=" + reportId +
                ", examCode='" + examCode + '\'' +
                ", patientName='" + patientName + '\'' +
                ", patientIdNo='" + patientIdNo + '\'' +
                ", sharerPhone='" + sharerPhone + '\'' +
                ", expireTime=" + expireTime +
                ", accessCount=" + accessCount +
                ", maxAccessCount=" + maxAccessCount +
                ", password='" + password + '\'' +
                ", status=" + status +
                '}';
    }
}
