package com.ruoyi.system.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移动端用户对象 mobile_user
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@EqualsAndHashCode
@Data
public class MobileUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户名 */
    @Excel(name = "用户名")
    private String username;

    /** 密码 */
    @Excel(name = "密码")
    private String password;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    private String smsCode;

    /** 操作类型 */
    private String operationType;

    /** 令牌 */
    private String token;

    /** 登录时间 */
    private Long loginTime;

    /** 过期时间 */
    private Long expireTime;

    /** 登录IP地址 */
    private String loginIp;

    /** 登录地点 */
    private String loginLocation;

    /** 浏览器类型 */
    private String browser;

    /** 操作系统 */
    private String os;

    /** 密码不参与序列化 */
    @JSONField(serialize = false)
    public String getPassword() {
        return password;
    }
}
