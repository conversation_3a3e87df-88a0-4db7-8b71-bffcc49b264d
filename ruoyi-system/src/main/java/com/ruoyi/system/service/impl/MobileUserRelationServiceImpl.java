package com.ruoyi.system.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.MobileUserRelation;
import com.ruoyi.system.mapper.MobileUserRelationMapper;
import com.ruoyi.system.service.IMobileUserRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 移动端用户亲友关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Service
public class MobileUserRelationServiceImpl implements IMobileUserRelationService
{
    @Autowired
    private MobileUserRelationMapper mobileUserRelationMapper;

    /**
     * 查询移动端用户亲友关系
     *
     * @param id 移动端用户亲友关系主键
     * @return 移动端用户亲友关系
     */
    @Override
    public MobileUserRelation selectMobileUserRelationById(Long id)
    {
        return mobileUserRelationMapper.selectMobileUserRelationById(id);
    }

    /**
     * 查询移动端用户亲友关系列表
     *
     * @param mobileUserRelation 移动端用户亲友关系
     * @return 移动端用户亲友关系
     */
    @Override
    public List<MobileUserRelation> selectMobileUserRelationList(MobileUserRelation mobileUserRelation)
    {
        return mobileUserRelationMapper.selectMobileUserRelationList(mobileUserRelation);
    }

    /**
     * 根据用户ID查询亲友关系列表
     *
     * @param userId 用户ID
     * @return 亲友关系列表
     */
    @Override
    public List<MobileUserRelation> selectRelationsByUserId(Long userId)
    {
        return mobileUserRelationMapper.selectRelationsByUserId(userId);
    }

    /**
     * 根据用户身份证号查询亲友关系列表
     *
     * @param userIdNo 用户身份证号
     * @return 亲友关系列表
     */
    @Override
    public List<MobileUserRelation> selectRelationsByUserIdNo(String userIdNo)
    {
        return mobileUserRelationMapper.selectRelationsByUserIdNo(userIdNo);
    }

    /**
     * 检查亲友关系是否已存在
     *
     * @param userId 用户ID
     * @param friendIdNo 亲友身份证号
     * @return 关系记录
     */
    @Override
    public MobileUserRelation selectRelationByUserAndFriend(Long userId, String friendIdNo)
    {
        return mobileUserRelationMapper.selectRelationByUserAndFriend(userId, friendIdNo);
    }

    /**
     * 根据亲友身份证号查询所有关联的用户
     *
     * @param friendIdNo 亲友身份证号
     * @return 关系列表
     */
    @Override
    public List<MobileUserRelation> selectRelationsByFriendIdNo(String friendIdNo)
    {
        return mobileUserRelationMapper.selectRelationsByFriendIdNo(friendIdNo);
    }

    /**
     * 新增移动端用户亲友关系
     *
     * @param mobileUserRelation 移动端用户亲友关系
     * @return 结果
     */
    @Override
    public int insertMobileUserRelation(MobileUserRelation mobileUserRelation)
    {
        mobileUserRelation.setCreateTime(DateUtils.getNowDate());
        return mobileUserRelationMapper.insertMobileUserRelation(mobileUserRelation);
    }

    /**
     * 修改移动端用户亲友关系
     *
     * @param mobileUserRelation 移动端用户亲友关系
     * @return 结果
     */
    @Override
    public int updateMobileUserRelation(MobileUserRelation mobileUserRelation)
    {
        mobileUserRelation.setUpdateTime(DateUtils.getNowDate());
        return mobileUserRelationMapper.updateMobileUserRelation(mobileUserRelation);
    }

    /**
     * 批量删除移动端用户亲友关系
     *
     * @param ids 需要删除的移动端用户亲友关系主键
     * @return 结果
     */
    @Override
    public int deleteMobileUserRelationByIds(Long[] ids)
    {
        return mobileUserRelationMapper.deleteMobileUserRelationByIds(ids);
    }

    /**
     * 删除移动端用户亲友关系信息
     *
     * @param id 移动端用户亲友关系主键
     * @return 结果
     */
    @Override
    public int deleteMobileUserRelationById(Long id)
    {
        return mobileUserRelationMapper.deleteMobileUserRelationById(id);
    }

    /**
     * 删除用户的特定亲友关系
     *
     * @param userId 用户ID
     * @param friendIdNo 亲友身份证号
     * @return 结果
     */
    @Override
    public int deleteRelationByUserAndFriend(Long userId, String friendIdNo)
    {
        return mobileUserRelationMapper.deleteRelationByUserAndFriend(userId, friendIdNo);
    }

    /**
     * 创建或更新亲友关系
     *
     * @param userId 用户ID
     * @param userIdNo 用户身份证号
     * @param friendIdNo 亲友身份证号
     * @param friendName 亲友姓名
     * @param friendPhone 亲友手机号
     * @return 结果
     */
    @Override
    public int createOrUpdateRelation(Long userId, String userIdNo, String friendIdNo, String friendName, String friendPhone)
    {
        // 检查是否已存在关系
        MobileUserRelation existingRelation = selectRelationByUserAndFriend(userId, friendIdNo);

        if (existingRelation != null) {
            // 更新现有关系
            existingRelation.setFriendName(friendName);
            existingRelation.setFriendPhone(friendPhone);
            existingRelation.setStatus(1);
            existingRelation.setVerifiedTime(new Date());
            return updateMobileUserRelation(existingRelation);
        } else {
            // 创建新关系
            MobileUserRelation newRelation = new MobileUserRelation(userId, userIdNo, friendIdNo, friendName, friendPhone);
            return insertMobileUserRelation(newRelation);
        }
    }

    /**
     * 获取用户可查看的所有身份证号列表（包括自己和亲友）
     *
     * @param userId 用户ID
     * @param userIdNo 用户身份证号（在我们系统中实际为手机号）
     * @return 身份证号列表
     */
    @Override
    public List<String> getAccessibleIdNos(Long userId, String userIdNo)
    {
        List<String> idNos = new ArrayList<>();

        // 注意：在我们的系统中，用户是通过手机号标识的，可能没有身份证号
        // 所以这里不添加用户自己的身份证号，只添加亲友的身份证号

        // 添加亲友的身份证号
        List<MobileUserRelation> relations = selectRelationsByUserId(userId);
        List<String> friendIdNos = relations.stream()
                .map(MobileUserRelation::getFriendIdNo)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());

        idNos.addAll(friendIdNos);

        return idNos;
    }
}
