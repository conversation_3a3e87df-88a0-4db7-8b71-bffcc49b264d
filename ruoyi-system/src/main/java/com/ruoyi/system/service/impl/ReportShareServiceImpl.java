package com.ruoyi.system.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.domain.ReportShare;
import com.ruoyi.system.domain.ReportShareAccess;
import com.ruoyi.system.mapper.ReportShareAccessMapper;
import com.ruoyi.system.mapper.ReportShareMapper;
import com.ruoyi.system.service.IReportShareService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 检查报告分享Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Service
public class ReportShareServiceImpl implements IReportShareService
{
    private static final Logger log = LoggerFactory.getLogger(ReportShareServiceImpl.class);

    @Autowired
    private ReportShareMapper reportShareMapper;

    @Autowired
    private ReportShareAccessMapper reportShareAccessMapper;

    /**
     * 查询检查报告分享
     *
     * @param id 检查报告分享主键
     * @return 检查报告分享
     */
    @Override
    public ReportShare selectReportShareById(Long id)
    {
        return reportShareMapper.selectReportShareById(id);
    }

    /**
     * 查询检查报告分享列表
     *
     * @param reportShare 检查报告分享
     * @return 检查报告分享
     */
    @Override
    public List<ReportShare> selectReportShareList(ReportShare reportShare)
    {
        return reportShareMapper.selectReportShareList(reportShare);
    }

    /**
     * 根据分享码查询分享记录
     *
     * @param shareCode 分享码
     * @return 分享记录
     */
    @Override
    public ReportShare selectReportShareByCode(String shareCode)
    {
        return reportShareMapper.selectReportShareByCode(shareCode);
    }

    /**
     * 根据报告ID查询分享记录
     *
     * @param reportId 报告ID
     * @return 分享记录列表
     */
    @Override
    public List<ReportShare> selectReportShareByReportId(Long reportId)
    {
        return reportShareMapper.selectReportShareByReportId(reportId);
    }

    /**
     * 根据检查号查询分享记录
     *
     * @param examCode 检查号
     * @return 分享记录列表
     */
    @Override
    public List<ReportShare> selectReportShareByExamCode(String examCode)
    {
        return reportShareMapper.selectReportShareByExamCode(examCode);
    }

    /**
     * 新增检查报告分享
     *
     * @param reportShare 检查报告分享
     * @return 结果
     */
    @Override
    public int insertReportShare(ReportShare reportShare)
    {
        reportShare.setCreateTime(DateUtils.getNowDate());
        return reportShareMapper.insertReportShare(reportShare);
    }

    /**
     * 修改检查报告分享
     *
     * @param reportShare 检查报告分享
     * @return 结果
     */
    @Override
    public int updateReportShare(ReportShare reportShare)
    {
        reportShare.setUpdateTime(DateUtils.getNowDate());
        return reportShareMapper.updateReportShare(reportShare);
    }

    /**
     * 批量删除检查报告分享
     *
     * @param ids 需要删除的检查报告分享主键
     * @return 结果
     */
    @Override
    public int deleteReportShareByIds(Long[] ids)
    {
        return reportShareMapper.deleteReportShareByIds(ids);
    }

    /**
     * 删除检查报告分享信息
     *
     * @param id 检查报告分享主键
     * @return 结果
     */
    @Override
    public int deleteReportShareById(Long id)
    {
        return reportShareMapper.deleteReportShareById(id);
    }

    /**
     * 生成分享码
     *
     * @param reportId 报告ID
     * @param examCode 检查号
     * @param patientName 患者姓名
     * @param expireHours 过期小时数
     * @param sharerPhone 分享者手机号
     * @return 分享码
     */
    @Override
    @Transactional
    public String generateShareCode(Long reportId, String examCode, String patientName, int expireHours, String sharerPhone)
    {
        // 生成唯一分享码
        String shareCode = generateUniqueShareCode();
        
        // 计算过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, expireHours);
        Date expireTime = calendar.getTime();
        
        // 创建分享记录
        ReportShare reportShare = new ReportShare(shareCode, reportId, examCode, patientName, expireTime);
        reportShare.setSharerPhone(sharerPhone);
        
        int result = insertReportShare(reportShare);
        
        if (result > 0) {
            log.info("生成分享码成功: shareCode={}, reportId={}, examCode={}", shareCode, reportId, examCode);
            return shareCode;
        } else {
            log.error("生成分享码失败: reportId={}, examCode={}", reportId, examCode);
            throw new RuntimeException("生成分享码失败");
        }
    }

    /**
     * 验证分享码并获取分享信息
     *
     * @param shareCode 分享码
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 分享信息，如果无效返回null
     */
    @Override
    @Transactional
    public ReportShare validateAndGetShare(String shareCode, String clientIp, String userAgent)
    {
        if (StringUtils.isEmpty(shareCode)) {
            recordAccess(shareCode, clientIp, userAgent, "invalid_code");
            return null;
        }
        
        ReportShare reportShare = selectReportShareByCode(shareCode);
        
        if (reportShare == null) {
            recordAccess(shareCode, clientIp, userAgent, "not_found");
            return null;
        }
        
        if (!reportShare.isValid()) {
            String result = "expired";
            if (reportShare.getStatus() != 1) {
                result = "forbidden";
            } else if (reportShare.getMaxAccessCount() != null && 
                      reportShare.getAccessCount() != null && 
                      reportShare.getAccessCount() >= reportShare.getMaxAccessCount()) {
                result = "max_access_exceeded";
            }
            recordAccess(shareCode, clientIp, userAgent, result);
            return null;
        }
        
        // 增加访问次数
        reportShareMapper.incrementAccessCount(shareCode);
        recordAccess(shareCode, clientIp, userAgent, "success");
        
        log.info("分享码验证成功: shareCode={}, reportId={}", shareCode, reportShare.getReportId());
        return reportShare;
    }

    /**
     * 记录访问
     *
     * @param shareCode 分享码
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @param accessResult 访问结果
     */
    @Override
    public void recordAccess(String shareCode, String clientIp, String userAgent, String accessResult)
    {
        try {
            ReportShareAccess access = new ReportShareAccess(shareCode, clientIp, userAgent, accessResult);
            reportShareAccessMapper.insertReportShareAccess(access);
        } catch (Exception e) {
            log.error("记录分享访问失败: shareCode={}, ip={}, result={}", shareCode, clientIp, accessResult, e);
        }
    }

    /**
     * 清理过期的分享记录
     *
     * @return 清理的记录数
     */
    @Override
    public int cleanExpiredShares()
    {
        return reportShareMapper.cleanExpiredShares();
    }

    /**
     * 生成唯一分享码
     */
    private String generateUniqueShareCode() {
        String shareCode;
        int attempts = 0;
        int maxAttempts = 10;
        
        do {
            shareCode = IdUtils.fastSimpleUUID().substring(0, 16);
            attempts++;
            
            if (attempts > maxAttempts) {
                throw new RuntimeException("生成唯一分享码失败，请重试");
            }
        } while (selectReportShareByCode(shareCode) != null);
        
        return shareCode;
    }
}
