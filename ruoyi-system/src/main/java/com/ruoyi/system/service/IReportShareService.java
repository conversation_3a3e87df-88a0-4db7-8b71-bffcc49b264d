package com.ruoyi.system.service;

import com.ruoyi.system.domain.ReportShare;

import java.util.List;

/**
 * 检查报告分享Service接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface IReportShareService
{
    /**
     * 查询检查报告分享
     *
     * @param id 检查报告分享主键
     * @return 检查报告分享
     */
    public ReportShare selectReportShareById(Long id);

    /**
     * 查询检查报告分享列表
     *
     * @param reportShare 检查报告分享
     * @return 检查报告分享集合
     */
    public List<ReportShare> selectReportShareList(ReportShare reportShare);

    /**
     * 根据分享码查询分享记录
     *
     * @param shareCode 分享码
     * @return 分享记录
     */
    public ReportShare selectReportShareByCode(String shareCode);

    /**
     * 根据报告ID查询分享记录
     *
     * @param reportId 报告ID
     * @return 分享记录列表
     */
    public List<ReportShare> selectReportShareByReportId(Long reportId);

    /**
     * 根据检查号查询分享记录
     *
     * @param examCode 检查号
     * @return 分享记录列表
     */
    public List<ReportShare> selectReportShareByExamCode(String examCode);

    /**
     * 新增检查报告分享
     *
     * @param reportShare 检查报告分享
     * @return 结果
     */
    public int insertReportShare(ReportShare reportShare);

    /**
     * 修改检查报告分享
     *
     * @param reportShare 检查报告分享
     * @return 结果
     */
    public int updateReportShare(ReportShare reportShare);

    /**
     * 批量删除检查报告分享
     *
     * @param ids 需要删除的检查报告分享主键集合
     * @return 结果
     */
    public int deleteReportShareByIds(Long[] ids);

    /**
     * 删除检查报告分享信息
     *
     * @param id 检查报告分享主键
     * @return 结果
     */
    public int deleteReportShareById(Long id);

    /**
     * 生成分享码
     *
     * @param reportId 报告ID
     * @param examCode 检查号
     * @param patientName 患者姓名
     * @param expireHours 过期小时数
     * @param sharerPhone 分享者手机号
     * @return 分享码
     */
    public String generateShareCode(Long reportId, String examCode, String patientName, int expireHours, String sharerPhone);

    /**
     * 验证分享码并获取分享信息
     *
     * @param shareCode 分享码
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 分享信息，如果无效返回null
     */
    public ReportShare validateAndGetShare(String shareCode, String clientIp, String userAgent);

    /**
     * 记录访问
     *
     * @param shareCode 分享码
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @param accessResult 访问结果
     */
    public void recordAccess(String shareCode, String clientIp, String userAgent, String accessResult);

    /**
     * 清理过期的分享记录
     *
     * @return 清理的记录数
     */
    public int cleanExpiredShares();
}
