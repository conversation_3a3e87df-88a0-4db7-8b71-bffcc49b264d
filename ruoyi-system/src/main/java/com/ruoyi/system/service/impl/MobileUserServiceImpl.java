package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.MobileUserMapper;
import com.ruoyi.system.domain.MobileUser;
import com.ruoyi.system.service.IMobileUserService;

/**
 * 移动端用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
public class MobileUserServiceImpl implements IMobileUserService
{
    @Autowired
    private MobileUserMapper mobileUserMapper;

    /**
     * 查询移动端用户
     *
     * @param id 移动端用户主键
     * @return 移动端用户
     */
    @Override
    public MobileUser selectMobileUserById(Long id)
    {
        return mobileUserMapper.selectMobileUserById(id);
    }

    /**
     * 根据用户名查询移动端用户
     *
     * @param username 用户名
     * @return 移动端用户
     */
    @Override
    public MobileUser selectMobileUserByUsername(String username)
    {
        return mobileUserMapper.selectMobileUserByUsername(username);
    }

    /**
     * 查询移动端用户列表
     *
     * @param mobileUser 移动端用户
     * @return 移动端用户
     */
    @Override
    public List<MobileUser> selectMobileUserList(MobileUser mobileUser)
    {
        return mobileUserMapper.selectMobileUserList(mobileUser);
    }

    @Override
    public MobileUser selectMobileUserByPhone(String phone) {
        return mobileUserMapper.selectMobileUserByPhone(phone);
    }

    /**
     * 新增移动端用户
     *
     * @param mobileUser 移动端用户
     * @return 结果
     */
    @Override
    public int insertMobileUser(MobileUser mobileUser)
    {
        mobileUser.setCreateTime(DateUtils.getNowDate());
        return mobileUserMapper.insertMobileUser(mobileUser);
    }

    /**
     * 修改移动端用户
     *
     * @param mobileUser 移动端用户
     * @return 结果
     */
    @Override
    public int updateMobileUser(MobileUser mobileUser)
    {
        mobileUser.setUpdateTime(DateUtils.getNowDate());
        return mobileUserMapper.updateMobileUser(mobileUser);
    }

    /**
     * 批量删除移动端用户
     *
     * @param ids 需要删除的移动端用户主键
     * @return 结果
     */
    @Override
    public int deleteMobileUserByIds(Long[] ids)
    {
        return mobileUserMapper.deleteMobileUserByIds(ids);
    }

    /**
     * 删除移动端用户信息
     *
     * @param id 移动端用户主键
     * @return 结果
     */
    @Override
    public int deleteMobileUserById(Long id)
    {
        return mobileUserMapper.deleteMobileUserById(id);
    }
}
