package com.ruoyi.system.service;

import com.ruoyi.system.domain.MobileUserRelation;

import java.util.List;

/**
 * 移动端用户亲友关系Service接口
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface IMobileUserRelationService
{
    /**
     * 查询移动端用户亲友关系
     *
     * @param id 移动端用户亲友关系主键
     * @return 移动端用户亲友关系
     */
    public MobileUserRelation selectMobileUserRelationById(Long id);

    /**
     * 查询移动端用户亲友关系列表
     *
     * @param mobileUserRelation 移动端用户亲友关系
     * @return 移动端用户亲友关系集合
     */
    public List<MobileUserRelation> selectMobileUserRelationList(MobileUserRelation mobileUserRelation);

    /**
     * 根据用户ID查询亲友关系列表
     *
     * @param userId 用户ID
     * @return 亲友关系列表
     */
    public List<MobileUserRelation> selectRelationsByUserId(Long userId);

    /**
     * 根据用户身份证号查询亲友关系列表
     *
     * @param userIdNo 用户身份证号
     * @return 亲友关系列表
     */
    public List<MobileUserRelation> selectRelationsByUserIdNo(String userIdNo);

    /**
     * 检查亲友关系是否已存在
     *
     * @param userId 用户ID
     * @param friendIdNo 亲友身份证号
     * @return 关系记录
     */
    public MobileUserRelation selectRelationByUserAndFriend(Long userId, String friendIdNo);

    /**
     * 根据亲友身份证号查询所有关联的用户
     *
     * @param friendIdNo 亲友身份证号
     * @return 关系列表
     */
    public List<MobileUserRelation> selectRelationsByFriendIdNo(String friendIdNo);

    /**
     * 新增移动端用户亲友关系
     *
     * @param mobileUserRelation 移动端用户亲友关系
     * @return 结果
     */
    public int insertMobileUserRelation(MobileUserRelation mobileUserRelation);

    /**
     * 修改移动端用户亲友关系
     *
     * @param mobileUserRelation 移动端用户亲友关系
     * @return 结果
     */
    public int updateMobileUserRelation(MobileUserRelation mobileUserRelation);

    /**
     * 批量删除移动端用户亲友关系
     *
     * @param ids 需要删除的移动端用户亲友关系主键集合
     * @return 结果
     */
    public int deleteMobileUserRelationByIds(Long[] ids);

    /**
     * 删除移动端用户亲友关系信息
     *
     * @param id 移动端用户亲友关系主键
     * @return 结果
     */
    public int deleteMobileUserRelationById(Long id);

    /**
     * 删除用户的特定亲友关系
     *
     * @param userId 用户ID
     * @param friendIdNo 亲友身份证号
     * @return 结果
     */
    public int deleteRelationByUserAndFriend(Long userId, String friendIdNo);

    /**
     * 创建或更新亲友关系
     *
     * @param userId 用户ID
     * @param userIdNo 用户身份证号
     * @param friendIdNo 亲友身份证号
     * @param friendName 亲友姓名
     * @param friendPhone 亲友手机号
     * @return 结果
     */
    public int createOrUpdateRelation(Long userId, String userIdNo, String friendIdNo, String friendName, String friendPhone);

    /**
     * 获取用户可查看的所有身份证号列表（包括自己和亲友）
     *
     * @param userId 用户ID
     * @param userIdNo 用户身份证号
     * @return 身份证号列表
     */
    public List<String> getAccessibleIdNos(Long userId, String userIdNo);
}
